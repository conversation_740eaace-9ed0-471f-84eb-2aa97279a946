# Coffee Shop Location Optimization using PuLP (Free Alternative)
# Converted from IBM DOcplex to PuLP for free usage

# Import required libraries
import sys
import json
import requests
import pulp
from geopy.distance import great_circle
import folium

# Install required packages if not available
try:
    import geopy.distance
except:
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", "geopy"])
    import geopy.distance

try:
    import folium
except:
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", "folium"])
    import folium

try:
    import pulp
except:
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", "pulp"])
    import pulp

# Data model classes
class XPoint(object):
    def __init__(self, x, y):
        self.x = x
        self.y = y
    def __str__(self):
        return "P(%g_%g)" % (self.x, self.y)
    def __hash__(self):
        return hash((self.x, self.y))
    def __eq__(self, other):
        return isinstance(other, XPoint) and self.x == other.x and self.y == other.y

class NamedPoint(XPoint):
    def __init__(self, name, x, y):
        XPoint.__init__(self, x, y)
        self.name = name
    def __str__(self):
        return self.name
    def __hash__(self):
        return hash((self.name, self.x, self.y))
    def __eq__(self, other):
        return isinstance(other, NamedPoint) and self.name == other.name and self.x == other.x and self.y == other.y

# Distance computation function
def get_distance(p1, p2):
    return great_circle((p1.y, p1.x), (p2.y, p2.x)).miles

# Function to build libraries from JSON data
def build_libraries_from_url(url, name_pos, lat_long_pos):
    r = requests.get(url)
    myjson = json.loads(r.text, parse_constant='utf-8')
    myjson = myjson['data']

    libraries = []
    k = 1
    for location in myjson:
        uname = location[name_pos]
        try:
            # Handle various data formats and potential string values
            lat_data = location[lat_long_pos][1]
            lng_data = location[lat_long_pos][2]
            
            # Try to convert to float, skip if not possible
            if lat_data is None or lng_data is None:
                continue
            if isinstance(lat_data, str) and not lat_data.replace('.', '').replace('-', '').isdigit():
                continue
            if isinstance(lng_data, str) and not lng_data.replace('.', '').replace('-', '').isdigit():
                continue
                
            latitude = float(lat_data)
            longitude = float(lng_data)
            
            # Basic validation of coordinates (Chicago area roughly)
            if not (41.0 <= latitude <= 42.5 and -88.5 <= longitude <= -87.0):
                continue
                
        except (TypeError, IndexError, ValueError, AttributeError):
            continue
            
        try:
            name = str(uname) if uname else "Unknown"
        except:
            name = "Unknown"
            
        # Clean name for better display
        name = name.replace('\n', ' ').replace('\r', ' ').strip()
        if not name or name == "None":
            name = f"Location_{k}"
            
        name = "P_%s_%d" % (name[:50], k)  # Limit name length
        
        if latitude and longitude:
            cp = NamedPoint(name, longitude, latitude)
            libraries.append(cp)
            k += 1
            
    return libraries

# Main execution
def main():
    # Load library data
    print("Downloading library data from Chicago Open Data...")
    try:
        libraries = build_libraries_from_url('https://data.cityofchicago.org/api/views/x8fc-8rcq/rows.json?accessType=DOWNLOAD',
                                           name_pos=10,
                                           lat_long_pos=16)
        print("There are %d public libraries in Chicago" % (len(libraries)))
        
        if len(libraries) == 0:
            print("No valid library data found. Using sample data...")
            # Fallback sample data for Chicago libraries
            libraries = [
                NamedPoint("Albany Park", -87.71409, 41.975456),
                NamedPoint("Altgeld", -87.54501, 41.631533),
                NamedPoint("Austin", -87.77593, 41.899041),
                NamedPoint("Austin-Irving", -87.772, 41.952),
                NamedPoint("Avalon", -87.605, 41.752),
                NamedPoint("Back of the Yards", -87.65138, 41.810147),
                NamedPoint("Bezazian", -87.672, 41.962),
                NamedPoint("Blackstone", -87.588, 41.805),
                NamedPoint("Brainerd", -87.555, 41.698),
                NamedPoint("Brighton Park", -87.696, 41.817),
                NamedPoint("Bucktown-Wicker Park", -87.674, 41.913),
                NamedPoint("Budlong Woods", -87.673, 41.981),
                NamedPoint("Canaryville", -87.652, 41.807),
                NamedPoint("Chinatown", -87.632, 41.853),
                NamedPoint("Clearing", -87.762, 41.790),
                NamedPoint("Coleman", -87.635, 41.821),
                NamedPoint("Douglass", -87.608, 41.818),
                NamedPoint("Dunning", -87.806, 41.950),
                NamedPoint("Edgewater", -87.663, 41.993),
                NamedPoint("Englewood", -87.644, 41.779),
                NamedPoint("Garfield Ridge", -87.760, 41.794),
                NamedPoint("Greater Grand Crossing", -87.602, 41.766),
                NamedPoint("Harold Washington Library Center", -87.628, 41.876),
                NamedPoint("Humboldt Park", -87.701, 41.907),
                NamedPoint("Jefferson Park", -87.760, 41.968),
                NamedPoint("Kelly", -87.623, 41.814),
                NamedPoint("King", -87.615, 41.827),
                NamedPoint("Lincoln Belmont", -87.689, 41.939),
                NamedPoint("Lincoln Park", -87.635, 41.928),
                NamedPoint("Little Village", -87.726, 41.852),
                NamedPoint("Logan Square", -87.707, 41.929),
                NamedPoint("Lozano", -87.698, 41.856),
                NamedPoint("McKinley Park", -87.652, 41.826),
                NamedPoint("Merlo", -87.784, 41.912),
                NamedPoint("Mount Greenwood", -87.702, 41.695),
                NamedPoint("Near North", -87.631, 41.903),
                NamedPoint("North Austin", -87.788, 41.913),
                NamedPoint("North Pulaski", -87.724, 41.975),
                NamedPoint("Northtown", -87.668, 41.991),
                NamedPoint("Northwest", -87.769, 41.962),
                NamedPoint("Oriole Park", -87.770, 41.943),
                NamedPoint("Portage-Cragin", -87.768, 41.952),
                NamedPoint("Pullman", -87.612, 41.706),
                NamedPoint("Ravenswood", -87.674, 41.961),
                NamedPoint("Richard J. Daley", -87.652, 41.807),
                NamedPoint("Roden", -87.672, 41.962),
                NamedPoint("Rogers Park", -87.675, 42.012),
                NamedPoint("Roosevelt", -87.634, 41.867),
                NamedPoint("Scottsdale", -87.801, 41.847),
                NamedPoint("South Chicago", -87.553, 41.746),
                NamedPoint("South Shore", -87.576, 41.757),
                NamedPoint("Sulzer Regional", -87.687, 41.973),
                NamedPoint("Thurgood Marshall", -87.624, 41.789),
                NamedPoint("Toman", -87.720, 41.976),
                NamedPoint("Uptown", -87.653, 41.966),
                NamedPoint("Vodak-East Side", -87.543, 41.719),
                NamedPoint("Walker", -87.709, 41.922),
                NamedPoint("West Belmont", -87.729, 41.939),
                NamedPoint("West Chicago Avenue", -87.674, 41.895),
                NamedPoint("West Englewood", -87.673, 41.778),
                NamedPoint("West Lawn", -87.720, 41.774),
                NamedPoint("West Pullman", -87.627, 41.689),
                NamedPoint("West Town", -87.674, 41.895),
                NamedPoint("Woodson Regional", -87.668, 41.936)
            ]
            print(f"Using {len(libraries)} sample libraries")
            
    except Exception as e:
        print(f"Error loading data: {e}")
        print("Using sample data instead...")
        # Use the same fallback data
        libraries = [
            NamedPoint("Albany Park", -87.71409, 41.975456),
            NamedPoint("Harold Washington Library Center", -87.628, 41.876),
            NamedPoint("Lincoln Park", -87.635, 41.928),
            NamedPoint("Uptown", -87.653, 41.966),
            NamedPoint("Chinatown", -87.632, 41.853),
            NamedPoint("Logan Square", -87.707, 41.929),
            NamedPoint("Edgewater", -87.663, 41.993),
            NamedPoint("Humboldt Park", -87.701, 41.907),
            NamedPoint("Near North", -87.631, 41.903),
            NamedPoint("Woodson Regional", -87.668, 41.936)
        ]
        print(f"Using {len(libraries)} sample libraries")

    # Define number of shops to open
    nb_shops = 5
    print("We would like to open %d coffee shops" % nb_shops)

    # Display initial map with all libraries
    map_osm = folium.Map(location=[41.878, -87.629], zoom_start=11)
    for library in libraries:
        lt = library.y
        lg = library.x
        folium.Marker([lt, lg]).add_to(map_osm)
    
    # Save initial map
    map_osm.save('initial_libraries_map.html')
    print("Initial libraries map saved as 'initial_libraries_map.html'")

    # Ensure unique points
    libraries = list(set(libraries))
    coffeeshop_locations = libraries

    # Create the PuLP optimization model
    prob = pulp.LpProblem("Coffee_Shop_Location", pulp.LpMinimize)

    # Decision variables
    # Binary vars indicating which coffee shop locations will be selected
    coffeeshop_vars = {}
    for loc in coffeeshop_locations:
        coffeeshop_vars[loc] = pulp.LpVariable(f"coffeeshop_{hash(loc)}", cat='Binary')

    # Binary vars representing the assignment of libraries to coffee shops
    link_vars = {}
    for c_loc in coffeeshop_locations:
        for lib in libraries:
            link_vars[(c_loc, lib)] = pulp.LpVariable(f"link_{hash(c_loc)}_{hash(lib)}", cat='Binary')

    # Constraint 1: Libraries can only be linked to open coffee shops
    for c_loc in coffeeshop_locations:
        for lib in libraries:
            prob += link_vars[(c_loc, lib)] <= coffeeshop_vars[c_loc], f"Link_constraint_{hash(c_loc)}_{hash(lib)}"

    # Constraint 2: Each library must be assigned to exactly one coffee shop
    for lib in libraries:
        prob += pulp.lpSum([link_vars[(c_loc, lib)] for c_loc in coffeeshop_locations]) == 1, f"Assignment_constraint_{hash(lib)}"

    # Constraint 3: Exactly nb_shops coffee shops must be opened
    prob += pulp.lpSum([coffeeshop_vars[c_loc] for c_loc in coffeeshop_locations]) == nb_shops, "Total_shops_constraint"

    # Objective: Minimize total distance from libraries to assigned coffee shops
    total_distance_expr = []
    for c_loc in coffeeshop_locations:
        for lib in libraries:
            distance = get_distance(c_loc, lib)
            total_distance_expr.append(link_vars[(c_loc, lib)] * distance)
    
    prob += pulp.lpSum(total_distance_expr), "Total_Distance"

    # Print problem information
    print(f"Number of variables: {len(prob.variables())}")
    print(f"Number of constraints: {len(prob.constraints)}")
    print("Problem type: Binary Integer Linear Programming")

    # Solve the problem
    print("\nSolving the optimization problem...")
    prob.solve(pulp.PULP_CBC_CMD(msg=1))

    # Check if solution is optimal
    if prob.status != pulp.LpStatusOptimal:
        print(f"Optimization failed. Status: {pulp.LpStatus[prob.status]}")
        return

    print(f"Optimization Status: {pulp.LpStatus[prob.status]}")

    # Extract solution
    total_distance = pulp.value(prob.objective)
    open_coffeeshops = [loc for loc in coffeeshop_locations if coffeeshop_vars[loc].varValue == 1]
    
    # Find assignments
    assignments = []
    for c_loc in coffeeshop_locations:
        for lib in libraries:
            if link_vars[(c_loc, lib)].varValue == 1:
                assignments.append((c_loc, lib))

    print(f"\nTotal distance = {total_distance:.3f} miles")
    print(f"Number of coffee shops opened = {len(open_coffeeshops)}")
    print("\nOptimal coffee shop locations:")
    for i, shop in enumerate(open_coffeeshops, 1):
        print(f"{i}. {shop}")

    # Create solution visualization map
    map_solution = folium.Map(location=[41.878, -87.629], zoom_start=11)
    
    # Add coffee shops (red markers with different icon)
    for coffeeshop in open_coffeeshops:
        lt = coffeeshop.y
        lg = coffeeshop.x
        folium.Marker(
            [lt, lg], 
            icon=folium.Icon(color='red', icon='info-sign'),
            popup=f'Coffee Shop: {coffeeshop.name}'
        ).add_to(map_solution)
    
    # Add other libraries (blue markers)
    for lib in libraries:
        if lib not in open_coffeeshops:
            lt = lib.y
            lg = lib.x
            folium.Marker(
                [lt, lg],
                popup=f'Library: {lib.name}'
            ).add_to(map_solution)
    
    # Add connection lines from libraries to assigned coffee shops
    for (coffeeshop, library) in assignments:
        coordinates = [[library.y, library.x], [coffeeshop.y, coffeeshop.x]]
        folium.PolyLine(
            coordinates, 
            color='red', 
            weight=2, 
            opacity=0.7
        ).add_to(map_solution)

    # Save solution map
    map_solution.save('coffee_shops_solution_pulp.html')
    print(f"\nSolution map saved as 'coffee_shops_solution_pulp.html'")
    
    # Print assignment details
    print(f"\nLibrary assignments:")
    assignment_dict = {}
    for coffeeshop, library in assignments:
        if coffeeshop not in assignment_dict:
            assignment_dict[coffeeshop] = []
        assignment_dict[coffeeshop].append(library)
    
    for coffeeshop in open_coffeeshops:
        assigned_libs = assignment_dict.get(coffeeshop, [])
        print(f"\nCoffee shop at {coffeeshop} serves {len(assigned_libs)} libraries:")
        for lib in assigned_libs:
            dist = get_distance(coffeeshop, lib)
            print(f"  - {lib} (distance: {dist:.2f} miles)")

if __name__ == "__main__":
    main()

