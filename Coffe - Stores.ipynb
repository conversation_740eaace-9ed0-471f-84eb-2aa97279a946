# Advanced Coffee Shop Location Optimization System
# Multi-Objective Optimization with Financial Modeling and Risk Analysis

import sys
import json
import requests
import pulp
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import folium
from folium import plugins
from geopy.distance import great_circle
from scipy import stats
from scipy.optimize import minimize
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import cross_val_score
import warnings
warnings.filterwarnings('ignore')

# Set style for professional visualizations
plt.style.use('seaborn-v0_8-darkgrid')
sns.set_palette("husl")

print("🚀 Advanced Coffee Shop Optimization System Initialized")
print("📊 Loading institutional-grade analytics framework...")

# Advanced Data Models and Classes

class AdvancedPoint:
    """Enhanced point class with demographic and economic attributes"""
    def __init__(self, name, longitude, latitude, 
                 population_density=1000, income_level=50000, 
                 foot_traffic=100, competition_score=0.5):
        self.name = name
        self.x = longitude
        self.y = latitude
        self.population_density = population_density
        self.income_level = income_level
        self.foot_traffic = foot_traffic
        self.competition_score = competition_score  # 0-1, higher = more competition
        
    def __str__(self):
        return self.name
    
    def __hash__(self):
        return hash((self.name, self.x, self.y))
    
    def __eq__(self, other):
        return (isinstance(other, AdvancedPoint) and 
                self.name == other.name and 
                abs(self.x - other.x) < 1e-6 and 
                abs(self.y - other.y) < 1e-6)

class FinancialModel:
    """Comprehensive financial modeling for coffee shop profitability"""
    def __init__(self):
        # Base financial parameters (industry benchmarks)
        self.setup_cost = 150000  # Initial investment
        self.monthly_fixed_costs = 8000  # Rent, utilities, staff
        self.avg_transaction = 6.50  # Average ticket size
        self.gross_margin = 0.65  # Industry standard
        self.discount_rate = 0.12  # Cost of capital
        self.analysis_period = 60  # months
        
    def calculate_monthly_revenue(self, location):
        """Calculate projected monthly revenue based on location attributes"""
        base_customers = location.foot_traffic * 30  # Monthly base
        
        # Demographic multipliers
        income_multiplier = min(location.income_level / 50000, 2.0)
        density_multiplier = min(location.population_density / 1000, 1.5)
        competition_penalty = 1 - (location.competition_score * 0.4)
        
        adjusted_customers = (base_customers * income_multiplier * 
                            density_multiplier * competition_penalty)
        
        return adjusted_customers * self.avg_transaction
    
    def calculate_npv(self, location):
        """Calculate Net Present Value for a location"""
        monthly_revenue = self.calculate_monthly_revenue(location)
        monthly_profit = (monthly_revenue * self.gross_margin) - self.monthly_fixed_costs
        
        # NPV calculation
        monthly_rate = self.discount_rate / 12
        npv = -self.setup_cost
        
        for month in range(1, self.analysis_period + 1):
            npv += monthly_profit / ((1 + monthly_rate) ** month)
            
        return npv
    
    def calculate_roi(self, location):
        """Calculate Return on Investment"""
        npv = self.calculate_npv(location)
        return (npv / self.setup_cost) * 100
    
    def calculate_payback_period(self, location):
        """Calculate payback period in months"""
        monthly_revenue = self.calculate_monthly_revenue(location)
        monthly_profit = (monthly_revenue * self.gross_margin) - self.monthly_fixed_costs
        
        if monthly_profit <= 0:
            return float('inf')
            
        return self.setup_cost / monthly_profit

print("💼 Advanced financial modeling framework loaded")
print("📈 Multi-objective optimization classes initialized")

# Advanced Multi-Objective Optimization Engine

class AdvancedOptimizer:
    """Sophisticated optimization engine with multiple objectives and constraints"""
    
    def __init__(self, financial_model):
        self.financial_model = financial_model
        self.results_cache = {}
        
    def calculate_distance_matrix(self, locations, libraries):
        """Precompute distance matrix for efficiency"""
        distances = {}
        for loc in locations:
            for lib in libraries:
                distances[(loc, lib)] = great_circle((loc.y, loc.x), (lib.y, lib.x)).miles
        return distances
    
    def solve_multi_objective(self, locations, libraries, num_shops, 
                            distance_weight=0.4, profit_weight=0.4, coverage_weight=0.2):
        """Solve multi-objective optimization problem"""
        
        print(f"🎯 Solving multi-objective optimization with {len(locations)} candidate locations")
        print(f"📊 Weights: Distance={distance_weight}, Profit={profit_weight}, Coverage={coverage_weight}")
        
        # Precompute matrices
        distances = self.calculate_distance_matrix(locations, libraries)
        
        # Create optimization model
        prob = pulp.LpProblem("Multi_Objective_Coffee_Optimization", pulp.LpMaximize)
        
        # Decision variables
        shop_vars = {loc: pulp.LpVariable(f"shop_{hash(loc)}", cat='Binary') 
                    for loc in locations}
        
        assignment_vars = {(loc, lib): pulp.LpVariable(f"assign_{hash(loc)}_{hash(lib)}", cat='Binary')
                          for loc in locations for lib in libraries}
        
        # Constraints
        # 1. Each library assigned to exactly one shop
        for lib in libraries:
            prob += pulp.lpSum([assignment_vars[(loc, lib)] for loc in locations]) == 1
        
        # 2. Libraries can only be assigned to open shops
        for loc in locations:
            for lib in libraries:
                prob += assignment_vars[(loc, lib)] <= shop_vars[loc]
        
        # 3. Exactly num_shops shops must be opened
        prob += pulp.lpSum([shop_vars[loc] for loc in locations]) == num_shops
        
        # Multi-objective function
        # Minimize distance (negative for maximization)
        distance_obj = -pulp.lpSum([assignment_vars[(loc, lib)] * distances[(loc, lib)] 
                                   for loc in locations for lib in libraries])
        
        # Maximize profit
        profit_obj = pulp.lpSum([shop_vars[loc] * self.financial_model.calculate_npv(loc) 
                                for loc in locations])
        
        # Maximize coverage (minimize max distance to any library)
        max_distance_var = pulp.LpVariable("max_distance", lowBound=0)
        for lib in libraries:
            prob += pulp.lpSum([assignment_vars[(loc, lib)] * distances[(loc, lib)] 
                               for loc in locations]) <= max_distance_var
        coverage_obj = -max_distance_var
        
        # Combined objective
        prob += (distance_weight * distance_obj + 
                profit_weight * profit_obj / 100000 +  # Scale profit
                coverage_weight * coverage_obj)
        
        # Solve
        prob.solve(pulp.PULP_CBC_CMD(msg=0))
        
        if prob.status != pulp.LpStatusOptimal:
            raise Exception(f"Optimization failed: {pulp.LpStatus[prob.status]}")
        
        # Extract solution
        selected_shops = [loc for loc in locations if shop_vars[loc].varValue == 1]
        assignments = [(loc, lib) for loc in locations for lib in libraries 
                      if assignment_vars[(loc, lib)].varValue == 1]
        
        return {
            'selected_shops': selected_shops,
            'assignments': assignments,
            'objective_value': pulp.value(prob.objective),
            'total_distance': sum(distances[(loc, lib)] for loc, lib in assignments),
            'total_npv': sum(self.financial_model.calculate_npv(loc) for loc in selected_shops),
            'max_distance': max(distances[(loc, lib)] for loc, lib in assignments)
        }

print("🔧 Advanced optimization engine loaded")
print("⚡ Multi-objective solver ready for deployment")

# Enhanced Data Loading and Processing

def load_enhanced_library_data():
    """Load and enhance library data with demographic information"""
    print("📡 Loading enhanced library data with demographic intelligence...")
    
    # Enhanced Chicago library data with realistic demographic attributes
    enhanced_libraries = [
        AdvancedPoint("Albany Park", -87.71409, 41.975456, 2800, 45000, 150, 0.6),
        AdvancedPoint("Altgeld", -87.54501, 41.631533, 800, 35000, 80, 0.3),
        AdvancedPoint("Austin", -87.77593, 41.899041, 1200, 38000, 120, 0.7),
        AdvancedPoint("Austin-Irving", -87.772, 41.952, 1500, 42000, 110, 0.5),
        AdvancedPoint("Avalon", -87.605, 41.752, 1800, 48000, 140, 0.4),
        AdvancedPoint("Back of the Yards", -87.65138, 41.810147, 2200, 32000, 90, 0.8),
        AdvancedPoint("Bezazian", -87.672, 41.962, 2500, 55000, 180, 0.6),
        AdvancedPoint("Blackstone", -87.588, 41.805, 1600, 52000, 160, 0.5),
        AdvancedPoint("Brainerd", -87.555, 41.698, 1000, 40000, 100, 0.4),
        AdvancedPoint("Brighton Park", -87.696, 41.817, 1800, 36000, 110, 0.7),
        AdvancedPoint("Bucktown-Wicker Park", -87.674, 41.913, 3500, 75000, 250, 0.9),
        AdvancedPoint("Budlong Woods", -87.673, 41.981, 2000, 58000, 170, 0.5),
        AdvancedPoint("Canaryville", -87.652, 41.807, 1400, 44000, 120, 0.6),
        AdvancedPoint("Chinatown", -87.632, 41.853, 2800, 46000, 200, 0.8),
        AdvancedPoint("Clearing", -87.762, 41.790, 1200, 48000, 100, 0.4),
        AdvancedPoint("Coleman", -87.635, 41.821, 1600, 41000, 130, 0.6),
        AdvancedPoint("Douglass", -87.608, 41.818, 1800, 39000, 110, 0.7),
        AdvancedPoint("Dunning", -87.806, 41.950, 1500, 52000, 140, 0.5),
        AdvancedPoint("Edgewater", -87.663, 41.993, 2600, 62000, 190, 0.7),
        AdvancedPoint("Englewood", -87.644, 41.779, 1100, 28000, 80, 0.5),
        AdvancedPoint("Harold Washington Library Center", -87.628, 41.876, 4000, 65000, 300, 0.9),
        AdvancedPoint("Lincoln Park", -87.635, 41.928, 3200, 85000, 280, 0.8),
        AdvancedPoint("Logan Square", -87.707, 41.929, 2800, 72000, 220, 0.8),
        AdvancedPoint("Near North", -87.631, 41.903, 3800, 95000, 350, 0.9),
        AdvancedPoint("Uptown", -87.653, 41.966, 2700, 52000, 200, 0.8)
    ]
    
    print(f"✅ Loaded {len(enhanced_libraries)} enhanced library locations")
    return enhanced_libraries

print("🔄 Enhanced data loading functions ready")

# Main Execution Engine - Strategic Coffee Shop Optimization

def run_advanced_optimization(num_shops=5):
    """Execute comprehensive coffee shop location optimization"""
    
    print("="*80)
    print("🚀 ADVANCED COFFEE SHOP LOCATION OPTIMIZATION SYSTEM")
    print("📊 Institutional-Grade Multi-Objective Analysis")
    print("="*80)
    
    # Initialize components
    financial_model = FinancialModel()
    optimizer = AdvancedOptimizer(financial_model)
    
    # Load enhanced data
    print("\n📡 PHASE 1: Data Loading and Enhancement")
    print("-" * 50)
    libraries = load_enhanced_library_data()
    
    # Use libraries as candidate locations for this demonstration
    candidate_locations = libraries
    print(f"📍 Total candidate locations: {len(candidate_locations)}")
    
    # Run multi-objective optimization
    print(f"\n🎯 PHASE 2: Multi-Objective Optimization ({num_shops} shops)")
    print("-" * 50)
    
    # Test different weight scenarios
    scenarios = [
        {"name": "Distance-Focused", "weights": (0.6, 0.3, 0.1)},
        {"name": "Profit-Focused", "weights": (0.2, 0.7, 0.1)},
        {"name": "Balanced", "weights": (0.4, 0.4, 0.2)},
        {"name": "Coverage-Focused", "weights": (0.3, 0.2, 0.5)}
    ]
    
    scenario_results = {}
    
    for scenario in scenarios:
        print(f"\n🔍 Analyzing scenario: {scenario['name']}")
        weights = scenario['weights']
        
        try:
            result = optimizer.solve_multi_objective(
                candidate_locations, libraries, num_shops,
                distance_weight=weights[0],
                profit_weight=weights[1],
                coverage_weight=weights[2]
            )
            scenario_results[scenario['name']] = result
            
            print(f"   ✅ Total NPV: ${result['total_npv']:,.0f}")
            print(f"   📏 Total Distance: {result['total_distance']:.1f} miles")
            print(f"   📐 Max Distance: {result['max_distance']:.1f} miles")
            
        except Exception as e:
            print(f"   ❌ Scenario failed: {e}")
            continue
    
    # Select best scenario (balanced approach)
    if "Balanced" in scenario_results:
        best_result = scenario_results["Balanced"]
        best_scenario = "Balanced"
    else:
        best_scenario = list(scenario_results.keys())[0]
        best_result = scenario_results[best_scenario]
    
    print(f"\n🏆 Selected Scenario: {best_scenario}")
    print(f"📊 Optimal Solution Summary:")
    print(f"   • Total NPV: ${best_result['total_npv']:,.0f}")
    print(f"   • Average ROI: {np.mean([financial_model.calculate_roi(shop) for shop in best_result['selected_shops']]):.1f}%")
    print(f"   • Total Distance: {best_result['total_distance']:.1f} miles")
    print(f"   • Max Distance: {best_result['max_distance']:.1f} miles")
    
    # Generate detailed report
    print(f"\n📋 PHASE 3: Strategic Recommendations")
    print("-" * 50)
    
    print(f"\n🏪 OPTIMAL COFFEE SHOP LOCATIONS:")
    for i, shop in enumerate(best_result['selected_shops'], 1):
        npv = financial_model.calculate_npv(shop)
        roi = financial_model.calculate_roi(shop)
        payback = financial_model.calculate_payback_period(shop)
        
        print(f"\n{i}. {shop.name}")
        print(f"   📍 Location: ({shop.y:.4f}, {shop.x:.4f})")
        print(f"   💰 NPV: ${npv:,.0f}")
        print(f"   📈 ROI: {roi:.1f}%")
        print(f"   ⏱️ Payback: {payback:.1f} months")
        print(f"   🏘️ Demographics: {shop.population_density:,.0f}/sq mi, ${shop.income_level:,.0f} avg income")
        print(f"   🚶 Foot Traffic: {shop.foot_traffic}/day")
        print(f"   🏢 Competition: {shop.competition_score:.2f}/1.0")
    
    print(f"\n🎯 STRATEGIC INSIGHTS:")
    total_investment = len(best_result['selected_shops']) * financial_model.setup_cost
    portfolio_npv = best_result['total_npv']
    portfolio_roi = (portfolio_npv / total_investment) * 100
    
    print(f"   • Total Investment Required: ${total_investment:,.0f}")
    print(f"   • Portfolio NPV: ${portfolio_npv:,.0f}")
    print(f"   • Portfolio ROI: {portfolio_roi:.1f}%")
    print(f"   • Average Service Distance: {best_result['total_distance']/len(libraries):.2f} miles")
    print(f"   • Market Coverage: {len(libraries)} libraries served")
    
    print(f"\n" + "="*80)
    print(f"✅ OPTIMIZATION COMPLETE - STRATEGIC RECOMMENDATIONS READY")
    print(f"="*80)
    
    return {
        'best_result': best_result,
        'scenario_results': scenario_results,
        'libraries': libraries,
        'candidates': candidate_locations
    }

# Execute the advanced optimization
print("🎯 Advanced optimization system ready for execution")
print("💡 Run: results = run_advanced_optimization(num_shops=5)")