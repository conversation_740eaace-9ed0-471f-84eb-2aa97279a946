{"version": 3, "file": "1869.48ca2e23bddad3adfc1a.js?v=48ca2e23bddad3adfc1a", "mappings": ";;;;;;;;;;AAAO;AACP;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA,6BAA6B,aAAa;;AAE1C;AACA;AACA;;AAEA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,cAAc,OAAO;AACrB;AACA;AACA;AACA;AACA,yBAAyB;AACzB;;AAEA;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/marked-mangle/src/index.js"], "sourcesContent": ["export function mangle() {\n  return {\n    mangle: false, // remove this once mangle option is removed\n    walkTokens(token) {\n      if (token.type !== 'link') {\n        return;\n      }\n\n      if (!token.href.startsWith('mailto:')) {\n        return;\n      }\n\n      const email = token.href.substring(7);\n      const mangledEmail = mangleEmail(email);\n\n      token.href = `mailto:${mangledEmail}`;\n\n      if (token.tokens.length !== 1 || token.tokens[0].type !== 'text' || token.tokens[0].text !== email) {\n        return;\n      }\n\n      token.text = mangledEmail;\n      token.tokens[0].text = mangledEmail;\n    },\n  };\n}\n\nfunction mangleEmail(text) {\n  let out = '',\n    i,\n    ch;\n\n  const l = text.length;\n  for (i = 0; i < l; i++) {\n    ch = text.charCodeAt(i);\n    if (Math.random() > 0.5) {\n      ch = 'x' + ch.toString(16);\n    }\n    out += '&#' + ch + ';';\n  }\n\n  return out;\n}\n"], "names": [], "sourceRoot": ""}