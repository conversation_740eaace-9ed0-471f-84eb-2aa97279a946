{"version": 3, "file": "221.21b91ccc95eefd849fa5.js?v=21b91ccc95eefd849fa5", "mappings": ";;;;;;;;;;AAAA;AACA,yBAAyB,WAAW;;AAEpC;AACA;;AAEA;AACA;AACA;AACA;AACA,MAAM;AACN,IAAI,yCAAyC;AAC7C;AACA;AACA,IAAI;AACJ;AACA,kEAAkE,6CAA6C;AAC/G;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA,IAAI;AACJ;AACA,gCAAgC;AAChC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEO;AACP;AACA;AACA,YAAY;AACZ;AACA;AACA,GAAG;;AAEH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/dtd.js"], "sourcesContent": ["var type;\nfunction ret(style, tp) {type = tp; return style;}\n\nfunction tokenBase(stream, state) {\n  var ch = stream.next();\n\n  if (ch == \"<\" && stream.eat(\"!\") ) {\n    if (stream.eatWhile(/[\\-]/)) {\n      state.tokenize = tokenSGMLComment;\n      return tokenSGMLComment(stream, state);\n    } else if (stream.eatWhile(/[\\w]/)) return ret(\"keyword\", \"doindent\");\n  } else if (ch == \"<\" && stream.eat(\"?\")) { //xml declaration\n    state.tokenize = inBlock(\"meta\", \"?>\");\n    return ret(\"meta\", ch);\n  } else if (ch == \"#\" && stream.eatWhile(/[\\w]/)) return ret(\"atom\", \"tag\");\n  else if (ch == \"|\") return ret(\"keyword\", \"separator\");\n  else if (ch.match(/[\\(\\)\\[\\]\\-\\.,\\+\\?>]/)) return ret(null, ch);//if(ch === \">\") return ret(null, \"endtag\"); else\n  else if (ch.match(/[\\[\\]]/)) return ret(\"rule\", ch);\n  else if (ch == \"\\\"\" || ch == \"'\") {\n    state.tokenize = tokenString(ch);\n    return state.tokenize(stream, state);\n  } else if (stream.eatWhile(/[a-zA-Z\\?\\+\\d]/)) {\n    var sc = stream.current();\n    if( sc.substr(sc.length-1,sc.length).match(/\\?|\\+/) !== null )stream.backUp(1);\n    return ret(\"tag\", \"tag\");\n  } else if (ch == \"%\" || ch == \"*\" ) return ret(\"number\", \"number\");\n  else {\n    stream.eatWhile(/[\\w\\\\\\-_%.{,]/);\n    return ret(null, null);\n  }\n}\n\nfunction tokenSGMLComment(stream, state) {\n  var dashes = 0, ch;\n  while ((ch = stream.next()) != null) {\n    if (dashes >= 2 && ch == \">\") {\n      state.tokenize = tokenBase;\n      break;\n    }\n    dashes = (ch == \"-\") ? dashes + 1 : 0;\n  }\n  return ret(\"comment\", \"comment\");\n}\n\nfunction tokenString(quote) {\n  return function(stream, state) {\n    var escaped = false, ch;\n    while ((ch = stream.next()) != null) {\n      if (ch == quote && !escaped) {\n        state.tokenize = tokenBase;\n        break;\n      }\n      escaped = !escaped && ch == \"\\\\\";\n    }\n    return ret(\"string\", \"tag\");\n  };\n}\n\nfunction inBlock(style, terminator) {\n  return function(stream, state) {\n    while (!stream.eol()) {\n      if (stream.match(terminator)) {\n        state.tokenize = tokenBase;\n        break;\n      }\n      stream.next();\n    }\n    return style;\n  };\n}\n\nexport const dtd = {\n  name: \"dtd\",\n  startState: function() {\n    return {tokenize: tokenBase,\n            baseIndent: 0,\n            stack: []};\n  },\n\n  token: function(stream, state) {\n    if (stream.eatSpace()) return null;\n    var style = state.tokenize(stream, state);\n\n    var context = state.stack[state.stack.length-1];\n    if (stream.current() == \"[\" || type === \"doindent\" || type == \"[\") state.stack.push(\"rule\");\n    else if (type === \"endtag\") state.stack[state.stack.length-1] = \"endtag\";\n    else if (stream.current() == \"]\" || type == \"]\" || (type == \">\" && context == \"rule\")) state.stack.pop();\n    else if (type == \"[\") state.stack.push(\"[\");\n    return style;\n  },\n\n  indent: function(state, textAfter, cx) {\n    var n = state.stack.length;\n\n    if( textAfter.charAt(0) === ']' )n--;\n    else if(textAfter.substr(textAfter.length-1, textAfter.length) === \">\"){\n      if(textAfter.substr(0,1) === \"<\") {}\n      else if( type == \"doindent\" && textAfter.length > 1 ) {}\n      else if( type == \"doindent\")n--;\n      else if( type == \">\" && textAfter.length > 1) {}\n      else if( type == \"tag\" && textAfter !== \">\") {}\n      else if( type == \"tag\" && state.stack[state.stack.length-1] == \"rule\")n--;\n      else if( type == \"tag\")n++;\n      else if( textAfter === \">\" && state.stack[state.stack.length-1] == \"rule\" && type === \">\")n--;\n      else if( textAfter === \">\" && state.stack[state.stack.length-1] == \"rule\") {}\n      else if( textAfter.substr(0,1) !== \"<\" && textAfter.substr(0,1) === \">\" )n=n-1;\n      else if( textAfter === \">\") {}\n      else n=n-1;\n      //over rule them all\n      if(type == null || type == \"]\")n--;\n    }\n\n    return state.baseIndent + n * cx.unit;\n  },\n\n  languageData: {\n    indentOnInput: /^\\s*[\\]>]$/\n  }\n};\n\n"], "names": [], "sourceRoot": ""}