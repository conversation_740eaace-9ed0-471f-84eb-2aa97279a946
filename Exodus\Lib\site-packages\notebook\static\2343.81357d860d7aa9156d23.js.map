{"version": 3, "file": "2343.81357d860d7aa9156d23.js?v=81357d860d7aa9156d23", "mappings": ";;;;;;;;;;;AAA2C;;AAE3C;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEO,mBAAmB,oEAAU;AACpC;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA,CAAC;;;;;;;;;;;;ACpMM;AACP;AACA,kBAAkB,kCAAkC;AACpD;AACA;AACA,oBAAoB,iBAAiB;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd,KAAK;AACL;AACA,eAAe;AACf;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,kBAAkB,gBAAgB;AAClC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,oBAAoB,qBAAqB;AACzC;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,UAAU;AACV;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B,oBAAoB;AAC9C;AACA,kCAAkC,2CAA2C;AAC7E;AACA;AACA,UAAU;AACV;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,iBAAiB;AACjB,sBAAsB,kBAAkB;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/dockerfile.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/simple-mode.js"], "sourcesContent": ["import {simpleMode} from \"./simple-mode.js\"\n\nvar from = \"from\";\nvar fromRegex = new RegExp(\"^(\\\\s*)\\\\b(\" + from + \")\\\\b\", \"i\");\n\nvar shells = [\"run\", \"cmd\", \"entrypoint\", \"shell\"];\nvar shellsAsArrayRegex = new RegExp(\"^(\\\\s*)(\" + shells.join('|') + \")(\\\\s+\\\\[)\", \"i\");\n\nvar expose = \"expose\";\nvar exposeRegex = new RegExp(\"^(\\\\s*)(\" + expose + \")(\\\\s+)\", \"i\");\n\nvar others = [\n  \"arg\", \"from\", \"maintainer\", \"label\", \"env\",\n  \"add\", \"copy\", \"volume\", \"user\",\n  \"workdir\", \"onbuild\", \"stopsignal\", \"healthcheck\", \"shell\"\n];\n\n// Collect all Dockerfile directives\nvar instructions = [from, expose].concat(shells).concat(others),\n    instructionRegex = \"(\" + instructions.join('|') + \")\",\n    instructionOnlyLine = new RegExp(\"^(\\\\s*)\" + instructionRegex + \"(\\\\s*)(#.*)?$\", \"i\"),\n    instructionWithArguments = new RegExp(\"^(\\\\s*)\" + instructionRegex + \"(\\\\s+)\", \"i\");\n\nexport const dockerFile = simpleMode({\n  start: [\n    // Block comment: This is a line starting with a comment\n    {\n      regex: /^\\s*#.*$/,\n      sol: true,\n      token: \"comment\"\n    },\n    {\n      regex: fromRegex,\n      token: [null, \"keyword\"],\n      sol: true,\n      next: \"from\"\n    },\n    // Highlight an instruction without any arguments (for convenience)\n    {\n      regex: instructionOnlyLine,\n      token: [null, \"keyword\", null, \"error\"],\n      sol: true\n    },\n    {\n      regex: shellsAsArrayRegex,\n      token: [null, \"keyword\", null],\n      sol: true,\n      next: \"array\"\n    },\n    {\n      regex: exposeRegex,\n      token: [null, \"keyword\", null],\n      sol: true,\n      next: \"expose\"\n    },\n    // Highlight an instruction followed by arguments\n    {\n      regex: instructionWithArguments,\n      token: [null, \"keyword\", null],\n      sol: true,\n      next: \"arguments\"\n    },\n    {\n      regex: /./,\n      token: null\n    }\n  ],\n  from: [\n    {\n      regex: /\\s*$/,\n      token: null,\n      next: \"start\"\n    },\n    {\n      // Line comment without instruction arguments is an error\n      regex: /(\\s*)(#.*)$/,\n      token: [null, \"error\"],\n      next: \"start\"\n    },\n    {\n      regex: /(\\s*\\S+\\s+)(as)/i,\n      token: [null, \"keyword\"],\n      next: \"start\"\n    },\n    // Fail safe return to start\n    {\n      token: null,\n      next: \"start\"\n    }\n  ],\n  single: [\n    {\n      regex: /(?:[^\\\\']|\\\\.)/,\n      token: \"string\"\n    },\n    {\n      regex: /'/,\n      token: \"string\",\n      pop: true\n    }\n  ],\n  double: [\n    {\n      regex: /(?:[^\\\\\"]|\\\\.)/,\n      token: \"string\"\n    },\n    {\n      regex: /\"/,\n      token: \"string\",\n      pop: true\n    }\n  ],\n  array: [\n    {\n      regex: /\\]/,\n      token: null,\n      next: \"start\"\n    },\n    {\n      regex: /\"(?:[^\\\\\"]|\\\\.)*\"?/,\n      token: \"string\"\n    }\n  ],\n  expose: [\n    {\n      regex: /\\d+$/,\n      token: \"number\",\n      next: \"start\"\n    },\n    {\n      regex: /[^\\d]+$/,\n      token: null,\n      next: \"start\"\n    },\n    {\n      regex: /\\d+/,\n      token: \"number\"\n    },\n    {\n      regex: /[^\\d]+/,\n      token: null\n    },\n    // Fail safe return to start\n    {\n      token: null,\n      next: \"start\"\n    }\n  ],\n  arguments: [\n    {\n      regex: /^\\s*#.*$/,\n      sol: true,\n      token: \"comment\"\n    },\n    {\n      regex: /\"(?:[^\\\\\"]|\\\\.)*\"?$/,\n      token: \"string\",\n      next: \"start\"\n    },\n    {\n      regex: /\"/,\n      token: \"string\",\n      push: \"double\"\n    },\n    {\n      regex: /'(?:[^\\\\']|\\\\.)*'?$/,\n      token: \"string\",\n      next: \"start\"\n    },\n    {\n      regex: /'/,\n      token: \"string\",\n      push: \"single\"\n    },\n    {\n      regex: /[^#\"']+[\\\\`]$/,\n      token: null\n    },\n    {\n      regex: /[^#\"']+$/,\n      token: null,\n      next: \"start\"\n    },\n    {\n      regex: /[^#\"']+/,\n      token: null\n    },\n    // Fail safe return to start\n    {\n      token: null,\n      next: \"start\"\n    }\n  ],\n  languageData: {\n    commentTokens: {line: \"#\"}\n  }\n});\n\n", "export function simpleMode(states) {\n  ensureState(states, \"start\");\n  var states_ = {}, meta = states.languageData || {}, hasIndentation = false;\n  for (var state in states) if (state != meta && states.hasOwnProperty(state)) {\n    var list = states_[state] = [], orig = states[state];\n    for (var i = 0; i < orig.length; i++) {\n      var data = orig[i];\n      list.push(new Rule(data, states));\n      if (data.indent || data.dedent) hasIndentation = true;\n    }\n  }\n  return {\n    name: meta.name,\n    startState: function() {\n      return {state: \"start\", pending: null, indent: hasIndentation ? [] : null};\n    },\n    copyState: function(state) {\n      var s = {state: state.state, pending: state.pending, indent: state.indent && state.indent.slice(0)};\n      if (state.stack)\n        s.stack = state.stack.slice(0);\n      return s;\n    },\n    token: tokenFunction(states_),\n    indent: indentFunction(states_, meta),\n    mergeTokens: meta.mergeTokens,\n    languageData: meta\n  }\n};\n\nfunction ensureState(states, name) {\n  if (!states.hasOwnProperty(name))\n    throw new Error(\"Undefined state \" + name + \" in simple mode\");\n}\n\nfunction toRegex(val, caret) {\n  if (!val) return /(?:)/;\n  var flags = \"\";\n  if (val instanceof RegExp) {\n    if (val.ignoreCase) flags = \"i\";\n    val = val.source;\n  } else {\n    val = String(val);\n  }\n  return new RegExp((caret === false ? \"\" : \"^\") + \"(?:\" + val + \")\", flags);\n}\n\nfunction asToken(val) {\n  if (!val) return null;\n  if (val.apply) return val\n  if (typeof val == \"string\") return val.replace(/\\./g, \" \");\n  var result = [];\n  for (var i = 0; i < val.length; i++)\n    result.push(val[i] && val[i].replace(/\\./g, \" \"));\n  return result;\n}\n\nfunction Rule(data, states) {\n  if (data.next || data.push) ensureState(states, data.next || data.push);\n  this.regex = toRegex(data.regex);\n  this.token = asToken(data.token);\n  this.data = data;\n}\n\nfunction tokenFunction(states) {\n  return function(stream, state) {\n    if (state.pending) {\n      var pend = state.pending.shift();\n      if (state.pending.length == 0) state.pending = null;\n      stream.pos += pend.text.length;\n      return pend.token;\n    }\n\n    var curState = states[state.state];\n    for (var i = 0; i < curState.length; i++) {\n      var rule = curState[i];\n      var matches = (!rule.data.sol || stream.sol()) && stream.match(rule.regex);\n      if (matches) {\n        if (rule.data.next) {\n          state.state = rule.data.next;\n        } else if (rule.data.push) {\n          (state.stack || (state.stack = [])).push(state.state);\n          state.state = rule.data.push;\n        } else if (rule.data.pop && state.stack && state.stack.length) {\n          state.state = state.stack.pop();\n        }\n\n        if (rule.data.indent)\n          state.indent.push(stream.indentation() + stream.indentUnit);\n        if (rule.data.dedent)\n          state.indent.pop();\n        var token = rule.token\n        if (token && token.apply) token = token(matches)\n        if (matches.length > 2 && rule.token && typeof rule.token != \"string\") {\n          state.pending = [];\n          for (var j = 2; j < matches.length; j++)\n            if (matches[j])\n              state.pending.push({text: matches[j], token: rule.token[j - 1]});\n          stream.backUp(matches[0].length - (matches[1] ? matches[1].length : 0));\n          return token[0];\n        } else if (token && token.join) {\n          return token[0];\n        } else {\n          return token;\n        }\n      }\n    }\n    stream.next();\n    return null;\n  };\n}\n\nfunction indentFunction(states, meta) {\n  return function(state, textAfter) {\n    if (state.indent == null || meta.dontIndentStates && meta.dontIndentStates.indexOf(state.state) > -1)\n      return null\n\n    var pos = state.indent.length - 1, rules = states[state.state];\n    scan: for (;;) {\n      for (var i = 0; i < rules.length; i++) {\n        var rule = rules[i];\n        if (rule.data.dedent && rule.data.dedentIfLineStart !== false) {\n          var m = rule.regex.exec(textAfter);\n          if (m && m[0]) {\n            pos--;\n            if (rule.next || rule.push) rules = states[rule.next || rule.push];\n            textAfter = textAfter.slice(m[0].length);\n            continue scan;\n          }\n        }\n      }\n      break;\n    }\n    return pos < 0 ? 0 : state.indent[pos];\n  };\n}\n"], "names": [], "sourceRoot": ""}