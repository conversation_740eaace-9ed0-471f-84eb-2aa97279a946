{"version": 3, "file": "35.a486baf38b12aec5500f.js?v=a486baf38b12aec5500f", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN,IAAI;AACJ;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;AC1CA;AACA;AACA;;AAEyC;AACA;AACR;AACA;AACW;AACV;AACV;;AAEjB;;AAEP;AACA,aAAa,QAAQ;AACrB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,8DAA8D;AACpE;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACO,wBAAwB,4BAAU;AACzC;AACA,aAAa,OAAO;AACpB;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA,cAAc;AACd;AACA;AACA,qCAAqC,KAAK;AAC1C,kBAAkB,6BAAgB;AAClC,qFAAqF,qBAAqB;AAC1G;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,KAAK,EAAE,kBAAU;AACjB;AACA;AACA,KAAK;AACL,yBAAyB;AACzB;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,cAAc;AACd;AACA;AACA;AACA;;AAEA;AACA,aAAa,yBAAyB;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,mBAAmB,6BAAgB;AACnC,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,MAAM;AACN;AACA,WAAW,kCAAc;AACzB;AACA;AACA;AACA;AACA,6BAA6B,0CAA0C;AACvE;AACA,2BAA2B,yBAAyB;AACpD;;AAEA;AACA,aAAa,QAAQ;AACrB,aAAa,KAAK;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;;AAEA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,WAAW;AACtB,WAAW,eAAe;AAC1B,WAAW,KAAK;AAChB;AACO;AACP;AACA,kBAAkB,oBAAoB;AACtC;AACA;AACA;AACA;AACA,mCAAmC,iBAAiB;AACpD;AACA;AACA,uBAAuB,6BAAgB;AACvC,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,gCAAgC,iCAAiC;AACjE,gCAAgC,iCAAiC;AACjE;AACA;;AAEA;AACA,WAAW,WAAW;AACtB,WAAW,eAAe;AAC1B,YAAY;AACZ;AACO;AACP;AACA;AACA;AACA,kBAAkB,SAAS;AAC3B;AACA;AACA,6BAA6B,iBAAiB;AAC9C;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,YAAY;AACvB,WAAW,mBAAmB;AAC9B,YAAY;AACZ;AACO;AACP;AACA;AACA;AACA;AACA,kBAAkB,SAAS;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,WAAW,WAAW;AACtB,WAAW,YAAY;AACvB,WAAW,KAAK;AAChB;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,SAAS;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,QAAQ;AACR;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;;ACtSA;AACA;AACA;AACA;AAC4C;AACD;AACO;AACzB;AACzB;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA,aAAa;AACb,iCAAiC,aAAa;AAC9C;AACA,4BAA4B,oBAAM;AAClC;AACA,6BAA6B,oBAAM;AACnC,gIAAgI,YAAK;AACrI;AACA,gCAAgC,oBAAa;AAC7C;AACA;AACA,SAAS;AACT,8BAA8B,SAAS;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,iBAAO;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,oBAAM;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,iBAAO;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,iBAAO;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;ACtLA;AACA;AACA;AACA;AAC2C;AAC3C;AACA;AACA;AACO,oBAAoB,SAAS;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,mCAAmC;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;;AChGA;AACA;AACA;AACA;AACkD;AACP;AACO;AACzB;AACzB;AACA;AACA;AACO,yDAAyD;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACA,uBAAuB,YAAK;AAC5B,wBAAwB,aAAM;AAC9B,0BAA0B,YAAK;AAC/B;AACA;AACA;AACA,qEAAqE,cAAI;AACzE;AACA;AACA;AACA,yDAAyD,UAAU;AACnE;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,cAAO;AACxC;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oDAAoD,UAAU;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO;AACA;AACP;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,yCAAyC;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA,6CAA6C;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC,oBAAM;AAC1C;AACA;AACA;AACA;AACA,4BAA4B,oBAAM;AAClC,6BAA6B,oBAAM;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wCAAwC,oBAAa;AACrD;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,4BAA4B,YAAK;AACjC;AACA,kCAAkC,SAAS;AAC3C,oCAAoC,oBAAa;AACjD;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,oBAAM;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,4BAA4B;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4DAA4D;AAC5D,kBAAkB,iBAAO;AACzB;AACA;AACA,mBAAmB,iBAAO;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA,0DAA0D,UAAU,uBAAuB;AAC3F;AACA;AACA;AACA;AACA,gBAAgB,iBAAO;AACvB;AACA;AACA;AACA;AACA;AACA;AACA,2GAA2G;AAC3G;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,0BAA0B,iBAAO;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,iBAAO;AACxB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wCAAwC,iBAAO;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC;AACjC;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,gCAAgC;AAClD,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA,uDAAuD;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,iBAAO;AACtB;AACA;AACA;AACA,6BAA6B,iBAAO;AACpC;AACA;AACA;AACA,wBAAwB,6BAA6B;AACrD;AACA,oCAAoC,aAAM;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC,YAAK;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AC12BA;AACA;AACA;AACA;AAC4C;AACD;AAClB;AACkB;AAC4B;AACvE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,wBAAwB,SAAS;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4CAA4C,iBAAO;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC;AACrC;AACA;AACA;AACA;AACA,iBAAiB;AACjB,qCAAqC,gBAAgB;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA,yCAAyC,gBAAgB;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC,iBAAiB;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC,iBAAiB;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B,6BAA6B,SAAS,gBAAgB;AACpF;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,uCAAuC,uBAAuB;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA,oCAAoC,oBAAM;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA8C,6BAA6B,UAAU,gBAAgB;AACrG;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B;AAC9B;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,UAAU;AACnC;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qGAAqG,UAAU;AAC/G,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4DAA4D;AAC5D,kBAAkB,iBAAO;AACzB;AACA;AACA,mBAAmB,iBAAO;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA,0DAA0D,UAAU,uBAAuB;AAC3F;AACA,gBAAgB,iBAAO;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC,iBAAO;AAC1C,8BAA8B,iBAAO;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,iBAAO;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC,YAAK;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;;AChgBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACyB;AACE;AACI;AACJ;AACI;AACJ;AACI;AACkE;AACjG;;;;;;;;;;;;;;;;AChBA;AACA;AACA;AACA;AACA;;AAE+B;;AAE/B;AACA;AACA;AACA;AACA,WAAW,cAAc;AACzB,YAAY;AACZ;AACO;;AAEP;AACA;AACA,YAAY;AACZ;AACO,gCAAgC,UAAU;;AAEjD;AACA;AACA,WAAW,UAAU;AACrB,YAAY;AACZ;AACO,6BAA6B,UAAU;;AAE9C;AACA;AACA;AACA;AACA,WAAW,UAAU;AACrB,WAAW,UAAU;AACrB;AACO;AACP,kBAAkB,gBAAgB;AAClC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,WAAW,0BAA0B;AACrC,YAAY;AACZ;AACO;;AAEP;AACA;AACA;AACA;AACA;AACA,cAAc,iBAAiB;AAC/B;AACA,WAAW,KAAK;AAChB,WAAW,qCAAqC;AAChD,YAAY;AACZ;AACO;AACP,kBAAkB,gBAAgB;AAClC;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,cAAc,cAAc;AAC5B,WAAW,KAAK;AAChB,WAAW,kCAAkC;AAC7C,YAAY;AACZ;AACO;AACP,kBAAkB,gBAAgB;AAClC;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,WAAW,iBAAiB;AAC5B,WAAW,iBAAiB;AAC5B,YAAY;AACZ;AACO;;AAEP;AACA;AACA,WAAW,oBAAoB;AAC/B,YAAY;AACZ;AACO,4CAA4C,aAAa;;AAEhE;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,8BAA8B;AACzC,YAAY;AACZ;AACO;AACP;AACA,kBAAkB,SAAS;AAC3B;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,WAAW,UAAU;AACrB,WAAW,QAAQ;AACnB,WAAW,oCAAoC;AAC/C;AACO;;AAEA;;AAEP;AACA;AACA,WAAW,UAAU;AACrB,YAAY;AACZ;AACO;;AAEP;AACA;AACA;AACA,WAAW,cAAc;AACzB,WAAW,eAAe;AAC1B,YAAY;AACZ;AACO;AACP;AACA,YAAY;AACZ;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA,kBAAkB,gBAAgB;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,cAAc,gBAAgB;AAC9B,cAAc,uEAAuE;AACrF,WAAW,KAAK;AAChB,WAAW,QAAQ;AACnB,YAAY;AACZ;AACO;AACP;AACA,YAAY;AACZ;AACA;AACA,kBAAkB,gBAAgB;AAClC,+BAA+B,KAAK,2BAA2B,KAAK;AACpE;AACA,oBAAoB,KAAK;AACzB;;;;;;;;;;;;;;;;;ACvLA;AACA;AACA;AACA;AACA;;AAEmC;AACE;;AAErC;AACA;AACA;AACA,WAAW,iBAAiB;AAC5B,WAAW,YAAY;AACvB;AACO;AACP;AACA,WAAW,eAAe;AAC1B;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;;AAEO;;AAEP;AACA;AACA,WAAW,cAAc;AACzB,YAAY;AACZ;AACO;;AAEP;AACA;AACA;AACA,WAAW,GAAG;AACd,YAAY;AACZ;AACO;;AAEP;AACA;AACA;AACA,WAAW,GAAG;AACd,WAAW,GAAG;AACd,YAAY;AACZ;AACO;;AAEP;AACA;AACA;AACA,WAAW,iBAAiB;AAC5B,WAAW,iBAAiB;AAC5B,YAAY;AACZ;AACO,4JAA4J,UAAU;;AAE7K;;AAEA;AACA,WAAW,KAAK;AAChB,WAAW,KAAK;AAChB,YAAY;AACZ;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,cAAc;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,wDAAa,QAAQ,wDAAa;AAC5C;AACA;AACA;AACA,aAAa,6DAAkB;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,cAAc;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,cAAc,GAAG;AACjB;AACA,WAAW,GAAG;AACd,WAAW,aAAa;AACxB;AACA;AACO;AACP;;AAEO,gBAAgB,wDAAa;;AAEpC;AACA,WAAW,KAAK;AAChB,YAAY;AACZ;AACO;;AAEP;AACA,WAAW,KAAK;AAChB,YAAY;AACZ;AACO;;AAEP;AACA,cAAc,oCAAoC;AAClD,WAAW,KAAK;AAChB,WAAW,MAAM;AACjB,YAAY;AACZ;AACO;;AAEP;AACA,cAAc,oCAAoC;AAClD,WAAW,MAAM;AACjB;AACO;AACP;AACA,aAAa,KAAK;AAClB,cAAc;AACd;AACA;;;;;;;;;;;;;;;;ACzLA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACO;;AAEP;AACA;AACA;AACA;AACA;AACA,WAAW,UAAU;AACrB,YAAY;AACZ;AACO;AACP;AACA,wBAAwB,aAAa;AACrC;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,UAAU;AACxB,WAAW,KAAK;AAChB,WAAW,GAAG;AACd,WAAW,cAAc;AACzB,YAAY;AACZ;AACO;AACP;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,UAAU;AACrB,WAAW,iBAAiB;AAC5B,YAAY;AACZ;AACO;AACP;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,UAAU;AACrB,WAAW,uBAAuB;AAClC,YAAY;AACZ;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,UAAU;AACrB,WAAW,uBAAuB;AAClC,YAAY;AACZ;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;AC/GA;AACA;AACA;AACA;AACA;;AAEO;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEP;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB,YAAY,QAAQ;AACpB;AACO;;AAEP;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB,YAAY,QAAQ;AACpB;AACO;;AAEP;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB,YAAY,QAAQ;AACpB;AACO;;AAEA;;AAEA;AACP;AACA;AACA;AACA,WAAW,QAAQ;AACnB,YAAY;AACZ;AACO;;AAEA;;AAEP;AACA,WAAW,QAAQ;AACnB,YAAY,SAAS;AACrB;AACO;;;;;;;;;;;;;;;;;ACzDP;AACA;AACA;AACA;AACA;;AAEA;AACA,YAAY,oBAAoB;AAChC;AACO;;AAEP;AACA;AACA;AACO;;AAEP;AACA,WAAW,oBAAoB;AAC/B;AACO;;AAEP;AACA;AACA,YAAY,eAAe;AAC3B,WAAW,wBAAwB;AACnC;AACO;AACP;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,oBAAoB;AAC/B,WAAW,wBAAwB;AACnC,YAAY;AACZ;AACO;AACP;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,WAAW,oBAAoB;AAC/B,YAAY;AACZ;AACO;;AAEP;AACA,WAAW,oBAAoB;AAC/B,WAAW,8BAA8B;AACzC,YAAY;AACZ;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,WAAW,kBAAkB;AAC7B;AACO;AACP;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,WAAW,oBAAoB;AAC/B,WAAW,8BAA8B;AACzC,YAAY;AACZ;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,WAAW,KAAK;AAChB,WAAW,eAAe;AAC1B,YAAY;AACZ;AACO;;AAEP;AACA,WAAW,oBAAoB;AAC/B,WAAW,oBAAoB;AAC/B,YAAY;AACZ;AACO;;;;;;;;;;;;;;;AC3GP;AACA;AACA;AACA;AACA;;AAE+B;AACA;AACI;;AAEnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,uCAAuC;AACtD;AACO;AACP;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;;AAEA;AACA,gBAAgB,QAAQ;AACxB,aAAa,MAAM;AACnB,aAAa,cAAc;AAC3B;AACA;AACA,mDAAmD,QAAQ;AAC3D;AACA;;AAEA;AACA,gBAAgB,QAAQ;AACxB,aAAa,MAAM;AACnB,aAAa,cAAc;AAC3B;AACA;AACA;AACA,gBAAgB,QAAQ;AACxB;AACA;AACA,gCAAgC,KAAK;AACrC;AACA;AACA,6BAA6B,KAAK;AAClC;;AAEA;AACA,gBAAgB,QAAQ;AACxB,aAAa,MAAM;AACnB,aAAa,cAAc;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,QAAQ;AACxB,aAAa,MAAM;AACnB,aAAa,0BAA0B;AACvC;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA,cAAc;AACd;AACA,sBAAsB,qDAAU;AAChC;;AAEA;AACA,aAAa,GAAG;AAChB,aAAa,UAAU;AACvB;AACA;AACA,IAAI,6DAAkB,wBAAwB,qDAAU;AACxD;;AAEA;AACA,aAAa,GAAG;AAChB,aAAa,UAAU;AACvB;AACA;AACA;AACA,gBAAgB,QAAQ;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,aAAa,GAAG;AAChB,aAAa,UAAU;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,GAAG;AAChB,aAAa,YAAY;AACzB;AACA;AACA;AACA,WAAW,qDAAU,+BAA+B,qDAAU;AAC9D;;AAEA;AACA,sBAAsB,qDAAU;AAChC;AACA;AACA;;;;;;;;;;;;AChKA;AACA;AACA;AACA;AACA;;AAEO;;AAEP;AACA;AACA,WAAW,QAAQ;AACnB,YAAY;AACZ;AACO;;AAEP;AACA;AACA,WAAW,QAAQ;AACnB,YAAY;AACZ;AACO;AACP;;AAEA;AACA;AACA,WAAW,aAAa;AACxB,YAAY;AACZ;AACO;;;;;;;;;;;;AC5BP;AACA;AACA;AACA;AACA;;AAEqC;AACJ;;AAEjC;AACA;AACA;AACA,YAAY;AACZ;AACO;;AAEP;AACA;AACA;AACA,YAAY;AACZ;AACO;;AAEP;AACA;AACA;AACA,WAAW,QAAQ;AACnB,YAAY,QAAQ;AACpB;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/ydoc/lib/utils.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/y-protocols/awareness.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/ydoc/lib/ydocument.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/ydoc/lib/yfile.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/ydoc/lib/ycell.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/ydoc/lib/ynotebook.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@jupyter/ydoc/lib/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lib0/array.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lib0/function.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lib0/map.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lib0/math.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lib0/object.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lib0/observable.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lib0/set.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lib0/time.js"], "sourcesContent": ["/* -----------------------------------------------------------------------------\n| Copyright (c) Jupyter Development Team.\n| Distributed under the terms of the Modified BSD License.\n|----------------------------------------------------------------------------*/\nexport function convertYMapEventToMapChange(event) {\n    let changes = new Map();\n    event.changes.keys.forEach((event, key) => {\n        changes.set(key, {\n            action: event.action,\n            oldValue: event.oldValue,\n            newValue: this.ymeta.get(key)\n        });\n    });\n    return changes;\n}\n/**\n * Creates a mutual exclude function with the following property:\n *\n * ```js\n * const mutex = createMutex()\n * mutex(() => {\n *   // This function is immediately executed\n *   mutex(() => {\n *     // This function is not executed, as the mutex is already active.\n *   })\n * })\n * ```\n */\nexport const createMutex = () => {\n    let token = true;\n    return (f) => {\n        if (token) {\n            token = false;\n            try {\n                f();\n            }\n            finally {\n                token = true;\n            }\n        }\n    };\n};\n//# sourceMappingURL=utils.js.map", "/**\n * @module awareness-protocol\n */\n\nimport * as encoding from 'lib0/encoding'\nimport * as decoding from 'lib0/decoding'\nimport * as time from 'lib0/time'\nimport * as math from 'lib0/math'\nimport { Observable } from 'lib0/observable'\nimport * as f from 'lib0/function'\nimport * as Y from 'yjs' // eslint-disable-line\n\nexport const outdatedTimeout = 30000\n\n/**\n * @typedef {Object} MetaClientState\n * @property {number} MetaClientState.clock\n * @property {number} MetaClientState.lastUpdated unix timestamp\n */\n\n/**\n * The Awareness class implements a simple shared state protocol that can be used for non-persistent data like awareness information\n * (cursor, username, status, ..). Each client can update its own local state and listen to state changes of\n * remote clients. Every client may set a state of a remote peer to `null` to mark the client as offline.\n *\n * Each client is identified by a unique client id (something we borrow from `doc.clientID`). A client can override\n * its own state by propagating a message with an increasing timestamp (`clock`). If such a message is received, it is\n * applied if the known state of that client is older than the new state (`clock < newClock`). If a client thinks that\n * a remote client is offline, it may propagate a message with\n * `{ clock: currentClientClock, state: null, client: remoteClient }`. If such a\n * message is received, and the known clock of that client equals the received clock, it will override the state with `null`.\n *\n * Before a client disconnects, it should propagate a `null` state with an updated clock.\n *\n * Awareness states must be updated every 30 seconds. Otherwise the Awareness instance will delete the client state.\n *\n * @extends {Observable<string>}\n */\nexport class Awareness extends Observable {\n  /**\n   * @param {Y.Doc} doc\n   */\n  constructor (doc) {\n    super()\n    this.doc = doc\n    /**\n     * @type {number}\n     */\n    this.clientID = doc.clientID\n    /**\n     * Maps from client id to client state\n     * @type {Map<number, Object<string, any>>}\n     */\n    this.states = new Map()\n    /**\n     * @type {Map<number, MetaClientState>}\n     */\n    this.meta = new Map()\n    this._checkInterval = /** @type {any} */ (setInterval(() => {\n      const now = time.getUnixTime()\n      if (this.getLocalState() !== null && (outdatedTimeout / 2 <= now - /** @type {{lastUpdated:number}} */ (this.meta.get(this.clientID)).lastUpdated)) {\n        // renew local clock\n        this.setLocalState(this.getLocalState())\n      }\n      /**\n       * @type {Array<number>}\n       */\n      const remove = []\n      this.meta.forEach((meta, clientid) => {\n        if (clientid !== this.clientID && outdatedTimeout <= now - meta.lastUpdated && this.states.has(clientid)) {\n          remove.push(clientid)\n        }\n      })\n      if (remove.length > 0) {\n        removeAwarenessStates(this, remove, 'timeout')\n      }\n    }, math.floor(outdatedTimeout / 10)))\n    doc.on('destroy', () => {\n      this.destroy()\n    })\n    this.setLocalState({})\n  }\n\n  destroy () {\n    this.emit('destroy', [this])\n    this.setLocalState(null)\n    super.destroy()\n    clearInterval(this._checkInterval)\n  }\n\n  /**\n   * @return {Object<string,any>|null}\n   */\n  getLocalState () {\n    return this.states.get(this.clientID) || null\n  }\n\n  /**\n   * @param {Object<string,any>|null} state\n   */\n  setLocalState (state) {\n    const clientID = this.clientID\n    const currLocalMeta = this.meta.get(clientID)\n    const clock = currLocalMeta === undefined ? 0 : currLocalMeta.clock + 1\n    const prevState = this.states.get(clientID)\n    if (state === null) {\n      this.states.delete(clientID)\n    } else {\n      this.states.set(clientID, state)\n    }\n    this.meta.set(clientID, {\n      clock,\n      lastUpdated: time.getUnixTime()\n    })\n    const added = []\n    const updated = []\n    const filteredUpdated = []\n    const removed = []\n    if (state === null) {\n      removed.push(clientID)\n    } else if (prevState == null) {\n      if (state != null) {\n        added.push(clientID)\n      }\n    } else {\n      updated.push(clientID)\n      if (!f.equalityDeep(prevState, state)) {\n        filteredUpdated.push(clientID)\n      }\n    }\n    if (added.length > 0 || filteredUpdated.length > 0 || removed.length > 0) {\n      this.emit('change', [{ added, updated: filteredUpdated, removed }, 'local'])\n    }\n    this.emit('update', [{ added, updated, removed }, 'local'])\n  }\n\n  /**\n   * @param {string} field\n   * @param {any} value\n   */\n  setLocalStateField (field, value) {\n    const state = this.getLocalState()\n    if (state !== null) {\n      this.setLocalState({\n        ...state,\n        [field]: value\n      })\n    }\n  }\n\n  /**\n   * @return {Map<number,Object<string,any>>}\n   */\n  getStates () {\n    return this.states\n  }\n}\n\n/**\n * Mark (remote) clients as inactive and remove them from the list of active peers.\n * This change will be propagated to remote clients.\n *\n * @param {Awareness} awareness\n * @param {Array<number>} clients\n * @param {any} origin\n */\nexport const removeAwarenessStates = (awareness, clients, origin) => {\n  const removed = []\n  for (let i = 0; i < clients.length; i++) {\n    const clientID = clients[i]\n    if (awareness.states.has(clientID)) {\n      awareness.states.delete(clientID)\n      if (clientID === awareness.clientID) {\n        const curMeta = /** @type {MetaClientState} */ (awareness.meta.get(clientID))\n        awareness.meta.set(clientID, {\n          clock: curMeta.clock + 1,\n          lastUpdated: time.getUnixTime()\n        })\n      }\n      removed.push(clientID)\n    }\n  }\n  if (removed.length > 0) {\n    awareness.emit('change', [{ added: [], updated: [], removed }, origin])\n    awareness.emit('update', [{ added: [], updated: [], removed }, origin])\n  }\n}\n\n/**\n * @param {Awareness} awareness\n * @param {Array<number>} clients\n * @return {Uint8Array}\n */\nexport const encodeAwarenessUpdate = (awareness, clients, states = awareness.states) => {\n  const len = clients.length\n  const encoder = encoding.createEncoder()\n  encoding.writeVarUint(encoder, len)\n  for (let i = 0; i < len; i++) {\n    const clientID = clients[i]\n    const state = states.get(clientID) || null\n    const clock = /** @type {MetaClientState} */ (awareness.meta.get(clientID)).clock\n    encoding.writeVarUint(encoder, clientID)\n    encoding.writeVarUint(encoder, clock)\n    encoding.writeVarString(encoder, JSON.stringify(state))\n  }\n  return encoding.toUint8Array(encoder)\n}\n\n/**\n * Modify the content of an awareness update before re-encoding it to an awareness update.\n *\n * This might be useful when you have a central server that wants to ensure that clients\n * cant hijack somebody elses identity.\n *\n * @param {Uint8Array} update\n * @param {function(any):any} modify\n * @return {Uint8Array}\n */\nexport const modifyAwarenessUpdate = (update, modify) => {\n  const decoder = decoding.createDecoder(update)\n  const encoder = encoding.createEncoder()\n  const len = decoding.readVarUint(decoder)\n  encoding.writeVarUint(encoder, len)\n  for (let i = 0; i < len; i++) {\n    const clientID = decoding.readVarUint(decoder)\n    const clock = decoding.readVarUint(decoder)\n    const state = JSON.parse(decoding.readVarString(decoder))\n    const modifiedState = modify(state)\n    encoding.writeVarUint(encoder, clientID)\n    encoding.writeVarUint(encoder, clock)\n    encoding.writeVarString(encoder, JSON.stringify(modifiedState))\n  }\n  return encoding.toUint8Array(encoder)\n}\n\n/**\n * @param {Awareness} awareness\n * @param {Uint8Array} update\n * @param {any} origin This will be added to the emitted change event\n */\nexport const applyAwarenessUpdate = (awareness, update, origin) => {\n  const decoder = decoding.createDecoder(update)\n  const timestamp = time.getUnixTime()\n  const added = []\n  const updated = []\n  const filteredUpdated = []\n  const removed = []\n  const len = decoding.readVarUint(decoder)\n  for (let i = 0; i < len; i++) {\n    const clientID = decoding.readVarUint(decoder)\n    let clock = decoding.readVarUint(decoder)\n    const state = JSON.parse(decoding.readVarString(decoder))\n    const clientMeta = awareness.meta.get(clientID)\n    const prevState = awareness.states.get(clientID)\n    const currClock = clientMeta === undefined ? 0 : clientMeta.clock\n    if (currClock < clock || (currClock === clock && state === null && awareness.states.has(clientID))) {\n      if (state === null) {\n        // never let a remote client remove this local state\n        if (clientID === awareness.clientID && awareness.getLocalState() != null) {\n          // remote client removed the local state. Do not remote state. Broadcast a message indicating\n          // that this client still exists by increasing the clock\n          clock++\n        } else {\n          awareness.states.delete(clientID)\n        }\n      } else {\n        awareness.states.set(clientID, state)\n      }\n      awareness.meta.set(clientID, {\n        clock,\n        lastUpdated: timestamp\n      })\n      if (clientMeta === undefined && state !== null) {\n        added.push(clientID)\n      } else if (clientMeta !== undefined && state === null) {\n        removed.push(clientID)\n      } else if (state !== null) {\n        if (!f.equalityDeep(state, prevState)) {\n          filteredUpdated.push(clientID)\n        }\n        updated.push(clientID)\n      }\n    }\n  }\n  if (added.length > 0 || filteredUpdated.length > 0 || removed.length > 0) {\n    awareness.emit('change', [{\n      added, updated: filteredUpdated, removed\n    }, origin])\n  }\n  if (added.length > 0 || updated.length > 0 || removed.length > 0) {\n    awareness.emit('update', [{\n      added, updated, removed\n    }, origin])\n  }\n}\n", "/* -----------------------------------------------------------------------------\n| Copyright (c) Jupyter Development Team.\n| Distributed under the terms of the Modified BSD License.\n|----------------------------------------------------------------------------*/\nimport { JSONExt } from '@lumino/coreutils';\nimport { Signal } from '@lumino/signaling';\nimport { Awareness } from 'y-protocols/awareness';\nimport * as Y from 'yjs';\n/**\n * Generic shareable document.\n */\nexport class YDocument {\n    constructor(options) {\n        var _a;\n        /**\n         * Handle a change to the ystate.\n         */\n        this.onStateChanged = (event) => {\n            const stateChange = new Array();\n            event.keysChanged.forEach(key => {\n                const change = event.changes.keys.get(key);\n                if (change) {\n                    stateChange.push({\n                        name: key,\n                        oldValue: change.oldValue,\n                        newValue: this.ystate.get(key)\n                    });\n                }\n            });\n            this._changed.emit({ stateChange });\n        };\n        this._changed = new Signal(this);\n        this._isDisposed = false;\n        this._disposed = new Signal(this);\n        this._ydoc = (_a = options === null || options === void 0 ? void 0 : options.ydoc) !== null && _a !== void 0 ? _a : new Y.Doc();\n        this._ystate = this._ydoc.getMap('state');\n        this._undoManager = new Y.UndoManager([], {\n            trackedOrigins: new Set([this]),\n            doc: this._ydoc\n        });\n        this._awareness = new Awareness(this._ydoc);\n        this._ystate.observe(this.onStateChanged);\n    }\n    /**\n     * YJS document.\n     */\n    get ydoc() {\n        return this._ydoc;\n    }\n    /**\n     * Shared state\n     */\n    get ystate() {\n        return this._ystate;\n    }\n    /**\n     * YJS document undo manager\n     */\n    get undoManager() {\n        return this._undoManager;\n    }\n    /**\n     * Shared awareness\n     */\n    get awareness() {\n        return this._awareness;\n    }\n    /**\n     * The changed signal.\n     */\n    get changed() {\n        return this._changed;\n    }\n    /**\n     * A signal emitted when the document is disposed.\n     */\n    get disposed() {\n        return this._disposed;\n    }\n    /**\n     * Whether the document is disposed or not.\n     */\n    get isDisposed() {\n        return this._isDisposed;\n    }\n    /**\n     * Document state\n     */\n    get state() {\n        return JSONExt.deepCopy(this.ystate.toJSON());\n    }\n    /**\n     * Whether the object can undo changes.\n     */\n    canUndo() {\n        return this.undoManager.undoStack.length > 0;\n    }\n    /**\n     * Whether the object can redo changes.\n     */\n    canRedo() {\n        return this.undoManager.redoStack.length > 0;\n    }\n    /**\n     * Dispose of the resources.\n     */\n    dispose() {\n        if (this._isDisposed) {\n            return;\n        }\n        this._isDisposed = true;\n        this.ystate.unobserve(this.onStateChanged);\n        this.awareness.destroy();\n        this.undoManager.destroy();\n        this.ydoc.destroy();\n        this._disposed.emit();\n        Signal.clearData(this);\n    }\n    /**\n     * Get the value for a state attribute\n     *\n     * @param key Key to get\n     */\n    getState(key) {\n        const value = this.ystate.get(key);\n        return typeof value === 'undefined'\n            ? value\n            : JSONExt.deepCopy(value);\n    }\n    /**\n     * Set the value of a state attribute\n     *\n     * @param key Key to set\n     * @param value New attribute value\n     */\n    setState(key, value) {\n        if (!JSONExt.deepEqual(this.ystate.get(key), value)) {\n            this.ystate.set(key, value);\n        }\n    }\n    /**\n     * Get the document source\n     *\n     * @returns The source\n     */\n    get source() {\n        return this.getSource();\n    }\n    /**\n     * Set the document source\n     *\n     * @param value The source to set\n     */\n    set source(value) {\n        this.setSource(value);\n    }\n    /**\n     * Undo an operation.\n     */\n    undo() {\n        this.undoManager.undo();\n    }\n    /**\n     * Redo an operation.\n     */\n    redo() {\n        this.undoManager.redo();\n    }\n    /**\n     * Clear the change stack.\n     */\n    clearUndoHistory() {\n        this.undoManager.clear();\n    }\n    /**\n     * Perform a transaction. While the function f is called, all changes to the shared\n     * document are bundled into a single event.\n     */\n    transact(f, undoable = true, origin = null) {\n        this.ydoc.transact(f, undoable ? this : origin);\n    }\n}\n//# sourceMappingURL=ydocument.js.map", "/* -----------------------------------------------------------------------------\n| Copyright (c) Jupyter Development Team.\n| Distributed under the terms of the Modified BSD License.\n|----------------------------------------------------------------------------*/\nimport { YDocument } from './ydocument.js';\n/**\n * Shareable text file.\n */\nexport class YFile extends YDocument {\n    /**\n     * Create a new file\n     *\n     * #### Notes\n     * The document is empty and must be populated\n     */\n    constructor() {\n        super();\n        /**\n         * Document version\n         */\n        this.version = '1.0.0';\n        /**\n         * YJS file text.\n         */\n        this.ysource = this.ydoc.getText('source');\n        /**\n         * Handle a change to the ymodel.\n         */\n        this._modelObserver = (event) => {\n            this._changed.emit({ sourceChange: event.changes.delta });\n        };\n        this.undoManager.addToScope(this.ysource);\n        this.ysource.observe(this._modelObserver);\n    }\n    /**\n     * Creates a standalone YFile\n     */\n    static create() {\n        return new YFile();\n    }\n    /**\n     * File text\n     */\n    get source() {\n        return this.getSource();\n    }\n    set source(v) {\n        this.setSource(v);\n    }\n    /**\n     * Dispose of the resources.\n     */\n    dispose() {\n        if (this.isDisposed) {\n            return;\n        }\n        this.ysource.unobserve(this._modelObserver);\n        super.dispose();\n    }\n    /**\n     * Get the file text.\n     *\n     * @returns File text.\n     */\n    getSource() {\n        return this.ysource.toString();\n    }\n    /**\n     * Set the file text.\n     *\n     * @param value New text\n     */\n    setSource(value) {\n        this.transact(() => {\n            const ytext = this.ysource;\n            ytext.delete(0, ytext.length);\n            ytext.insert(0, value);\n        });\n    }\n    /**\n     * Replace content from `start' to `end` with `value`.\n     *\n     * @param start: The start index of the range to replace (inclusive).\n     * @param end: The end index of the range to replace (exclusive).\n     * @param value: New source (optional).\n     */\n    updateSource(start, end, value = '') {\n        this.transact(() => {\n            const ysource = this.ysource;\n            // insert and then delete.\n            // This ensures that the cursor position is adjusted after the replaced content.\n            ysource.insert(start, value);\n            ysource.delete(start + value.length, end - start);\n        });\n    }\n}\n//# sourceMappingURL=yfile.js.map", "/* -----------------------------------------------------------------------------\n| Copyright (c) Jupyter Development Team.\n| Distributed under the terms of the Modified BSD License.\n|----------------------------------------------------------------------------*/\nimport { JSONExt, UUID } from '@lumino/coreutils';\nimport { Signal } from '@lumino/signaling';\nimport { Awareness } from 'y-protocols/awareness';\nimport * as Y from 'yjs';\n/**\n * Create a new shared cell model given the YJS shared type.\n */\nexport const createCellModelFromSharedType = (type, options = {}) => {\n    switch (type.get('cell_type')) {\n        case 'code':\n            return new YCodeCell(type, type.get('source'), type.get('outputs'), options);\n        case 'markdown':\n            return new YMarkdownCell(type, type.get('source'), options);\n        case 'raw':\n            return new YRawCell(type, type.get('source'), options);\n        default:\n            throw new Error('Found unknown cell type');\n    }\n};\n/**\n * Create a new cell that can be inserted in an existing shared model.\n *\n * If no notebook is specified the cell will be standalone.\n *\n * @param cell Cell JSON representation\n * @param notebook Notebook to which the cell will be added\n */\nexport const createCell = (cell, notebook) => {\n    var _a, _b;\n    const ymodel = new Y.Map();\n    const ysource = new Y.Text();\n    const ymetadata = new Y.Map();\n    ymodel.set('source', ysource);\n    ymodel.set('metadata', ymetadata);\n    ymodel.set('cell_type', cell.cell_type);\n    ymodel.set('id', (_a = cell.id) !== null && _a !== void 0 ? _a : UUID.uuid4());\n    let ycell;\n    switch (cell.cell_type) {\n        case 'markdown': {\n            ycell = new YMarkdownCell(ymodel, ysource, { notebook }, ymetadata);\n            if (cell.attachments != null) {\n                ycell.setAttachments(cell.attachments);\n            }\n            break;\n        }\n        case 'code': {\n            const youtputs = new Y.Array();\n            ymodel.set('outputs', youtputs);\n            ycell = new YCodeCell(ymodel, ysource, youtputs, {\n                notebook\n            }, ymetadata);\n            const cCell = cell;\n            ycell.execution_count = (_b = cCell.execution_count) !== null && _b !== void 0 ? _b : null;\n            if (cCell.outputs) {\n                ycell.setOutputs(cCell.outputs);\n            }\n            break;\n        }\n        default: {\n            // raw\n            ycell = new YRawCell(ymodel, ysource, { notebook }, ymetadata);\n            if (cell.attachments) {\n                ycell.setAttachments(cell.attachments);\n            }\n            break;\n        }\n    }\n    if (cell.metadata != null) {\n        ycell.setMetadata(cell.metadata);\n    }\n    if (cell.source != null) {\n        ycell.setSource(typeof cell.source === 'string' ? cell.source : cell.source.join(''));\n    }\n    return ycell;\n};\n/**\n * Create a new cell that cannot be inserted in an existing shared model.\n *\n * @param cell Cell JSON representation\n */\nexport const createStandaloneCell = (cell) => createCell(cell);\nexport class YBaseCell {\n    /**\n     * Create a new YCell that works standalone. It cannot be\n     * inserted into a YNotebook because the Yjs model is already\n     * attached to an anonymous Y.Doc instance.\n     */\n    static create(id) {\n        return createCell({ id, cell_type: this.prototype.cell_type });\n    }\n    /**\n     * Base cell constructor\n     *\n     * ### Notes\n     * Don't use the constructor directly - prefer using ``YNotebook.insertCell``\n     *\n     * The ``ysource`` is needed because ``ymodel.get('source')`` will\n     * not return the real source if the model is not yet attached to\n     * a document. Requesting it explicitly allows to introspect a non-empty\n     * source before the cell is attached to the document.\n     *\n     * @param ymodel Cell map\n     * @param ysource Cell source\n     * @param options \\{ notebook?: The notebook the cell is attached to \\}\n     * @param ymetadata Cell metadata\n     */\n    constructor(ymodel, ysource, options = {}, ymetadata) {\n        /**\n         * Handle a change to the ymodel.\n         */\n        this._modelObserver = (events, transaction) => {\n            if (transaction.origin !== 'silent-change') {\n                this._changed.emit(this.getChanges(events));\n            }\n        };\n        this._metadataChanged = new Signal(this);\n        /**\n         * The notebook that this cell belongs to.\n         */\n        this._notebook = null;\n        this._changed = new Signal(this);\n        this._disposed = new Signal(this);\n        this._isDisposed = false;\n        this._undoManager = null;\n        this.ymodel = ymodel;\n        this._ysource = ysource;\n        this._ymetadata = ymetadata !== null && ymetadata !== void 0 ? ymetadata : this.ymodel.get('metadata');\n        this._prevSourceLength = ysource ? ysource.length : 0;\n        this._notebook = null;\n        this._awareness = null;\n        this._undoManager = null;\n        if (options.notebook) {\n            this._notebook = options.notebook;\n            if (this._notebook.disableDocumentWideUndoRedo) {\n                this._undoManager = new Y.UndoManager([this.ymodel], {\n                    trackedOrigins: new Set([this]),\n                    doc: this._notebook.ydoc\n                });\n            }\n        }\n        else {\n            // Standalone cell\n            const doc = new Y.Doc();\n            doc.getArray().insert(0, [this.ymodel]);\n            this._awareness = new Awareness(doc);\n            this._undoManager = new Y.UndoManager([this.ymodel], {\n                trackedOrigins: new Set([this])\n            });\n        }\n        this.ymodel.observeDeep(this._modelObserver);\n    }\n    /**\n     * Cell notebook awareness or null.\n     */\n    get awareness() {\n        var _a, _b, _c;\n        return (_c = (_a = this._awareness) !== null && _a !== void 0 ? _a : (_b = this.notebook) === null || _b === void 0 ? void 0 : _b.awareness) !== null && _c !== void 0 ? _c : null;\n    }\n    /**\n     * The type of the cell.\n     */\n    get cell_type() {\n        throw new Error('A YBaseCell must not be constructed');\n    }\n    /**\n     * The changed signal.\n     */\n    get changed() {\n        return this._changed;\n    }\n    /**\n     * Signal emitted when the cell is disposed.\n     */\n    get disposed() {\n        return this._disposed;\n    }\n    /**\n     * Cell id\n     */\n    get id() {\n        return this.getId();\n    }\n    /**\n     * Whether the model has been disposed or not.\n     */\n    get isDisposed() {\n        return this._isDisposed;\n    }\n    /**\n     * Whether the cell is standalone or not.\n     *\n     * If the cell is standalone. It cannot be\n     * inserted into a YNotebook because the Yjs model is already\n     * attached to an anonymous Y.Doc instance.\n     */\n    get isStandalone() {\n        return this._notebook !== null;\n    }\n    /**\n     * Cell metadata.\n     *\n     * #### Notes\n     * You should prefer to access and modify the specific key of interest.\n     */\n    get metadata() {\n        return this.getMetadata();\n    }\n    set metadata(v) {\n        this.setMetadata(v);\n    }\n    /**\n     * Signal triggered when the cell metadata changes.\n     */\n    get metadataChanged() {\n        return this._metadataChanged;\n    }\n    /**\n     * The notebook that this cell belongs to.\n     */\n    get notebook() {\n        return this._notebook;\n    }\n    /**\n     * Cell input content.\n     */\n    get source() {\n        return this.getSource();\n    }\n    set source(v) {\n        this.setSource(v);\n    }\n    /**\n     * The cell undo manager.\n     */\n    get undoManager() {\n        var _a;\n        if (!this.notebook) {\n            return this._undoManager;\n        }\n        return ((_a = this.notebook) === null || _a === void 0 ? void 0 : _a.disableDocumentWideUndoRedo)\n            ? this._undoManager\n            : this.notebook.undoManager;\n    }\n    get ysource() {\n        return this._ysource;\n    }\n    /**\n     * Whether the object can undo changes.\n     */\n    canUndo() {\n        return !!this.undoManager && this.undoManager.undoStack.length > 0;\n    }\n    /**\n     * Whether the object can redo changes.\n     */\n    canRedo() {\n        return !!this.undoManager && this.undoManager.redoStack.length > 0;\n    }\n    /**\n     * Clear the change stack.\n     */\n    clearUndoHistory() {\n        var _a;\n        (_a = this.undoManager) === null || _a === void 0 ? void 0 : _a.clear();\n    }\n    /**\n     * Undo an operation.\n     */\n    undo() {\n        var _a;\n        (_a = this.undoManager) === null || _a === void 0 ? void 0 : _a.undo();\n    }\n    /**\n     * Redo an operation.\n     */\n    redo() {\n        var _a;\n        (_a = this.undoManager) === null || _a === void 0 ? void 0 : _a.redo();\n    }\n    /**\n     * Dispose of the resources.\n     */\n    dispose() {\n        var _a;\n        if (this._isDisposed)\n            return;\n        this._isDisposed = true;\n        this.ymodel.unobserveDeep(this._modelObserver);\n        if (this._awareness) {\n            // A new document is created for standalone cell.\n            const doc = this._awareness.doc;\n            this._awareness.destroy();\n            doc.destroy();\n        }\n        if (this._undoManager) {\n            // Be sure to not destroy the document undo manager.\n            if (this._undoManager === ((_a = this.notebook) === null || _a === void 0 ? void 0 : _a.undoManager)) {\n                this._undoManager = null;\n            }\n            else {\n                this._undoManager.destroy();\n            }\n        }\n        this._disposed.emit();\n        Signal.clearData(this);\n    }\n    /**\n     * Get cell id.\n     *\n     * @returns Cell id\n     */\n    getId() {\n        return this.ymodel.get('id');\n    }\n    /**\n     * Gets cell's source.\n     *\n     * @returns Cell's source.\n     */\n    getSource() {\n        return this.ysource.toString();\n    }\n    /**\n     * Sets cell's source.\n     *\n     * @param value: New source.\n     */\n    setSource(value) {\n        this.transact(() => {\n            this.ysource.delete(0, this.ysource.length);\n            this.ysource.insert(0, value);\n        });\n        // @todo Do we need proper replace semantic? This leads to issues in editor bindings because they don't switch source.\n        // this.ymodel.set('source', new Y.Text(value));\n    }\n    /**\n     * Replace content from `start' to `end` with `value`.\n     *\n     * @param start: The start index of the range to replace (inclusive).\n     *\n     * @param end: The end index of the range to replace (exclusive).\n     *\n     * @param value: New source (optional).\n     */\n    updateSource(start, end, value = '') {\n        this.transact(() => {\n            const ysource = this.ysource;\n            // insert and then delete.\n            // This ensures that the cursor position is adjusted after the replaced content.\n            ysource.insert(start, value);\n            ysource.delete(start + value.length, end - start);\n        });\n    }\n    /**\n     * Delete a metadata cell.\n     *\n     * @param key The key to delete\n     */\n    deleteMetadata(key) {\n        if (typeof this.getMetadata(key) === 'undefined') {\n            return;\n        }\n        this.transact(() => {\n            this._ymetadata.delete(key);\n            const jupyter = this.getMetadata('jupyter');\n            if (key === 'collapsed' && jupyter) {\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                const { outputs_hidden, ...others } = jupyter;\n                if (Object.keys(others).length === 0) {\n                    this._ymetadata.delete('jupyter');\n                }\n                else {\n                    this._ymetadata.set('jupyter', others);\n                }\n            }\n            else if (key === 'jupyter') {\n                this._ymetadata.delete('collapsed');\n            }\n        }, false);\n    }\n    getMetadata(key) {\n        const metadata = this._ymetadata;\n        // Transiently the metadata can be missing - like during destruction\n        if (metadata === undefined) {\n            return undefined;\n        }\n        if (typeof key === 'string') {\n            const value = metadata.get(key);\n            return typeof value === 'undefined'\n                ? undefined // undefined is converted to `{}` by `JSONExt.deepCopy`\n                : JSONExt.deepCopy(metadata.get(key));\n        }\n        else {\n            return JSONExt.deepCopy(metadata.toJSON());\n        }\n    }\n    setMetadata(metadata, value) {\n        var _a, _b;\n        if (typeof metadata === 'string') {\n            if (typeof value === 'undefined') {\n                throw new TypeError(`Metadata value for ${metadata} cannot be 'undefined'; use deleteMetadata.`);\n            }\n            const key = metadata;\n            // Only set metadata if we change something to avoid infinite\n            // loop of signal changes.\n            if (JSONExt.deepEqual((_a = this.getMetadata(key)) !== null && _a !== void 0 ? _a : null, value)) {\n                return;\n            }\n            this.transact(() => {\n                var _a;\n                this._ymetadata.set(key, value);\n                if (key === 'collapsed') {\n                    const jupyter = ((_a = this.getMetadata('jupyter')) !== null && _a !== void 0 ? _a : {});\n                    if (jupyter.outputs_hidden !== value) {\n                        this.setMetadata('jupyter', {\n                            ...jupyter,\n                            outputs_hidden: value\n                        });\n                    }\n                }\n                else if (key === 'jupyter') {\n                    const isHidden = value['outputs_hidden'];\n                    if (typeof isHidden !== 'undefined') {\n                        if (this.getMetadata('collapsed') !== isHidden) {\n                            this.setMetadata('collapsed', isHidden);\n                        }\n                    }\n                    else {\n                        this.deleteMetadata('collapsed');\n                    }\n                }\n            }, false);\n        }\n        else {\n            const clone = JSONExt.deepCopy(metadata);\n            if (clone.collapsed != null) {\n                clone.jupyter = clone.jupyter || {};\n                clone.jupyter.outputs_hidden = clone.collapsed;\n            }\n            else if (((_b = clone === null || clone === void 0 ? void 0 : clone.jupyter) === null || _b === void 0 ? void 0 : _b.outputs_hidden) != null) {\n                clone.collapsed = clone.jupyter.outputs_hidden;\n            }\n            if (!JSONExt.deepEqual(clone, this.getMetadata())) {\n                this.transact(() => {\n                    for (const [key, value] of Object.entries(clone)) {\n                        this._ymetadata.set(key, value);\n                    }\n                }, false);\n            }\n        }\n    }\n    /**\n     * Serialize the model to JSON.\n     */\n    toJSON() {\n        return {\n            id: this.getId(),\n            cell_type: this.cell_type,\n            source: this.getSource(),\n            metadata: this.getMetadata()\n        };\n    }\n    /**\n     * Perform a transaction. While the function f is called, all changes to the shared\n     * document are bundled into a single event.\n     *\n     * @param f Transaction to execute\n     * @param undoable Whether to track the change in the action history or not (default `true`)\n     */\n    transact(f, undoable = true, origin = null) {\n        !this.notebook || this.notebook.disableDocumentWideUndoRedo\n            ? this.ymodel.doc == null\n                ? f()\n                : this.ymodel.doc.transact(f, undoable ? this : origin)\n            : this.notebook.transact(f, undoable);\n    }\n    /**\n     * Extract changes from YJS events\n     *\n     * @param events YJS events\n     * @returns Cell changes\n     */\n    getChanges(events) {\n        const changes = {};\n        const sourceEvent = events.find(event => event.target === this.ymodel.get('source'));\n        if (sourceEvent) {\n            changes.sourceChange = sourceEvent.changes.delta;\n        }\n        const metadataEvents = events.find(event => event.target === this._ymetadata);\n        if (metadataEvents) {\n            changes.metadataChange = metadataEvents.changes.keys;\n            metadataEvents.changes.keys.forEach((change, key) => {\n                switch (change.action) {\n                    case 'add':\n                        this._metadataChanged.emit({\n                            key,\n                            newValue: this._ymetadata.get(key),\n                            type: 'add'\n                        });\n                        break;\n                    case 'delete':\n                        this._metadataChanged.emit({\n                            key,\n                            oldValue: change.oldValue,\n                            type: 'remove'\n                        });\n                        break;\n                    case 'update':\n                        {\n                            const newValue = this._ymetadata.get(key);\n                            const oldValue = change.oldValue;\n                            let equal = true;\n                            if (typeof oldValue == 'object' && typeof newValue == 'object') {\n                                equal = JSONExt.deepEqual(oldValue, newValue);\n                            }\n                            else {\n                                equal = oldValue === newValue;\n                            }\n                            if (!equal) {\n                                this._metadataChanged.emit({\n                                    key,\n                                    type: 'change',\n                                    oldValue,\n                                    newValue\n                                });\n                            }\n                        }\n                        break;\n                }\n            });\n        }\n        const modelEvent = events.find(event => event.target === this.ymodel);\n        // The model allows us to replace the complete source with a new string. We express this in the Delta format\n        // as a replace of the complete string.\n        const ysource = this.ymodel.get('source');\n        if (modelEvent && modelEvent.keysChanged.has('source')) {\n            changes.sourceChange = [\n                { delete: this._prevSourceLength },\n                { insert: ysource.toString() }\n            ];\n        }\n        this._prevSourceLength = ysource.length;\n        return changes;\n    }\n}\n/**\n * Shareable code cell.\n */\nexport class YCodeCell extends YBaseCell {\n    /**\n     * Create a new YCodeCell that works standalone. It cannot be\n     * inserted into a YNotebook because the Yjs model is already\n     * attached to an anonymous Y.Doc instance.\n     */\n    static create(id) {\n        return super.create(id);\n    }\n    /**\n     * Code cell constructor\n     *\n     * ### Notes\n     * Don't use the constructor directly - prefer using ``YNotebook.insertCell``\n     *\n     * The ``ysource`` is needed because ``ymodel.get('source')`` will\n     * not return the real source if the model is not yet attached to\n     * a document. Requesting it explicitly allows to introspect a non-empty\n     * source before the cell is attached to the document.\n     *\n     * @param ymodel Cell map\n     * @param ysource Cell source\n     * @param youtputs Code cell outputs\n     * @param options \\{ notebook?: The notebook the cell is attached to \\}\n     * @param ymetadata Cell metadata\n     */\n    constructor(ymodel, ysource, youtputs, options = {}, ymetadata) {\n        super(ymodel, ysource, options, ymetadata);\n        this._youtputs = youtputs;\n    }\n    /**\n     * The type of the cell.\n     */\n    get cell_type() {\n        return 'code';\n    }\n    /**\n     * The code cell's prompt number. Will be null if the cell has not been run.\n     */\n    get execution_count() {\n        return this.ymodel.get('execution_count') || null;\n    }\n    set execution_count(count) {\n        // Do not use `this.execution_count`. When initializing the\n        // cell, we need to set execution_count to `null` if we compare\n        // using `this.execution_count` it will return `null` and we will\n        // never initialize it\n        if (this.ymodel.get('execution_count') !== count) {\n            this.transact(() => {\n                this.ymodel.set('execution_count', count);\n            }, false);\n        }\n    }\n    /**\n     * The code cell's execution state.\n     */\n    get executionState() {\n        var _a;\n        return (_a = this.ymodel.get('execution_state')) !== null && _a !== void 0 ? _a : 'idle';\n    }\n    set executionState(state) {\n        if (this.ymodel.get('execution_state') !== state) {\n            this.transact(() => {\n                this.ymodel.set('execution_state', state);\n            }, false);\n        }\n    }\n    /**\n     * Cell outputs.\n     */\n    get outputs() {\n        return this.getOutputs();\n    }\n    set outputs(v) {\n        this.setOutputs(v);\n    }\n    get youtputs() {\n        return this._youtputs;\n    }\n    /**\n     * Execution, display, or stream outputs.\n     */\n    getOutputs() {\n        return JSONExt.deepCopy(this._youtputs.toJSON());\n    }\n    createOutputs(outputs) {\n        const newOutputs = [];\n        for (const output of JSONExt.deepCopy(outputs)) {\n            let _newOutput1;\n            if (output.output_type === 'stream') {\n                // Set the text field as a Y.Text\n                const { text, ...outputWithoutText } = output;\n                _newOutput1 = outputWithoutText;\n                const newText = new Y.Text();\n                let _text = text instanceof Array ? text.join() : text;\n                newText.insert(0, _text);\n                _newOutput1['text'] = newText;\n            }\n            else {\n                _newOutput1 = output;\n            }\n            const _newOutput2 = [];\n            for (const [key, value] of Object.entries(_newOutput1)) {\n                _newOutput2.push([key, value]);\n            }\n            const newOutput = new Y.Map(_newOutput2);\n            newOutputs.push(newOutput);\n        }\n        return newOutputs;\n    }\n    /**\n     * Replace all outputs.\n     */\n    setOutputs(outputs) {\n        this.transact(() => {\n            this._youtputs.delete(0, this._youtputs.length);\n            const newOutputs = this.createOutputs(outputs);\n            this._youtputs.insert(0, newOutputs);\n        }, false);\n    }\n    /**\n     * Remove text from a stream output.\n     */\n    removeStreamOutput(index, start, origin = null) {\n        this.transact(() => {\n            const output = this._youtputs.get(index);\n            const prevText = output.get('text');\n            const length = prevText.length - start;\n            prevText.delete(start, length);\n        }, false, origin);\n    }\n    /**\n     * Append text to a stream output.\n     */\n    appendStreamOutput(index, text, origin = null) {\n        this.transact(() => {\n            const output = this._youtputs.get(index);\n            const prevText = output.get('text');\n            prevText.insert(prevText.length, text);\n        }, false, origin);\n    }\n    /**\n     * Replace content from `start' to `end` with `outputs`.\n     *\n     * @param start: The start index of the range to replace (inclusive).\n     *\n     * @param end: The end index of the range to replace (exclusive).\n     *\n     * @param outputs: New outputs (optional).\n     */\n    updateOutputs(start, end, outputs = [], origin = null) {\n        const fin = end < this._youtputs.length ? end - start : this._youtputs.length - start;\n        this.transact(() => {\n            this._youtputs.delete(start, fin);\n            const newOutputs = this.createOutputs(outputs);\n            this._youtputs.insert(start, newOutputs);\n        }, false, origin);\n    }\n    /**\n     * Serialize the model to JSON.\n     */\n    toJSON() {\n        return {\n            ...super.toJSON(),\n            outputs: this.getOutputs(),\n            execution_count: this.execution_count\n        };\n    }\n    /**\n     * Extract changes from YJS events\n     *\n     * @param events YJS events\n     * @returns Cell changes\n     */\n    getChanges(events) {\n        const changes = super.getChanges(events);\n        const streamOutputEvent = events.find(\n        // Changes to the 'text' of a cell's stream output can be accessed like so:\n        // ycell['outputs'][output_idx]['text']\n        // This translates to an event path of: ['outputs', output_idx, 'text]\n        event => event.path.length === 3 &&\n            event.path[0] === 'outputs' &&\n            event.path[2] === 'text');\n        if (streamOutputEvent) {\n            changes.streamOutputChange = streamOutputEvent.changes.delta;\n        }\n        const outputEvent = events.find(event => event.target === this.ymodel.get('outputs'));\n        if (outputEvent) {\n            changes.outputsChange = outputEvent.changes.delta;\n        }\n        const modelEvent = events.find(event => event.target === this.ymodel);\n        if (modelEvent && modelEvent.keysChanged.has('execution_count')) {\n            const change = modelEvent.changes.keys.get('execution_count');\n            changes.executionCountChange = {\n                oldValue: change.oldValue,\n                newValue: this.ymodel.get('execution_count')\n            };\n        }\n        if (modelEvent && modelEvent.keysChanged.has('execution_state')) {\n            const change = modelEvent.changes.keys.get('execution_state');\n            changes.executionStateChange = {\n                oldValue: change.oldValue,\n                newValue: this.ymodel.get('execution_state')\n            };\n        }\n        return changes;\n    }\n}\nclass YAttachmentCell extends YBaseCell {\n    /**\n     * Cell attachments\n     */\n    get attachments() {\n        return this.getAttachments();\n    }\n    set attachments(v) {\n        this.setAttachments(v);\n    }\n    /**\n     * Gets the cell attachments.\n     *\n     * @returns The cell attachments.\n     */\n    getAttachments() {\n        return this.ymodel.get('attachments');\n    }\n    /**\n     * Sets the cell attachments\n     *\n     * @param attachments: The cell attachments.\n     */\n    setAttachments(attachments) {\n        this.transact(() => {\n            if (attachments == null) {\n                this.ymodel.delete('attachments');\n            }\n            else {\n                this.ymodel.set('attachments', attachments);\n            }\n        }, false);\n    }\n    /**\n     * Extract changes from YJS events\n     *\n     * @param events YJS events\n     * @returns Cell changes\n     */\n    getChanges(events) {\n        const changes = super.getChanges(events);\n        const modelEvent = events.find(event => event.target === this.ymodel);\n        if (modelEvent && modelEvent.keysChanged.has('attachments')) {\n            const change = modelEvent.changes.keys.get('attachments');\n            changes.attachmentsChange = {\n                oldValue: change.oldValue,\n                newValue: this.ymodel.get('attachments')\n            };\n        }\n        return changes;\n    }\n}\n/**\n * Shareable raw cell.\n */\nexport class YRawCell extends YAttachmentCell {\n    /**\n     * Create a new YRawCell that works standalone. It cannot be\n     * inserted into a YNotebook because the Yjs model is already\n     * attached to an anonymous Y.Doc instance.\n     */\n    static create(id) {\n        return super.create(id);\n    }\n    /**\n     * String identifying the type of cell.\n     */\n    get cell_type() {\n        return 'raw';\n    }\n    /**\n     * Serialize the model to JSON.\n     */\n    toJSON() {\n        return {\n            id: this.getId(),\n            cell_type: 'raw',\n            source: this.getSource(),\n            metadata: this.getMetadata(),\n            attachments: this.getAttachments()\n        };\n    }\n}\n/**\n * Shareable markdown cell.\n */\nexport class YMarkdownCell extends YAttachmentCell {\n    /**\n     * Create a new YMarkdownCell that works standalone. It cannot be\n     * inserted into a YNotebook because the Yjs model is already\n     * attached to an anonymous Y.Doc instance.\n     */\n    static create(id) {\n        return super.create(id);\n    }\n    /**\n     * String identifying the type of cell.\n     */\n    get cell_type() {\n        return 'markdown';\n    }\n    /**\n     * Serialize the model to JSON.\n     */\n    toJSON() {\n        return {\n            id: this.getId(),\n            cell_type: 'markdown',\n            source: this.getSource(),\n            metadata: this.getMetadata(),\n            attachments: this.getAttachments()\n        };\n    }\n}\n//# sourceMappingURL=ycell.js.map", "/* -----------------------------------------------------------------------------\n| Copyright (c) Jupyter Development Team.\n| Distributed under the terms of the Modified BSD License.\n|----------------------------------------------------------------------------*/\nimport { JSONExt } from '@lumino/coreutils';\nimport { Signal } from '@lumino/signaling';\nimport * as Y from 'yjs';\nimport { YDocument } from './ydocument.js';\nimport { createCell, createCellModelFromSharedType } from './ycell.js';\n/**\n * Shared implementation of the Shared Document types.\n *\n * Shared cells can be inserted into a SharedNotebook.\n * Shared cells only start emitting events when they are connected to a SharedNotebook.\n *\n * \"Standalone\" cells must not be inserted into a (Shared)Notebook.\n * Standalone cells emit events immediately after they have been created, but they must not\n * be included into a (Shared)Notebook.\n */\nexport class YNotebook extends YDocument {\n    /**\n     * Create a new notebook\n     *\n     * #### Notes\n     * The document is empty and must be populated\n     *\n     * @param options\n     */\n    constructor(options = {}) {\n        var _a;\n        super();\n        /**\n         * Document version\n         */\n        this.version = '2.0.0';\n        /**\n         * YJS map for the notebook metadata\n         */\n        this.ymeta = this.ydoc.getMap('meta');\n        /**\n         * Handle a change to the ystate.\n         */\n        this._onMetaChanged = (events) => {\n            const metadataEvents = events.find(event => event.target === this.ymeta.get('metadata'));\n            if (metadataEvents) {\n                const metadataChange = metadataEvents.changes.keys;\n                const ymetadata = this.ymeta.get('metadata');\n                metadataEvents.changes.keys.forEach((change, key) => {\n                    switch (change.action) {\n                        case 'add':\n                            this._metadataChanged.emit({\n                                key,\n                                type: 'add',\n                                newValue: ymetadata.get(key)\n                            });\n                            break;\n                        case 'delete':\n                            this._metadataChanged.emit({\n                                key,\n                                type: 'remove',\n                                oldValue: change.oldValue\n                            });\n                            break;\n                        case 'update':\n                            {\n                                const newValue = ymetadata.get(key);\n                                const oldValue = change.oldValue;\n                                let equal = true;\n                                if (typeof oldValue == 'object' && typeof newValue == 'object') {\n                                    equal = JSONExt.deepEqual(oldValue, newValue);\n                                }\n                                else {\n                                    equal = oldValue === newValue;\n                                }\n                                if (!equal) {\n                                    this._metadataChanged.emit({\n                                        key,\n                                        type: 'change',\n                                        oldValue,\n                                        newValue\n                                    });\n                                }\n                            }\n                            break;\n                    }\n                });\n                this._changed.emit({ metadataChange });\n            }\n            const metaEvent = events.find(event => event.target === this.ymeta);\n            if (!metaEvent) {\n                return;\n            }\n            if (metaEvent.keysChanged.has('metadata')) {\n                // Handle metadata change when adding/removing the YMap\n                const change = metaEvent.changes.keys.get('metadata');\n                if ((change === null || change === void 0 ? void 0 : change.action) === 'add' && !change.oldValue) {\n                    const metadataChange = new Map();\n                    for (const key of Object.keys(this.metadata)) {\n                        metadataChange.set(key, {\n                            action: 'add',\n                            oldValue: undefined\n                        });\n                        this._metadataChanged.emit({\n                            key,\n                            type: 'add',\n                            newValue: this.getMetadata(key)\n                        });\n                    }\n                    this._changed.emit({ metadataChange });\n                }\n            }\n            if (metaEvent.keysChanged.has('nbformat')) {\n                const change = metaEvent.changes.keys.get('nbformat');\n                const nbformatChanged = {\n                    key: 'nbformat',\n                    oldValue: (change === null || change === void 0 ? void 0 : change.oldValue) ? change.oldValue : undefined,\n                    newValue: this.nbformat\n                };\n                this._changed.emit({ nbformatChanged });\n            }\n            if (metaEvent.keysChanged.has('nbformat_minor')) {\n                const change = metaEvent.changes.keys.get('nbformat_minor');\n                const nbformatChanged = {\n                    key: 'nbformat_minor',\n                    oldValue: (change === null || change === void 0 ? void 0 : change.oldValue) ? change.oldValue : undefined,\n                    newValue: this.nbformat_minor\n                };\n                this._changed.emit({ nbformatChanged });\n            }\n        };\n        /**\n         * Handle a change to the list of cells.\n         */\n        this._onYCellsChanged = (event) => {\n            // update the type cell mapping by iterating through the added/removed types\n            event.changes.added.forEach(item => {\n                const type = item.content.type;\n                if (!this._ycellMapping.has(type)) {\n                    const c = createCellModelFromSharedType(type, { notebook: this });\n                    this._ycellMapping.set(type, c);\n                }\n            });\n            event.changes.deleted.forEach(item => {\n                const type = item.content.type;\n                const model = this._ycellMapping.get(type);\n                if (model) {\n                    model.dispose();\n                    this._ycellMapping.delete(type);\n                }\n            });\n            let index = 0;\n            // this reflects the event.changes.delta, but replaces the content of delta.insert with ycells\n            const cellsChange = [];\n            event.changes.delta.forEach((d) => {\n                if (d.insert != null) {\n                    const insertedCells = d.insert.map((ycell) => this._ycellMapping.get(ycell));\n                    cellsChange.push({ insert: insertedCells });\n                    this.cells.splice(index, 0, ...insertedCells);\n                    index += d.insert.length;\n                }\n                else if (d.delete != null) {\n                    cellsChange.push(d);\n                    this.cells.splice(index, d.delete);\n                }\n                else if (d.retain != null) {\n                    cellsChange.push(d);\n                    index += d.retain;\n                }\n            });\n            this._changed.emit({\n                cellsChange: cellsChange\n            });\n        };\n        this._metadataChanged = new Signal(this);\n        /**\n         * Internal Yjs cells list\n         */\n        this._ycells = this.ydoc.getArray('cells');\n        this._ycellMapping = new WeakMap();\n        this._disableDocumentWideUndoRedo =\n            (_a = options.disableDocumentWideUndoRedo) !== null && _a !== void 0 ? _a : false;\n        this.cells = this._ycells.toArray().map(ycell => {\n            if (!this._ycellMapping.has(ycell)) {\n                this._ycellMapping.set(ycell, createCellModelFromSharedType(ycell, { notebook: this }));\n            }\n            return this._ycellMapping.get(ycell);\n        });\n        this.undoManager.addToScope(this._ycells);\n        this._ycells.observe(this._onYCellsChanged);\n        this.ymeta.observeDeep(this._onMetaChanged);\n    }\n    /**\n     * Creates a standalone YNotebook\n     *\n     * Note: This method is useful when we need to initialize\n     * the YNotebook from the JavaScript side.\n     */\n    static create(options = {}) {\n        var _a, _b, _c, _d, _e, _f, _g, _h, _j;\n        const ynotebook = new YNotebook({\n            disableDocumentWideUndoRedo: (_a = options.disableDocumentWideUndoRedo) !== null && _a !== void 0 ? _a : false\n        });\n        const data = {\n            cells: (_c = (_b = options.data) === null || _b === void 0 ? void 0 : _b.cells) !== null && _c !== void 0 ? _c : [],\n            nbformat: (_e = (_d = options.data) === null || _d === void 0 ? void 0 : _d.nbformat) !== null && _e !== void 0 ? _e : 4,\n            nbformat_minor: (_g = (_f = options.data) === null || _f === void 0 ? void 0 : _f.nbformat_minor) !== null && _g !== void 0 ? _g : 5,\n            metadata: (_j = (_h = options.data) === null || _h === void 0 ? void 0 : _h.metadata) !== null && _j !== void 0 ? _j : {}\n        };\n        ynotebook.fromJSON(data);\n        return ynotebook;\n    }\n    /**\n     * Wether the undo/redo logic should be\n     * considered on the full document across all cells.\n     *\n     * Default: false\n     */\n    get disableDocumentWideUndoRedo() {\n        return this._disableDocumentWideUndoRedo;\n    }\n    /**\n     * Notebook metadata\n     */\n    get metadata() {\n        return this.getMetadata();\n    }\n    set metadata(v) {\n        this.setMetadata(v);\n    }\n    /**\n     * Signal triggered when a metadata changes.\n     */\n    get metadataChanged() {\n        return this._metadataChanged;\n    }\n    /**\n     * nbformat major version\n     */\n    get nbformat() {\n        return this.ymeta.get('nbformat');\n    }\n    set nbformat(value) {\n        this.transact(() => {\n            this.ymeta.set('nbformat', value);\n        }, false);\n    }\n    /**\n     * nbformat minor version\n     */\n    get nbformat_minor() {\n        return this.ymeta.get('nbformat_minor');\n    }\n    set nbformat_minor(value) {\n        this.transact(() => {\n            this.ymeta.set('nbformat_minor', value);\n        }, false);\n    }\n    /**\n     * Dispose of the resources.\n     */\n    dispose() {\n        if (this.isDisposed) {\n            return;\n        }\n        this._ycells.unobserve(this._onYCellsChanged);\n        this.ymeta.unobserveDeep(this._onMetaChanged);\n        super.dispose();\n    }\n    /**\n     * Get a shared cell by index.\n     *\n     * @param index: Cell's position.\n     *\n     * @returns The requested shared cell.\n     */\n    getCell(index) {\n        return this.cells[index];\n    }\n    /**\n     * Add a shared cell at the notebook bottom.\n     *\n     * @param cell Cell to add.\n     *\n     * @returns The added cell.\n     */\n    addCell(cell) {\n        return this.insertCell(this._ycells.length, cell);\n    }\n    /**\n     * Insert a shared cell into a specific position.\n     *\n     * @param index: Cell's position.\n     * @param cell: Cell to insert.\n     *\n     * @returns The inserted cell.\n     */\n    insertCell(index, cell) {\n        return this.insertCells(index, [cell])[0];\n    }\n    /**\n     * Insert a list of shared cells into a specific position.\n     *\n     * @param index: Position to insert the cells.\n     * @param cells: Array of shared cells to insert.\n     *\n     * @returns The inserted cells.\n     */\n    insertCells(index, cells) {\n        const yCells = cells.map(c => {\n            const cell = createCell(c, this);\n            this._ycellMapping.set(cell.ymodel, cell);\n            return cell;\n        });\n        this.transact(() => {\n            this._ycells.insert(index, yCells.map(cell => cell.ymodel));\n        });\n        return yCells;\n    }\n    /**\n     * Move a cell.\n     *\n     * @param fromIndex: Index of the cell to move.\n     * @param toIndex: New position of the cell.\n     */\n    moveCell(fromIndex, toIndex) {\n        this.moveCells(fromIndex, toIndex);\n    }\n    /**\n     * Move cells.\n     *\n     * @param fromIndex: Index of the first cells to move.\n     * @param toIndex: New position of the first cell (in the current array).\n     * @param n: Number of cells to move (default 1)\n     */\n    moveCells(fromIndex, toIndex, n = 1) {\n        // FIXME we need to use yjs move feature to preserve undo history\n        const clones = new Array(n)\n            .fill(true)\n            .map((_, idx) => this.getCell(fromIndex + idx).toJSON());\n        this.transact(() => {\n            this._ycells.delete(fromIndex, n);\n            this._ycells.insert(fromIndex > toIndex ? toIndex : toIndex - n + 1, clones.map(clone => createCell(clone, this).ymodel));\n        });\n    }\n    /**\n     * Remove a cell.\n     *\n     * @param index: Index of the cell to remove.\n     */\n    deleteCell(index) {\n        this.deleteCellRange(index, index + 1);\n    }\n    /**\n     * Remove a range of cells.\n     *\n     * @param from: The start index of the range to remove (inclusive).\n     * @param to: The end index of the range to remove (exclusive).\n     */\n    deleteCellRange(from, to) {\n        // Cells will be removed from the mapping in the model event listener.\n        this.transact(() => {\n            this._ycells.delete(from, to - from);\n        });\n    }\n    /**\n     * Delete a metadata notebook.\n     *\n     * @param key The key to delete\n     */\n    deleteMetadata(key) {\n        if (typeof this.getMetadata(key) === 'undefined') {\n            return;\n        }\n        const allMetadata = this.metadata;\n        delete allMetadata[key];\n        this.setMetadata(allMetadata);\n    }\n    getMetadata(key) {\n        const ymetadata = this.ymeta.get('metadata');\n        // Transiently the metadata can be missing - like during destruction\n        if (ymetadata === undefined) {\n            return undefined;\n        }\n        if (typeof key === 'string') {\n            const value = ymetadata.get(key);\n            return typeof value === 'undefined'\n                ? undefined // undefined is converted to `{}` by `JSONExt.deepCopy`\n                : JSONExt.deepCopy(value);\n        }\n        else {\n            return JSONExt.deepCopy(ymetadata.toJSON());\n        }\n    }\n    setMetadata(metadata, value) {\n        var _a;\n        if (typeof metadata === 'string') {\n            if (typeof value === 'undefined') {\n                throw new TypeError(`Metadata value for ${metadata} cannot be 'undefined'; use deleteMetadata.`);\n            }\n            if (JSONExt.deepEqual((_a = this.getMetadata(metadata)) !== null && _a !== void 0 ? _a : null, value)) {\n                return;\n            }\n            const update = {};\n            update[metadata] = value;\n            this.updateMetadata(update);\n        }\n        else {\n            if (!this.metadata || !JSONExt.deepEqual(this.metadata, metadata)) {\n                const clone = JSONExt.deepCopy(metadata);\n                const ymetadata = this.ymeta.get('metadata');\n                // Transiently the metadata can be missing - like during destruction\n                if (ymetadata === undefined) {\n                    return undefined;\n                }\n                this.transact(() => {\n                    ymetadata.clear();\n                    for (const [key, value] of Object.entries(clone)) {\n                        ymetadata.set(key, value);\n                    }\n                });\n            }\n        }\n    }\n    /**\n     * Updates the metadata associated with the notebook.\n     *\n     * @param value: Metadata's attribute to update.\n     */\n    updateMetadata(value) {\n        // TODO: Maybe modify only attributes instead of replacing the whole metadata?\n        const clone = JSONExt.deepCopy(value);\n        const ymetadata = this.ymeta.get('metadata');\n        // Transiently the metadata can be missing - like during destruction\n        if (ymetadata === undefined) {\n            return undefined;\n        }\n        this.transact(() => {\n            for (const [key, value] of Object.entries(clone)) {\n                ymetadata.set(key, value);\n            }\n        });\n    }\n    /**\n     * Get the notebook source\n     *\n     * @returns The notebook\n     */\n    getSource() {\n        return this.toJSON();\n    }\n    /**\n     * Set the notebook source\n     *\n     * @param value The notebook\n     */\n    setSource(value) {\n        this.fromJSON(value);\n    }\n    /**\n     * Override the notebook with a JSON-serialized document.\n     *\n     * @param value The notebook\n     */\n    fromJSON(value) {\n        this.transact(() => {\n            this.nbformat = value.nbformat;\n            this.nbformat_minor = value.nbformat_minor;\n            const metadata = value.metadata;\n            if (metadata['orig_nbformat'] !== undefined) {\n                delete metadata['orig_nbformat'];\n            }\n            if (!this.metadata) {\n                const ymetadata = new Y.Map();\n                for (const [key, value] of Object.entries(metadata)) {\n                    ymetadata.set(key, value);\n                }\n                this.ymeta.set('metadata', ymetadata);\n            }\n            else {\n                this.metadata = metadata;\n            }\n            const useId = value.nbformat === 4 && value.nbformat_minor >= 5;\n            const ycells = value.cells.map(cell => {\n                if (!useId) {\n                    delete cell.id;\n                }\n                return cell;\n            });\n            this.insertCells(this.cells.length, ycells);\n            this.deleteCellRange(0, this.cells.length);\n        });\n    }\n    /**\n     * Serialize the model to JSON.\n     */\n    toJSON() {\n        // strip cell ids if we have notebook format 4.0-4.4\n        const pruneCellId = this.nbformat === 4 && this.nbformat_minor <= 4;\n        return {\n            metadata: this.metadata,\n            nbformat_minor: this.nbformat_minor,\n            nbformat: this.nbformat,\n            cells: this.cells.map(c => {\n                const raw = c.toJSON();\n                if (pruneCellId) {\n                    delete raw.id;\n                }\n                return raw;\n            })\n        };\n    }\n}\n//# sourceMappingURL=ynotebook.js.map", "/* -----------------------------------------------------------------------------\n| Copyright (c) Jupyter Development Team.\n| Distributed under the terms of the Modified BSD License.\n|----------------------------------------------------------------------------*/\n/**\n * @packageDocumentation\n * @module ydoc\n */\nexport * from './api.js';\nexport * from './utils.js';\nexport * from './awareness.js';\nexport * from './ytext.js';\nexport * from './ydocument.js';\nexport * from './yfile.js';\nexport * from './ynotebook.js';\nexport { YB<PERSON><PERSON><PERSON>, YRaw<PERSON>ell, <PERSON><PERSON><PERSON>down<PERSON>ell, <PERSON><PERSON><PERSON><PERSON><PERSON>, createStandalone<PERSON>ell } from './ycell.js';\n//# sourceMappingURL=index.js.map", "/**\n * Utility module to work with Arrays.\n *\n * @module array\n */\n\nimport * as set from './set.js'\n\n/**\n * Return the last element of an array. The element must exist\n *\n * @template L\n * @param {ArrayLike<L>} arr\n * @return {L}\n */\nexport const last = arr => arr[arr.length - 1]\n\n/**\n * @template C\n * @return {Array<C>}\n */\nexport const create = () => /** @type {Array<C>} */ ([])\n\n/**\n * @template D\n * @param {Array<D>} a\n * @return {Array<D>}\n */\nexport const copy = a => /** @type {Array<D>} */ (a.slice())\n\n/**\n * Append elements from src to dest\n *\n * @template M\n * @param {Array<M>} dest\n * @param {Array<M>} src\n */\nexport const appendTo = (dest, src) => {\n  for (let i = 0; i < src.length; i++) {\n    dest.push(src[i])\n  }\n}\n\n/**\n * Transforms something array-like to an actual Array.\n *\n * @function\n * @template T\n * @param {ArrayLike<T>|Iterable<T>} arraylike\n * @return {T}\n */\nexport const from = Array.from\n\n/**\n * True iff condition holds on every element in the Array.\n *\n * @function\n * @template ITEM\n * @template {ArrayLike<ITEM>} ARR\n *\n * @param {ARR} arr\n * @param {function(ITEM, number, ARR):boolean} f\n * @return {boolean}\n */\nexport const every = (arr, f) => {\n  for (let i = 0; i < arr.length; i++) {\n    if (!f(arr[i], i, arr)) {\n      return false\n    }\n  }\n  return true\n}\n\n/**\n * True iff condition holds on some element in the Array.\n *\n * @function\n * @template S\n * @template {ArrayLike<S>} ARR\n * @param {ARR} arr\n * @param {function(S, number, ARR):boolean} f\n * @return {boolean}\n */\nexport const some = (arr, f) => {\n  for (let i = 0; i < arr.length; i++) {\n    if (f(arr[i], i, arr)) {\n      return true\n    }\n  }\n  return false\n}\n\n/**\n * @template ELEM\n *\n * @param {ArrayLike<ELEM>} a\n * @param {ArrayLike<ELEM>} b\n * @return {boolean}\n */\nexport const equalFlat = (a, b) => a.length === b.length && every(a, (item, index) => item === b[index])\n\n/**\n * @template ELEM\n * @param {Array<Array<ELEM>>} arr\n * @return {Array<ELEM>}\n */\nexport const flatten = arr => fold(arr, /** @type {Array<ELEM>} */ ([]), (acc, val) => acc.concat(val))\n\n/**\n * @template T\n * @param {number} len\n * @param {function(number, Array<T>):T} f\n * @return {Array<T>}\n */\nexport const unfold = (len, f) => {\n  const array = new Array(len)\n  for (let i = 0; i < len; i++) {\n    array[i] = f(i, array)\n  }\n  return array\n}\n\n/**\n * @template T\n * @template RESULT\n * @param {Array<T>} arr\n * @param {RESULT} seed\n * @param {function(RESULT, T, number):RESULT} folder\n */\nexport const fold = (arr, seed, folder) => arr.reduce(folder, seed)\n\nexport const isArray = Array.isArray\n\n/**\n * @template T\n * @param {Array<T>} arr\n * @return {Array<T>}\n */\nexport const unique = arr => from(set.from(arr))\n\n/**\n * @template T\n * @template M\n * @param {ArrayLike<T>} arr\n * @param {function(T):M} mapper\n * @return {Array<T>}\n */\nexport const uniqueBy = (arr, mapper) => {\n  /**\n   * @type {Set<M>}\n   */\n  const happened = set.create()\n  /**\n   * @type {Array<T>}\n   */\n  const result = []\n  for (let i = 0; i < arr.length; i++) {\n    const el = arr[i]\n    const mapped = mapper(el)\n    if (!happened.has(mapped)) {\n      happened.add(mapped)\n      result.push(el)\n    }\n  }\n  return result\n}\n\n/**\n * @template {ArrayLike<any>} ARR\n * @template {function(ARR extends ArrayLike<infer T> ? T : never, number, ARR):any} MAPPER\n * @param {ARR} arr\n * @param {MAPPER} mapper\n * @return {Array<MAPPER extends function(...any): infer M ? M : never>}\n */\nexport const map = (arr, mapper) => {\n  /**\n   * @type {Array<any>}\n   */\n  const res = Array(arr.length)\n  for (let i = 0; i < arr.length; i++) {\n    res[i] = mapper(/** @type {any} */ (arr[i]), i, /** @type {any} */ (arr))\n  }\n  return /** @type {any} */ (res)\n}\n", "/**\n * Common functions and function call helpers.\n *\n * @module function\n */\n\nimport * as array from './array.js'\nimport * as object from './object.js'\n\n/**\n * Calls all functions in `fs` with args. Only throws after all functions were called.\n *\n * @param {Array<function>} fs\n * @param {Array<any>} args\n */\nexport const callAll = (fs, args, i = 0) => {\n  try {\n    for (; i < fs.length; i++) {\n      fs[i](...args)\n    }\n  } finally {\n    if (i < fs.length) {\n      callAll(fs, args, i + 1)\n    }\n  }\n}\n\nexport const nop = () => {}\n\n/**\n * @template T\n * @param {function():T} f\n * @return {T}\n */\nexport const apply = f => f()\n\n/**\n * @template A\n *\n * @param {A} a\n * @return {A}\n */\nexport const id = a => a\n\n/**\n * @template T\n *\n * @param {T} a\n * @param {T} b\n * @return {boolean}\n */\nexport const equalityStrict = (a, b) => a === b\n\n/**\n * @template T\n *\n * @param {Array<T>|object} a\n * @param {Array<T>|object} b\n * @return {boolean}\n */\nexport const equalityFlat = (a, b) => a === b || (a != null && b != null && a.constructor === b.constructor && ((array.isArray(a) && array.equalFlat(a, /** @type {Array<T>} */ (b))) || (typeof a === 'object' && object.equalFlat(a, b))))\n\n/* c8 ignore start */\n\n/**\n * @param {any} a\n * @param {any} b\n * @return {boolean}\n */\nexport const equalityDeep = (a, b) => {\n  if (a == null || b == null) {\n    return equalityStrict(a, b)\n  }\n  if (a.constructor !== b.constructor) {\n    return false\n  }\n  if (a === b) {\n    return true\n  }\n  switch (a.constructor) {\n    case ArrayBuffer:\n      a = new Uint8Array(a)\n      b = new Uint8Array(b)\n    // eslint-disable-next-line no-fallthrough\n    case Uint8Array: {\n      if (a.byteLength !== b.byteLength) {\n        return false\n      }\n      for (let i = 0; i < a.length; i++) {\n        if (a[i] !== b[i]) {\n          return false\n        }\n      }\n      break\n    }\n    case Set: {\n      if (a.size !== b.size) {\n        return false\n      }\n      for (const value of a) {\n        if (!b.has(value)) {\n          return false\n        }\n      }\n      break\n    }\n    case Map: {\n      if (a.size !== b.size) {\n        return false\n      }\n      for (const key of a.keys()) {\n        if (!b.has(key) || !equalityDeep(a.get(key), b.get(key))) {\n          return false\n        }\n      }\n      break\n    }\n    case Object:\n      if (object.length(a) !== object.length(b)) {\n        return false\n      }\n      for (const key in a) {\n        if (!object.hasProperty(a, key) || !equalityDeep(a[key], b[key])) {\n          return false\n        }\n      }\n      break\n    case Array:\n      if (a.length !== b.length) {\n        return false\n      }\n      for (let i = 0; i < a.length; i++) {\n        if (!equalityDeep(a[i], b[i])) {\n          return false\n        }\n      }\n      break\n    default:\n      return false\n  }\n  return true\n}\n\n/**\n * @template V\n * @template {V} OPTS\n *\n * @param {V} value\n * @param {Array<OPTS>} options\n */\n// @ts-ignore\nexport const isOneOf = (value, options) => options.includes(value)\n/* c8 ignore stop */\n\nexport const isArray = array.isArray\n\n/**\n * @param {any} s\n * @return {s is String}\n */\nexport const isString = (s) => s && s.constructor === String\n\n/**\n * @param {any} n\n * @return {n is Number}\n */\nexport const isNumber = n => n != null && n.constructor === Number\n\n/**\n * @template {abstract new (...args: any) => any} TYPE\n * @param {any} n\n * @param {TYPE} T\n * @return {n is InstanceType<TYPE>}\n */\nexport const is = (n, T) => n && n.constructor === T\n\n/**\n * @template {abstract new (...args: any) => any} TYPE\n * @param {TYPE} T\n */\nexport const isTemplate = (T) =>\n  /**\n   * @param {any} n\n   * @return {n is InstanceType<TYPE>}\n   **/\n  n => n && n.constructor === T\n", "/**\n * Utility module to work with key-value stores.\n *\n * @module map\n */\n\n/**\n * Creates a new Map instance.\n *\n * @function\n * @return {Map<any, any>}\n *\n * @function\n */\nexport const create = () => new Map()\n\n/**\n * Copy a Map object into a fresh Map object.\n *\n * @function\n * @template X,Y\n * @param {Map<X,Y>} m\n * @return {Map<X,Y>}\n */\nexport const copy = m => {\n  const r = create()\n  m.forEach((v, k) => { r.set(k, v) })\n  return r\n}\n\n/**\n * Get map property. Create T if property is undefined and set T on map.\n *\n * ```js\n * const listeners = map.setIfUndefined(events, 'eventName', set.create)\n * listeners.add(listener)\n * ```\n *\n * @function\n * @template V,K\n * @template {Map<K,V>} MAP\n * @param {MAP} map\n * @param {K} key\n * @param {function():V} createT\n * @return {V}\n */\nexport const setIfUndefined = (map, key, createT) => {\n  let set = map.get(key)\n  if (set === undefined) {\n    map.set(key, set = createT())\n  }\n  return set\n}\n\n/**\n * Creates an Array and populates it with the content of all key-value pairs using the `f(value, key)` function.\n *\n * @function\n * @template K\n * @template V\n * @template R\n * @param {Map<K,V>} m\n * @param {function(V,K):R} f\n * @return {Array<R>}\n */\nexport const map = (m, f) => {\n  const res = []\n  for (const [key, value] of m) {\n    res.push(f(value, key))\n  }\n  return res\n}\n\n/**\n * Tests whether any key-value pairs pass the test implemented by `f(value, key)`.\n *\n * @todo should rename to some - similarly to Array.some\n *\n * @function\n * @template K\n * @template V\n * @param {Map<K,V>} m\n * @param {function(V,K):boolean} f\n * @return {boolean}\n */\nexport const any = (m, f) => {\n  for (const [key, value] of m) {\n    if (f(value, key)) {\n      return true\n    }\n  }\n  return false\n}\n\n/**\n * Tests whether all key-value pairs pass the test implemented by `f(value, key)`.\n *\n * @function\n * @template K\n * @template V\n * @param {Map<K,V>} m\n * @param {function(V,K):boolean} f\n * @return {boolean}\n */\nexport const all = (m, f) => {\n  for (const [key, value] of m) {\n    if (!f(value, key)) {\n      return false\n    }\n  }\n  return true\n}\n", "/**\n * Common Math expressions.\n *\n * @module math\n */\n\nexport const floor = Math.floor\nexport const ceil = Math.ceil\nexport const abs = Math.abs\nexport const imul = Math.imul\nexport const round = Math.round\nexport const log10 = Math.log10\nexport const log2 = Math.log2\nexport const log = Math.log\nexport const sqrt = Math.sqrt\n\n/**\n * @function\n * @param {number} a\n * @param {number} b\n * @return {number} The sum of a and b\n */\nexport const add = (a, b) => a + b\n\n/**\n * @function\n * @param {number} a\n * @param {number} b\n * @return {number} The smaller element of a and b\n */\nexport const min = (a, b) => a < b ? a : b\n\n/**\n * @function\n * @param {number} a\n * @param {number} b\n * @return {number} The bigger element of a and b\n */\nexport const max = (a, b) => a > b ? a : b\n\nexport const isNaN = Number.isNaN\n\nexport const pow = Math.pow\n/**\n * Base 10 exponential function. Returns the value of 10 raised to the power of pow.\n *\n * @param {number} exp\n * @return {number}\n */\nexport const exp10 = exp => Math.pow(10, exp)\n\nexport const sign = Math.sign\n\n/**\n * @param {number} n\n * @return {boolean} Wether n is negative. This function also differentiates between -0 and +0\n */\nexport const isNegativeZero = n => n !== 0 ? n < 0 : 1 / n < 0\n", "/**\n * Utility functions for working with EcmaScript objects.\n *\n * @module object\n */\n\n/**\n * @return {Object<string,any>} obj\n */\nexport const create = () => Object.create(null)\n\n/**\n * Object.assign\n */\nexport const assign = Object.assign\n\n/**\n * @param {Object<string,any>} obj\n */\nexport const keys = Object.keys\n\n/**\n * @template V\n * @param {{[k:string]:V}} obj\n * @param {function(V,string):any} f\n */\nexport const forEach = (obj, f) => {\n  for (const key in obj) {\n    f(obj[key], key)\n  }\n}\n\n/**\n * @todo implement mapToArray & map\n *\n * @template R\n * @param {Object<string,any>} obj\n * @param {function(any,string):R} f\n * @return {Array<R>}\n */\nexport const map = (obj, f) => {\n  const results = []\n  for (const key in obj) {\n    results.push(f(obj[key], key))\n  }\n  return results\n}\n\n/**\n * @param {Object<string,any>} obj\n * @return {number}\n */\nexport const length = obj => keys(obj).length\n\n/**\n * @param {Object<string,any>} obj\n * @param {function(any,string):boolean} f\n * @return {boolean}\n */\nexport const some = (obj, f) => {\n  for (const key in obj) {\n    if (f(obj[key], key)) {\n      return true\n    }\n  }\n  return false\n}\n\n/**\n * @param {Object|undefined} obj\n */\nexport const isEmpty = obj => {\n  // eslint-disable-next-line\n  for (const _k in obj) {\n    return false\n  }\n  return true\n}\n\n/**\n * @param {Object<string,any>} obj\n * @param {function(any,string):boolean} f\n * @return {boolean}\n */\nexport const every = (obj, f) => {\n  for (const key in obj) {\n    if (!f(obj[key], key)) {\n      return false\n    }\n  }\n  return true\n}\n\n/**\n * Calls `Object.prototype.hasOwnProperty`.\n *\n * @param {any} obj\n * @param {string|symbol} key\n * @return {boolean}\n */\nexport const hasProperty = (obj, key) => Object.prototype.hasOwnProperty.call(obj, key)\n\n/**\n * @param {Object<string,any>} a\n * @param {Object<string,any>} b\n * @return {boolean}\n */\nexport const equalFlat = (a, b) => a === b || (length(a) === length(b) && every(a, (val, key) => (val !== undefined || hasProperty(b, key)) && b[key] === val))\n", "/**\n * Observable class prototype.\n *\n * @module observable\n */\n\nimport * as map from './map.js'\nimport * as set from './set.js'\nimport * as array from './array.js'\n\n/**\n * Handles named events.\n * @experimental\n *\n * This is basically a (better typed) duplicate of Observable, which will replace Observable in the\n * next release.\n *\n * @template {{[key: string]: function(...any):void}} EVENTS\n */\nexport class ObservableV2 {\n  constructor () {\n    /**\n     * Some desc.\n     * @type {Map<string, Set<any>>}\n     */\n    this._observers = map.create()\n  }\n\n  /**\n   * @template {string} NAME\n   * @param {NAME} name\n   * @param {EVENTS[NAME]} f\n   */\n  on (name, f) {\n    map.setIfUndefined(this._observers, /** @type {string} */ (name), set.create).add(f)\n    return f\n  }\n\n  /**\n   * @template {string} NAME\n   * @param {NAME} name\n   * @param {EVENTS[NAME]} f\n   */\n  once (name, f) {\n    /**\n     * @param  {...any} args\n     */\n    const _f = (...args) => {\n      this.off(name, /** @type {any} */ (_f))\n      f(...args)\n    }\n    this.on(name, /** @type {any} */ (_f))\n  }\n\n  /**\n   * @template {string} NAME\n   * @param {NAME} name\n   * @param {EVENTS[NAME]} f\n   */\n  off (name, f) {\n    const observers = this._observers.get(name)\n    if (observers !== undefined) {\n      observers.delete(f)\n      if (observers.size === 0) {\n        this._observers.delete(name)\n      }\n    }\n  }\n\n  /**\n   * Emit a named event. All registered event listeners that listen to the\n   * specified name will receive the event.\n   *\n   * @todo This should catch exceptions\n   *\n   * @template {string} NAME\n   * @param {NAME} name The event name.\n   * @param {Parameters<EVENTS[NAME]>} args The arguments that are applied to the event listener.\n   */\n  emit (name, args) {\n    // copy all listeners to an array first to make sure that no event is emitted to listeners that are subscribed while the event handler is called.\n    return array.from((this._observers.get(name) || map.create()).values()).forEach(f => f(...args))\n  }\n\n  destroy () {\n    this._observers = map.create()\n  }\n}\n\n/* c8 ignore start */\n/**\n * Handles named events.\n *\n * @deprecated\n * @template N\n */\nexport class Observable {\n  constructor () {\n    /**\n     * Some desc.\n     * @type {Map<N, any>}\n     */\n    this._observers = map.create()\n  }\n\n  /**\n   * @param {N} name\n   * @param {function} f\n   */\n  on (name, f) {\n    map.setIfUndefined(this._observers, name, set.create).add(f)\n  }\n\n  /**\n   * @param {N} name\n   * @param {function} f\n   */\n  once (name, f) {\n    /**\n     * @param  {...any} args\n     */\n    const _f = (...args) => {\n      this.off(name, _f)\n      f(...args)\n    }\n    this.on(name, _f)\n  }\n\n  /**\n   * @param {N} name\n   * @param {function} f\n   */\n  off (name, f) {\n    const observers = this._observers.get(name)\n    if (observers !== undefined) {\n      observers.delete(f)\n      if (observers.size === 0) {\n        this._observers.delete(name)\n      }\n    }\n  }\n\n  /**\n   * Emit a named event. All registered event listeners that listen to the\n   * specified name will receive the event.\n   *\n   * @todo This should catch exceptions\n   *\n   * @param {N} name The event name.\n   * @param {Array<any>} args The arguments that are applied to the event listener.\n   */\n  emit (name, args) {\n    // copy all listeners to an array first to make sure that no event is emitted to listeners that are subscribed while the event handler is called.\n    return array.from((this._observers.get(name) || map.create()).values()).forEach(f => f(...args))\n  }\n\n  destroy () {\n    this._observers = map.create()\n  }\n}\n/* c8 ignore end */\n", "/**\n * Utility module to work with sets.\n *\n * @module set\n */\n\nexport const create = () => new Set()\n\n/**\n * @template T\n * @param {Set<T>} set\n * @return {Array<T>}\n */\nexport const toArray = set => Array.from(set)\n\n/**\n * @template T\n * @param {Set<T>} set\n * @return {T}\n */\nexport const first = set =>\n  set.values().next().value || undefined\n\n/**\n * @template T\n * @param {Iterable<T>} entries\n * @return {Set<T>}\n */\nexport const from = entries => new Set(entries)\n", "/**\n * Utility module to work with time.\n *\n * @module time\n */\n\nimport * as metric from './metric.js'\nimport * as math from './math.js'\n\n/**\n * Return current time.\n *\n * @return {Date}\n */\nexport const getDate = () => new Date()\n\n/**\n * Return current unix time.\n *\n * @return {number}\n */\nexport const getUnixTime = Date.now\n\n/**\n * Transform time (in ms) to a human readable format. E.g. 1100 => 1.1s. 60s => 1min. .001 => 10μs.\n *\n * @param {number} d duration in milliseconds\n * @return {string} humanized approximation of time\n */\nexport const humanizeDuration = d => {\n  if (d < 60000) {\n    const p = metric.prefix(d, -1)\n    return math.round(p.n * 100) / 100 + p.prefix + 's'\n  }\n  d = math.floor(d / 1000)\n  const seconds = d % 60\n  const minutes = math.floor(d / 60) % 60\n  const hours = math.floor(d / 3600) % 24\n  const days = math.floor(d / 86400)\n  if (days > 0) {\n    return days + 'd' + ((hours > 0 || minutes > 30) ? ' ' + (minutes > 30 ? hours + 1 : hours) + 'h' : '')\n  }\n  if (hours > 0) {\n    /* c8 ignore next */\n    return hours + 'h' + ((minutes > 0 || seconds > 30) ? ' ' + (seconds > 30 ? minutes + 1 : minutes) + 'min' : '')\n  }\n  return minutes + 'min' + (seconds > 0 ? ' ' + seconds + 's' : '')\n}\n"], "names": [], "sourceRoot": ""}