{"version": 3, "file": "3619.eae907ba96e51b90fe15.js?v=eae907ba96e51b90fe15", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAqF;AACjC;AACF;AACD;AACL;AACA;AACE;AACQ;AACF;AACN;AACC;AAC6D;AACxD;AACU,CAAC,eAAe;AACO;AACtB;AACI;AAC3B;AACU;AACR;AACiB;AACf;AACJ;AACU;AACR;AACA;AACE;AACI;AACiC;AACzB;AACZ;AACF;AACE;AACU;AACJ;AACU;AAClB,CAAC,eAAe;AACA;AAClB;AAC6B;AACjB;AACF;AACV;AACI;AACF;AACI;AACN;AACM;AACE;AACN;AACY;AACJ;AACQ;AACZ;AACI;AACN;AACG;;;ACxD/C,6BAAe,sBAAS;AACxB;AACA;;;ACFqC;;AAErC,IAAI,QAAG;AACP;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,QAAG;AACxB;AACA,6BAA6B,QAAG;;AAEhC;AACA;AACA,yGAAyG,YAAQ;AACjH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,+BAA+B,QAAG;;AAElC;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,2CAA2C,4FAA4F;;AAEvI;AACA;AACA,2CAA2C,gCAAgC,yEAAyE;AACpJ;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,yCAAyC,yCAAyC;;AAElF;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,2BAA2B,yBAAyB;AACpD;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEO;AACP,cAAc,QAAG;AACjB;;AAEO;AACP;AACA;;AAEO;AACP;AACA;;AAEO;AACP;AACA;;;ACxKmB;;;ACLnB;;AAEA,6BAAe,kBAAS;AACxB;AACA;AACA;AACA;;;ACNqC;AACC;;AAEtC,6BAAe,0BAAS;AACxB,6CAA6C,QAAQ;;AAErD,sFAAsF,OAAO;AAC7F,gHAAgH,OAAO;AACvH;AACA;AACA;AACA;AACA;AACA;;AAEA,aAAa,SAAS;AACtB;;;AChBA;AACA;AACA;AACA,cAAc;AACd;AACA;AACe;AACf;AACA;;;ACRA;AACA;AACA;;AAEA,6BAAe,qBAAS;AACxB;AACA;AACA;AACA;;;ACRqC;AACL;AACY;;AAE5C;AACA;AACA,WAAW,KAAK;AAChB;AACA;;AAEA,6BAAe,mBAAS;AACxB;AACA,gBAAgB,WAAW;;AAE3B,0FAA0F,OAAO;AACjG,+DAA+D,OAAO;AACtE;AACA;AACA;AACA;AACA;AACA;;AAEA,aAAa,SAAS;AACtB;;;ACxBA,6BAAe,iBAAS;AACxB;AACA;AACA;AACA;;AAEO;AACP;AACA;AACA;AACA;;;;ACV2C;;AAE3C;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,6BAAe,qBAAS;AACxB;AACA,wDAAwD,YAAY;AACpE;;;ACjB2C;;AAE3C;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,6BAAe,wBAAS;AACxB;AACA,6DAA6D,YAAY;AACzE;;;ACjBqC;AACD;;AAEpC,6BAAe,0BAAS;AACxB,2CAA2C,OAAO;;AAElD,sFAAsF,OAAO;AAC7F,6FAA6F,OAAO;AACpG;AACA;AACA;AACA;AACA;;AAEA,aAAa,SAAS;AACtB;;;ACfA,6BAAe,gBAAS;AACxB;AACA;;;ACFiC;AACI;;AAErC,6BAAe,iBAAW;AAC1B,aAAa,SAAS,iCAAiC,MAAM;AAC7D;;AAEO;AACP;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,iCAAiC,sDAAsD;AACvF,wCAAwC,gDAAgD;AACxF,sCAAsC,8CAA8C;AACpF,yCAAyC;AACzC;;;ACrBA,6BAAe,sBAAS;AACxB;AACA;AACA;AACA;;;ACJqC;AACA;AACC;;AAEtC;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,SAAS,gBAAgB;AACzB;AACA;AACA;AACA,MAAM;AACN,qBAAqB,SAAS;AAC9B;AACA;;AAEA;AACA,SAAS,iBAAiB;AAC1B;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,cAAc,iBAAiB;AAC/B;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,cAAc,gBAAgB;AAC9B;AACA;AACA;AACA;AACA;AACA,MAAM;AACN,qBAAqB,SAAS;AAC9B;AACA;;AAEA;AACA,cAAc,iBAAiB;AAC/B;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,6BAAe,cAAS;AACxB;;AAEA;AACA;AACA;;AAEA,2CAA2C,YAAQ;;AAEnD,uGAAuG,OAAO;AAC9G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,6CAA6C,iBAAiB;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAAS;AACxB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA,wBAAwB;AACxB;;;AC/HiC;AACI;;AAErC,6BAAe,gBAAW;AAC1B,aAAa,SAAS,gCAAgC,MAAM;AAC5D;;;ACLA,6BAAe,cAAS;AACxB;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC;AACrC;AACA;;;ACdqC;;AAErC,6BAAe,eAAS;AACxB;;AAEA,+JAA+J,OAAO;AACtK,yHAAyH,OAAO;AAChI;AACA;AACA;AACA;AACA;;AAEA,SAAS,QAAQ;AACjB;AACA;;AAEA,aAAa,SAAS;AACtB;;;AClBA,6BAAe,iBAAW;;AAE1B,6DAA6D,QAAQ;AACrE,6EAA6E,SAAS;AACtF;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;;ACZqC;;AAErC,6BAAe,cAAS;AACxB;;AAEA;AACA;AACA;;AAEA,uFAAuF,OAAO;AAC9F,yGAAyG,OAAO;AAChH;AACA;AACA;AACA;AACA;AACA;;AAEA,aAAa,SAAS;AACtB;;AAEA;AACA;AACA;;;ACvBA,6BAAe,gBAAW;AAC1B;AACA;AACA;AACA;AACA;;;ACLA,6BAAe,iBAAW;AAC1B;AACA;;;ACFA,6BAAe,gBAAW;;AAE1B,4DAA4D,OAAO;AACnE,yDAAyD,OAAO;AAChE;AACA;AACA;AACA;;AAEA;AACA;;;ACVA,6BAAe,gBAAW;AAC1B;AACA,mCAAmC;AACnC;AACA;;;ACJA,6BAAe,2BAAW;AAC1B;AACA;;;ACFA,6BAAe,cAAS;;AAExB,4DAA4D,OAAO;AACnE,+DAA+D,OAAO;AACtE;AACA;AACA;;AAEA;AACA;;;ACTO;;AAEP,iDAAe;AACf;AACA;AACA;AACA;AACA;AACA,CAAC,EAAC;;;ACRuC;;AAEzC,6BAAe,mBAAS;AACxB;AACA;AACA,SAAS,UAAU,2BAA2B,OAAO,UAAU,uBAAuB,QAAQ;AAC9F;;;ACNwC;;AAExC;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,6BAAe,cAAS;AACxB,iBAAiB,SAAS;;AAE1B;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;ACxDA,6BAAe,oBAAS;AACxB;AACA;AACA,2BAA2B;AAC3B;;;ACJuC;;AAEvC;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,6BAAe,eAAS;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;;AAEO;AACP;AACA,SAAS,UAAW;AACpB;;;AClCA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,6BAAe,kBAAS;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;;;AC3BA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,6BAAe,iBAAS;AACxB;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;AC1EA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA,6BAAe,wBAAS;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;;;ACxBA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA,6BAAe,cAAS;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;;;ACxBA;AACA;AACA;;AAEA,6BAAe,2BAAW;AAC1B;AACA;;;ACNA;AACA;AACA;;AAEA,6BAAe,2BAAW;AAC1B;AACA;;;ACNuC;AACD;;AAEtC;AACA;AACA;AACA;AACA,mBAAmB,KAAK,8CAA8C,KAAK;AAC3E;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,6BAAe,iBAAS;AACxB,iBAAiB,SAAS;AAC1B;AACA;AACA;AACA;;;ACxBoC;;AAEpC,6BAAe,gBAAS;AACxB,mDAAmD,OAAO;AAC1D;AACA;AACA,GAAG;AACH;;;ACPoC;AACE;;AAEtC;AACA;AACA;;AAEA,6BAAe,gBAAS;AACxB,mDAAmD,OAAO;AAC1D,uFAAuF,QAAQ;AAC/F;AACA;AACA,GAAG;AACH;;;ACbA;AACA;AACA;AACA;;AAEA,6BAAe,4BAAW;AAC1B;AACA;;;ACPA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA,6BAAe,eAAS;AACxB;AACA;;;ACZA,6BAAe,yBAAS;AACxB;AACA;AACA;AACA;;;ACJA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,YAAY;AACZ,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA,8CAA8C,OAAO;AACrD;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,2CAA2C,OAAO;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;;AAEA,6BAAe,YAAS;AACxB;;AAEA;AACA;AACA,8CAA8C,OAAO;AACrD,6BAA6B,OAAO;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,cAAc,OAAO;AACrB;AACA;;;AClEuC;;AAEvC;AACA,eAAe,UAAW;AAC1B;;AAEA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,6BAAe,4BAAS;AACxB;AACA;AACA;AACA;;;ACjCA,6BAAe,qBAAY;AAC3B,4DAA4D,OAAO;AACnE,+DAA+D,OAAO;AACtE;AACA;AACA;AACA;;;ACN2C;AACM;AACI;AACM;AAChB;AACJ;AACE;AACF;AACA;AACE;AACA;AACF;AACA;AACE;AACF;AACA;AACE;AACF;AACA;AACE;AACM;AACF;AACN;AACA;AACE;AACA;AACE;AACA;AACA;AACF;AACA;AACN;AACY;AACA;;AAExC;;AAEA;AACP;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,UAAU,gBAAgB;AAC1B,aAAa,SAAmB;AAChC,eAAe,WAAqB;AACpC,kBAAkB,cAAwB;AAC1C,UAAU,gBAAgB;AAC1B,QAAQ,IAAc;AACtB,SAAS,KAAe;AACxB,QAAQ,IAAc;AACtB,QAAQ,IAAc;AACtB,SAAS,KAAe;AACxB;AACA,SAAS,KAAe;AACxB,QAAQ,IAAc;AACtB,QAAQ,IAAc;AACtB,SAAS,KAAe;AACxB,QAAQ,IAAc;AACtB,QAAQ,IAAc;AACtB,SAAS,eAAe;AACxB,QAAQ,IAAc;AACtB,QAAQ,IAAc;AACtB,SAAS,KAAe;AACxB,YAAY,QAAkB;AAC9B,WAAW,OAAiB;AAC5B,QAAQ,cAAc;AACtB,QAAQ,IAAc;AACtB,SAAS,eAAe;AACxB,SAAS,eAAe;AACxB,UAAU,MAAgB;AAC1B,UAAU,MAAgB;AAC1B,UAAU,gBAAgB;AAC1B,SAAS,KAAe;AACxB,SAAS,eAAe;AACxB,MAAM,EAAY;AAClB,YAAY,kBAAkB;AAC9B,qBAAqB,QAAkB;AACvC;;AAEA,oDAAe,SAAS,EAAC;;;;;;;ACzFQ;;AAEjC,6BAAe,iBAAS;AACxB,cAAc,mBAAK;AACnB;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;;;ACVqC;AACG;;AAExC,cAAc,+BAAQ;AACtB;;AAEO;AACA;AACA;AACA;AACA;AACA;AACA;;AAEP,6BAAe,kBAAS;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEO;AACP;AACA,2DAA2D;AAC3D;AACA;;AAEO;AACP;AACA,2DAA2D;AAC3D;AACA;;AAEO;AACP;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,eAAe,uBAAK;;AAEpB;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,4CAA4C;AAC5C,sCAAsC,OAAO;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,yDAAyD;AACzD;AACA,2DAA2D;AAC3D;AACA,IAAI,OAAO;AACX;AACA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA,yCAAyC;AACzC;;AAEA;AACA;AACA,wBAAwB,OAAO;AAC/B;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,qCAAqC;AACrC;AACA;AACA;;;ACxJiE;;AAEjE,6BAAe,uBAAS;AACxB;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA,mDAAmD,eAAe;AAClE,8BAA8B,QAAQ,qBAAqB,MAAM;AACjE,qBAAqB,KAAK;AAC1B;AACA;AACA;AACA;;AAEA;AACA;;;ACvBwC;;AAExC,6BAAe,6BAAS;AACxB;AACA,IAAI,aAAS;AACb,GAAG;AACH;;;;;ACNuC;;AAEvC;AACA;AACA;AACA,mBAAmB,GAAG;AACtB;;AAEA;AACA;AACA;AACA;AACA;AACA,yCAAyC,OAAO;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,mBAAmB,GAAG;AACtB;;AAEA;AACA;AACA;AACA;AACA;AACA,oBAAoB,yBAAyB,4BAA4B,OAAO;AAChF;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,6BAAe,eAAS;AACxB;;AAEA;;AAEA;AACA,gBAAgB,GAAG;AACnB,yCAAyC,OAAO;AAChD;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEO;AACP;;AAEA;AACA,mBAAmB,GAAG;AACtB,2CAA2C;AAC3C,GAAG;;AAEH;AACA,WAAW,GAAG;AACd;AACA;;;;;;;;;;;AChF+B;AACqD;;AAEpF,6BAAe,gCAAS;AACxB;AACA,kCAAkC,yBAAiB;AACnD,qBAAqB,qBAAK,GAAG,mBAAc;AAC3C,aAAa,yBAAK,eAAe,mBAAc;AAC/C,QAAQ,qBAAiB;AACzB;;;ACT+E;AACxC;AACD;AACK;;AAE3C,SAAS,eAAU;AACnB;AACA;AACA;AACA;;AAEA,SAAS,iBAAY;AACrB;AACA;AACA;AACA;;AAEA,SAAS,iBAAY;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAS,mBAAc;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAS,iBAAY;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAS,mBAAc;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,6BAAe,yBAAS;AACxB,iBAAiB,SAAS,uCAAuC,wCAAoB,GAAG,sBAAW;AACnG;AACA,0BAA0B,mBAAc,GAAG,iBAAY,eAAe,UAAU;AAChF,0CAA0C,iBAAY,GAAG,eAAU;AACnE,0BAA0B,mBAAc,GAAG,iBAAY;AACvD;;;AC7EuC;;AAEvC;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,6BAAe,8BAAS;AACxB;AACA;AACA;AACA;AACA,iBAAiB,SAAS;AAC1B;AACA;;;AC3CwC;;AAExC;AACA;AACA,IAAI,IAAI;AACR;AACA;;AAEA;AACA;AACA,IAAI,IAAI;AACR;AACA;;AAEA,6BAAe,eAAS;AACxB;;AAEA;AACA;AACA;AACA;AACA,QAAQ,GAAG;AACX;;;ACtBuC;;AAEvC;AACA;AACA,IAAI,GAAG;AACP;AACA;;AAEA;AACA;AACA,IAAI,GAAG;AACP;AACA;;AAEA,6BAAe,kBAAS;AACxB;;AAEA;AACA;AACA;AACA;AACA,QAAQ,GAAG;AACX;;;ACtBuC;;AAEvC;AACA;AACA;AACA,IAAI,GAAG;AACP;AACA;;AAEA,6BAAe,cAAS;AACxB;;AAEA;AACA;AACA,QAAQ,GAAG;AACX;;;ACfkC;;AAElC;AACA;AACA;AACA;AACA,IAAI,GAAG;AACP;AACA;;AAEA,6BAAe,gCAAS;AACxB;AACA;AACA;;;ACbqC;AACC;;AAEtC,6BAAe,2BAAS;AACxB,2CAA2C,OAAO;;AAElD,sFAAsF,OAAO;AAC7F,6FAA6F,OAAO;AACpG;AACA;AACA;AACA;AACA;;AAEA,aAAa,UAAU;AACvB;;;ACfsC;;AAEtC,6BAAe,0BAAS;AACxB;;AAEA,gKAAgK,OAAO;AACvK,yHAAyH,OAAO;AAChI;AACA;AACA;AACA;AACA;;AAEA,SAAS,QAAQ;AACjB;AACA;;AAEA,aAAa,UAAU;AACvB;;;AClB6C;;AAE7C;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA,oCAAoC,IAAI,GAAG,GAAG;AAC9C;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,6BAAe,uBAAS;AACxB;;AAEA;AACA,QAAQ,GAAG;AACX;AACA;;;AC/BA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,6BAAe,6BAAW;AAC1B;AACA;;;ACVsC;AACA;AACM;;AAE5C,6BAAe,2BAAS;AACxB;AACA;;AAEA,6CAA6C,QAAQ;;AAErD,sFAAsF,OAAO;AAC7F,gHAAgH,OAAO;AACvH;AACA;AACA;AACA,QAAQ,QAAQ,qCAAqC,GAAG;AACxD;AACA;AACA;;AAEA,aAAa,UAAU;AACvB;;;ACrByC;AACH;AACM;;AAE5C,6BAAe,8BAAS;AACxB;AACA;;AAEA,6CAA6C,WAAW;;AAExD,0FAA0F,OAAO;AACjG,+DAA+D,OAAO;AACtE;AACA,yFAAyF,GAAG,wCAAwC,OAAO;AAC3I;AACA,YAAY,QAAQ;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,aAAa,UAAU;AACvB;;;ACzBuC;;AAEvC,IAAI,mBAAS,GAAG,aAAS;;AAEzB,6BAAe,gCAAW;AAC1B,aAAa,mBAAS;AACtB;;;ACN+E;AAC5C;AACD;AACI;AACK;;AAE3C;AACA;AACA;AACA;AACA;AACA,kBAAkB,UAAK;AACvB,oDAAoD,UAAK;AACzD;AACA;AACA;AACA;AACA;;AAEA,SAAS,iBAAW;AACpB;AACA;AACA;AACA;;AAEA,SAAS,mBAAa;AACtB;AACA;AACA;AACA;AACA,kBAAkB,UAAK;AACvB;AACA;AACA;AACA;AACA;;AAEA,SAAS,mBAAa;AACtB;AACA;AACA;AACA;AACA,kBAAkB,UAAK;AACvB;AACA;AACA,6EAA6E,UAAK;AAClF;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,mBAAmB,GAAG;AACtB;AACA,qEAAqE,iBAAW;;AAEhF;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,6BAAe,0BAAS;AACxB,yCAAyC,wCAAoB,GAAG,sBAAW;AAC3E;AACA;AACA,+BAA+B,iBAAW;AAC1C;AACA,wBAAwB,mBAAa,UAAU,UAAU;AACzD;AACA;AACA,wBAAwB,mBAAa;AACrC;AACA;;;AC/EA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,6BAAe,+BAAS;AACxB;AACA;AACA;AACA;AACA;AACA;;;ACvBsC;;AAEtC,SAAS,iBAAY;AACrB;AACA;AACA;AACA;;AAEA,SAAS,iBAAY;AACrB;AACA;AACA;AACA;AACA;;AAEA,6BAAe,yBAAS;AACxB;AACA,QAAQ,iBAAY,CAAC,UAAU;AAC/B,QAAQ,iBAAY;AACpB;;;ACnBA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,6BAAe,8BAAS;AACxB;AACA;AACA;AACA;AACA;AACA;;;ACvB6C;AACD;;AAE5C,6BAAe,sBAAW;AAC1B;AACA;AACA,YAAY,KAAK;;AAEjB,4DAA4D,OAAO;AACnE,+DAA+D,OAAO;AACtE;AACA,sBAAsB,GAAG;AACzB,QAAQ,QAAQ;AAChB;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;;AAEA,aAAa,UAAU;AACvB;;;ACvBkC;;AAElC,6BAAe,eAAW;AAC1B;AACA;AACA,kBAAkB,cAAc;AAChC,eAAe,oBAAoB;;AAEnC;AACA,qBAAqB,GAAG;AACxB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,KAAK;;AAEL;AACA;AACA,GAAG;AACH;;;AC5BuC;AACC;AACU;AACR;AACM;AACR;AACc;AACV;AACF;AACN;AACQ;AACA;AACM;AACA;AACR;AACU;AACZ;AACU;AACE;AACV;AACJ;;AAEtC;;AAEO;AACP;AACA;AACA;AACA;AACA;;AAEe,SAAS,qBAAU;AAClC,SAAS,aAAS;AAClB;;AAEO;AACP;AACA;;AAEA,0BAA0B,aAAS;;AAEnC,uBAAuB,qBAAU;AACjC;AACA,UAAU,iBAAiB;AAC3B,aAAa,oBAAoB;AACjC;AACA;AACA,UAAU,iBAAiB;AAC3B,SAAS,gBAAgB;AACzB,aAAa,oBAAoB;AACjC,cAAc,UAAqB;AACnC;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,aAAa;AACnB,QAAQ,eAAe;AACvB,aAAa,oBAAoB;AACjC,SAAS,gBAAgB;AACzB,cAAc,qBAAqB;AACnC,QAAQ,eAAe;AACvB,aAAa,oBAAoB;AACjC,UAAU,iBAAiB;AAC3B,SAAS,KAAgB;AACzB,SAAS,KAAgB;AACzB,YAAY,QAAmB;AAC/B,QAAQ,IAAe;AACvB,eAAe,sBAAsB;AACrC,OAAO,GAAc;AACrB;AACA;;;ACxEO;AACP;AACA;;AAEO;AACP;AACA;;AAEO;AACP;AACA;;;ACVyD;AACR;AACV;AACV;;AAE7B;AACA;AACA;AACA;AACA,QAAQ,UAAc;AACtB;;AAEA;AACA;AACA;AACA;AACA,oCAAoC,IAAI;AACxC;AACA;AACA;AACA;;AAEA,6BAAe,8BAAS;AACxB;AACA;;AAEA,sBAAsB,UAAU;AAChC;AACA,IAAI;AACJ,SAAS,KAAK,oCAAoC,qBAAG;AACrD;;AAEA,4DAA4D,OAAO;AACnE,+DAA+D,OAAO;AACtE;AACA,QAAQ,QAAQ;AAChB;AACA;AACA;;AAEA,aAAa,UAAU;AACvB;;;ACzCuC;AACU;AACE;;AAEnD,aAAS,uBAAuB,mBAAmB;AACnD,aAAS,wBAAwB,oBAAoB;;;ACLvB;AAC8B;AACd;AACM;;;ACHf;AACW;AACL;AACE;AACL;AACH;AACD;AACgB;;AAEpD,iBAAiB,aAAa;AAC9B,kBAAkB,cAAc;AAChC,mBAAmB,eAAe;AAClC,mBAAmB;;AAEnB,OAAO,QAAQ,gBAAK,aAAE;;AAEtB;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,0BAA0B,iEAAiE;AAC3F,yBAAyB;AACzB;;AAEA;AACA;AACA;AACA,0BAA0B,iEAAiE;AAC3F,yBAAyB;AACzB;;AAEA;AACA;AACA;AACA,wBAAwB,yCAAyC;AACjE,yBAAyB;AACzB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,UAAU;AACV;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,SAAS,WAAK;AACd;AACA;AACA;;AAEO;AACP;AACA;AACA;;AAEO;AACP,SAAS,WAAK;AACd;;AAEO;AACP,SAAS,WAAK;AACd;;AAEA,6BAAe,iBAAW;AAC1B,SAAS,WAAK;AACd;;AAEA,SAAS,WAAK;AACd;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,uCAAuC,gBAAgB;;AAEvD;;AAEA;AACA,qCAAqC,oCAAoC;AACzE,sCAAsC,yBAAyB;;AAE/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,+CAA+C,sDAAsD;AACrG,6DAA6D,sCAAsC;AACnG;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,WAAW;AACX,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,mCAAmC,iHAAiH;AACpJ,mCAAmC,iGAAiG;AACpI,uCAAuC,wGAAwG;AAC/I,wCAAwC,wGAAwG;AAChJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;;AAEA;AACA;AACA;AACA;AACA,+BAA+B,SAAG;AAClC,+BAA+B,SAAG;AAClC;AACA,+BAA+B,SAAG;AAClC,+BAA+B,SAAG;AAClC;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,0BAA0B,SAAG,SAAS,SAAG;AACzC,0BAA0B,SAAG,SAAS,SAAG;AACzC;AACA;AACA;AACA;AACA,4BAA4B,SAAG,IAAI,SAAG,yBAAyB,SAAG,IAAI,SAAG;AACzE,4BAA4B,SAAG,IAAI,SAAG,yBAAyB,SAAG,IAAI,SAAG;AACzE,YAAY;AACZ,gCAAgC,SAAG,SAAS,SAAG;AAC/C,qCAAqC,SAAG,SAAS,SAAG;AACpD,gCAAgC,SAAG,SAAS,SAAG;AAC/C,qCAAqC,SAAG,SAAS,SAAG;AACpD;AACA;AACA;AACA;AACA,0BAA0B,SAAG,IAAI,SAAG,4BAA4B,SAAG,IAAI,SAAG;AAC1E,0BAA0B,SAAG,IAAI,SAAG,4BAA4B,SAAG,IAAI,SAAG;AAC1E;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA,wDAAwD;AACxD;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,8CAA8C,qBAAqB,QAAQ;AAC3E,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA,wDAAwD;AACxD,UAAU,WAAK;AACf;AACA;;AAEA;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,UAAU;AAC7B;AACA,yCAAyC;AACzC,yCAAyC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA,oCAAoC;AACpC,oCAAoC;AACpC;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA,cAAc;AACd,sCAAsC;AACtC,sCAAsC;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,iCAAiC;AACjC;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;;ACvmBoB;;;;;;;;;;;ACLuB;AACP;AACD;;AAEpB;AACf,cAAc,0BAAO;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,wBAAQ,sBAAsB,0BAA0B;AACzE;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAS,yBAAS;AAClB;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEO;AACP;AACA;;;;;;;ACjGmB;;AAII;;AAIF;;AAIH;;AAIG;;AAKC;;AAKJ;;AAIG;;AAIE;;AAIA;;AAIC;;AAIL;;AAIG;;AAQG;;AAIQ;;AAQT;;AAIC;;;;;AC7ES;;AAElC,gDAAe,yBAAM,gEAAgE,EAAC;;;ACFd;AACR;AACF;AACc;AACZ;AACE;AACA;AACN;AACA;AACA;AACU;AACe;AACA;AACA;AACA;AACA;AACA;AACM;AACA;AACM;AACL;AACA;AACA;AACA;AACM;AACN;AACA;AACA;AACM;AACN;AACM;AACA;AACF;AACG;AACH;AACM;AACT;AACS;AAC1B;AACW;AACuC;AAClD;AACJ;AAC2F;;;AC3C9G;;AAErD,6BAAe,oBAAS;AACxB;AACA,YAAY,SAAS;AACrB,YAAY,SAAS,eAAe,IAAI;AACxC;;;ACN8C;AACE;AACJ;AACI;AACI;AACE;AACN;AACE;AACJ;AACM;AACM;AACR;AACM;AACC;AACX;;;;;;;;;;;ACd9C,6BAAe,oBAAS;AACxB;AACA;;;ACFA,6BAAe,+BAAS;AACxB;AACA;;;;;ACF+B;AACM;AACI;AACJ;AACP;;AAE9B,6BAAe,eAAW;AAC1B,cAAc,qBAAQ;AACtB,mBAAmB,UAAU;AAC7B;AACA,mBAAmB,wCAAQ;AAC3B,iBAAiB,wCAAQ,CAAC,gBAAG;AAC7B,iBAAiB,wCAAQ;;AAEzB;AACA;AACA,oBAAoB,4BAAK;AACzB;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,gBAAG,YAAY,gBAAG;AACxC;AACA;AACA;AACA;;AAEA,gBAAgB,OAAO;AACvB;AACA;AACA;AACA;;AAEA;AACA,wDAAwD,sCAAsC;AAC9F,uDAAuD,gCAAgC;;AAEvF;AACA,mDAAmD,OAAO;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,qEAAqE,wCAAQ;AAC7E;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,0EAA0E,wCAAQ;AAClF;;AAEA;AACA,wEAAwE,wCAAQ;AAChF;;AAEA;AACA,wEAAwE,wCAAQ;AAChF;;AAEA;AACA;;;;;;;;;AC/E4C;;AAE5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEO;AACP;AACA;;AAEO;AACP;AACA;;AAEO;AACP;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1EwC;AACE;AACA;AACF;AACqC,CAAC;AACD,CAAC;AACtB;AACiB;;AAEyB;AACnC;AACJ;AACF;AACI;AACE;AACR;AACI;AACE;AACN;AACQ;AACE;AACZ;AACwB;;AAEV;AACJ;AACR;AACkB;AAChB;AACgB;AACJ;AACR;AACgB;AACJ;AACR;AACI;AACZ;AACoC;AAClC;AACsD;;AAErE;AACoB;AACM;AACV;AACY;AACR;AACM;AACF;AACE;AACF;AACV;AACM;;;;;;;;;;;;;;;;;AClDzC;;AAOG;;AAOL;;AAOA;;AAOF;;AASD;;AAmCC;;AAOC;;AAOD;;AAOC;;;;;AC/F8F;AAC1D;AACJ;AACF;;;ACH3C,SAAS,mBAAS;AACzB;AACA;AACA;AACA;;AAEA,mBAAS;AACT,eAAe,mBAAS;AACxB;AACA,gCAAgC,mBAAS;AACzC,GAAG;AACH;AACA,0CAA0C,mBAAS;AACnD,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;;AAEO,IAAI,kBAAQ,OAAO,mBAAS;;AAEnC,mBAAS,aAAa,mBAAS;;AAEhB,SAAS,mBAAS;AACjC,6DAA6D,kBAAQ;AACrE;AACA;;;AClDqC;AACW;AACD;AACF;AACL;AACH;AACF;AACgB;AACC;;AAEpD;AACA;AACA,SAAS,kBAAa;AACtB;AACA;;AAEA,SAAS,kBAAa;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAAS,qBAAgB;AACzB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,6BAAe,gBAAW;AAC1B,eAAe,kBAAa;AAC5B,eAAe,kBAAa;AAC5B;AACA;AACA,kBAAkB,qBAAgB;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,oCAAoC,eAAe;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,uCAAuC,gDAAgD;AACvF,oDAAoD,8CAA8C;AAClG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC;AAChC,mBAAmB,4BAA4B;AAC/C;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,gBAAgB,OAAO;AACvB;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA,iFAAiF,uBAAuB;AACxG;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,gBAAgB,OAAO;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,0CAA0C,qBAAqB;AAC/D,gBAAgB,OAAO;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;;AC9b0C;AACoE;;;ACDrF;AACD;AACC;AACA;AACA;AACE;AACC;AACA;AACJ;AACD;AACC;AACC;AACA;AACC;AACH;AACM;AACE;AACP;AACG;AACC;AACF;AACD;AACU;AACN;AACJ;AACD;AACO;AACN;AACK;AACN", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-array/src/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-axis/src/identity.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-axis/src/axis.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-axis/src/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-selection/src/selector.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-selection/src/selection/select.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-selection/src/array.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-selection/src/selectorAll.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-selection/src/selection/selectAll.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-selection/src/matcher.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-selection/src/selection/selectChild.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-selection/src/selection/selectChildren.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-selection/src/selection/filter.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-selection/src/selection/sparse.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-selection/src/selection/enter.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-selection/src/constant.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-selection/src/selection/data.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-selection/src/selection/exit.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-selection/src/selection/join.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-selection/src/selection/merge.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-selection/src/selection/order.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-selection/src/selection/sort.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-selection/src/selection/call.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-selection/src/selection/nodes.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-selection/src/selection/node.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-selection/src/selection/size.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-selection/src/selection/empty.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-selection/src/selection/each.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-selection/src/namespaces.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-selection/src/namespace.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-selection/src/selection/attr.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-selection/src/window.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-selection/src/selection/style.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-selection/src/selection/property.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-selection/src/selection/classed.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-selection/src/selection/text.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-selection/src/selection/html.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-selection/src/selection/raise.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-selection/src/selection/lower.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-selection/src/creator.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-selection/src/selection/append.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-selection/src/selection/insert.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-selection/src/selection/remove.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-selection/src/selection/clone.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-selection/src/selection/datum.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-selection/src/selection/on.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-selection/src/selection/dispatch.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-selection/src/selection/iterator.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-selection/src/selection/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-timer/src/timeout.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-transition/src/transition/schedule.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-transition/src/interrupt.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-transition/src/selection/interrupt.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-transition/src/transition/tween.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-transition/src/transition/interpolate.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-transition/src/transition/attr.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-transition/src/transition/attrTween.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-transition/src/transition/delay.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-transition/src/transition/duration.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-transition/src/transition/ease.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-transition/src/transition/easeVarying.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-transition/src/transition/filter.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-transition/src/transition/merge.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-transition/src/transition/on.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-transition/src/transition/remove.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-transition/src/transition/select.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-transition/src/transition/selectAll.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-transition/src/transition/selection.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-transition/src/transition/style.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-transition/src/transition/styleTween.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-transition/src/transition/text.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-transition/src/transition/textTween.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-transition/src/transition/transition.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-transition/src/transition/end.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-transition/src/transition/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-ease/src/cubic.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-transition/src/selection/transition.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-transition/src/selection/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-transition/src/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-brush/src/brush.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-brush/src/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-scale/src/band.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-scale/src/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-scale-chromatic/src/categorical/Tableau10.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-scale-chromatic/src/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-selection/src/select.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-selection/src/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-shape/src/descending.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-shape/src/identity.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-shape/src/pie.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-shape/src/curve/bump.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-shape/src/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-time/src/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-time-format/src/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-zoom/src/transform.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-zoom/src/zoom.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3-zoom/src/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/d3/src/index.js"], "sourcesContent": ["export {default as bisect, bisectRight, bisectLeft, bisectCenter} from \"./bisect.js\";\nexport {default as ascending} from \"./ascending.js\";\nexport {default as bisector} from \"./bisector.js\";\nexport {blur, blur2, blurImage} from \"./blur.js\";\nexport {default as count} from \"./count.js\";\nexport {default as cross} from \"./cross.js\";\nexport {default as cumsum} from \"./cumsum.js\";\nexport {default as descending} from \"./descending.js\";\nexport {default as deviation} from \"./deviation.js\";\nexport {default as extent} from \"./extent.js\";\nexport {Adder, fsum, fcumsum} from \"./fsum.js\";\nexport {default as group, flatGroup, flatRollup, groups, index, indexes, rollup, rollups} from \"./group.js\";\nexport {default as groupSort} from \"./groupSort.js\";\nexport {default as bin, default as histogram} from \"./bin.js\"; // Deprecated; use bin.\nexport {default as thresholdFreedmanDiaconis} from \"./threshold/freedmanDiaconis.js\";\nexport {default as thresholdScott} from \"./threshold/scott.js\";\nexport {default as thresholdSturges} from \"./threshold/sturges.js\";\nexport {default as max} from \"./max.js\";\nexport {default as maxIndex} from \"./maxIndex.js\";\nexport {default as mean} from \"./mean.js\";\nexport {default as median, medianIndex} from \"./median.js\";\nexport {default as merge} from \"./merge.js\";\nexport {default as min} from \"./min.js\";\nexport {default as minIndex} from \"./minIndex.js\";\nexport {default as mode} from \"./mode.js\";\nexport {default as nice} from \"./nice.js\";\nexport {default as pairs} from \"./pairs.js\";\nexport {default as permute} from \"./permute.js\";\nexport {default as quantile, quantileIndex, quantileSorted} from \"./quantile.js\";\nexport {default as quickselect} from \"./quickselect.js\";\nexport {default as range} from \"./range.js\";\nexport {default as rank} from \"./rank.js\";\nexport {default as least} from \"./least.js\";\nexport {default as leastIndex} from \"./leastIndex.js\";\nexport {default as greatest} from \"./greatest.js\";\nexport {default as greatestIndex} from \"./greatestIndex.js\";\nexport {default as scan} from \"./scan.js\"; // Deprecated; use leastIndex.\nexport {default as shuffle, shuffler} from \"./shuffle.js\";\nexport {default as sum} from \"./sum.js\";\nexport {default as ticks, tickIncrement, tickStep} from \"./ticks.js\";\nexport {default as transpose} from \"./transpose.js\";\nexport {default as variance} from \"./variance.js\";\nexport {default as zip} from \"./zip.js\";\nexport {default as every} from \"./every.js\";\nexport {default as some} from \"./some.js\";\nexport {default as filter} from \"./filter.js\";\nexport {default as map} from \"./map.js\";\nexport {default as reduce} from \"./reduce.js\";\nexport {default as reverse} from \"./reverse.js\";\nexport {default as sort} from \"./sort.js\";\nexport {default as difference} from \"./difference.js\";\nexport {default as disjoint} from \"./disjoint.js\";\nexport {default as intersection} from \"./intersection.js\";\nexport {default as subset} from \"./subset.js\";\nexport {default as superset} from \"./superset.js\";\nexport {default as union} from \"./union.js\";\nexport {InternMap, InternSet} from \"internmap\";\n", "export default function(x) {\n  return x;\n}\n", "import identity from \"./identity.js\";\n\nvar top = 1,\n    right = 2,\n    bottom = 3,\n    left = 4,\n    epsilon = 1e-6;\n\nfunction translateX(x) {\n  return \"translate(\" + x + \",0)\";\n}\n\nfunction translateY(y) {\n  return \"translate(0,\" + y + \")\";\n}\n\nfunction number(scale) {\n  return d => +scale(d);\n}\n\nfunction center(scale, offset) {\n  offset = Math.max(0, scale.bandwidth() - offset * 2) / 2;\n  if (scale.round()) offset = Math.round(offset);\n  return d => +scale(d) + offset;\n}\n\nfunction entering() {\n  return !this.__axis;\n}\n\nfunction axis(orient, scale) {\n  var tickArguments = [],\n      tickValues = null,\n      tickFormat = null,\n      tickSizeInner = 6,\n      tickSizeOuter = 6,\n      tickPadding = 3,\n      offset = typeof window !== \"undefined\" && window.devicePixelRatio > 1 ? 0 : 0.5,\n      k = orient === top || orient === left ? -1 : 1,\n      x = orient === left || orient === right ? \"x\" : \"y\",\n      transform = orient === top || orient === bottom ? translateX : translateY;\n\n  function axis(context) {\n    var values = tickValues == null ? (scale.ticks ? scale.ticks.apply(scale, tickArguments) : scale.domain()) : tickValues,\n        format = tickFormat == null ? (scale.tickFormat ? scale.tickFormat.apply(scale, tickArguments) : identity) : tickFormat,\n        spacing = Math.max(tickSizeInner, 0) + tickPadding,\n        range = scale.range(),\n        range0 = +range[0] + offset,\n        range1 = +range[range.length - 1] + offset,\n        position = (scale.bandwidth ? center : number)(scale.copy(), offset),\n        selection = context.selection ? context.selection() : context,\n        path = selection.selectAll(\".domain\").data([null]),\n        tick = selection.selectAll(\".tick\").data(values, scale).order(),\n        tickExit = tick.exit(),\n        tickEnter = tick.enter().append(\"g\").attr(\"class\", \"tick\"),\n        line = tick.select(\"line\"),\n        text = tick.select(\"text\");\n\n    path = path.merge(path.enter().insert(\"path\", \".tick\")\n        .attr(\"class\", \"domain\")\n        .attr(\"stroke\", \"currentColor\"));\n\n    tick = tick.merge(tickEnter);\n\n    line = line.merge(tickEnter.append(\"line\")\n        .attr(\"stroke\", \"currentColor\")\n        .attr(x + \"2\", k * tickSizeInner));\n\n    text = text.merge(tickEnter.append(\"text\")\n        .attr(\"fill\", \"currentColor\")\n        .attr(x, k * spacing)\n        .attr(\"dy\", orient === top ? \"0em\" : orient === bottom ? \"0.71em\" : \"0.32em\"));\n\n    if (context !== selection) {\n      path = path.transition(context);\n      tick = tick.transition(context);\n      line = line.transition(context);\n      text = text.transition(context);\n\n      tickExit = tickExit.transition(context)\n          .attr(\"opacity\", epsilon)\n          .attr(\"transform\", function(d) { return isFinite(d = position(d)) ? transform(d + offset) : this.getAttribute(\"transform\"); });\n\n      tickEnter\n          .attr(\"opacity\", epsilon)\n          .attr(\"transform\", function(d) { var p = this.parentNode.__axis; return transform((p && isFinite(p = p(d)) ? p : position(d)) + offset); });\n    }\n\n    tickExit.remove();\n\n    path\n        .attr(\"d\", orient === left || orient === right\n            ? (tickSizeOuter ? \"M\" + k * tickSizeOuter + \",\" + range0 + \"H\" + offset + \"V\" + range1 + \"H\" + k * tickSizeOuter : \"M\" + offset + \",\" + range0 + \"V\" + range1)\n            : (tickSizeOuter ? \"M\" + range0 + \",\" + k * tickSizeOuter + \"V\" + offset + \"H\" + range1 + \"V\" + k * tickSizeOuter : \"M\" + range0 + \",\" + offset + \"H\" + range1));\n\n    tick\n        .attr(\"opacity\", 1)\n        .attr(\"transform\", function(d) { return transform(position(d) + offset); });\n\n    line\n        .attr(x + \"2\", k * tickSizeInner);\n\n    text\n        .attr(x, k * spacing)\n        .text(format);\n\n    selection.filter(entering)\n        .attr(\"fill\", \"none\")\n        .attr(\"font-size\", 10)\n        .attr(\"font-family\", \"sans-serif\")\n        .attr(\"text-anchor\", orient === right ? \"start\" : orient === left ? \"end\" : \"middle\");\n\n    selection\n        .each(function() { this.__axis = position; });\n  }\n\n  axis.scale = function(_) {\n    return arguments.length ? (scale = _, axis) : scale;\n  };\n\n  axis.ticks = function() {\n    return tickArguments = Array.from(arguments), axis;\n  };\n\n  axis.tickArguments = function(_) {\n    return arguments.length ? (tickArguments = _ == null ? [] : Array.from(_), axis) : tickArguments.slice();\n  };\n\n  axis.tickValues = function(_) {\n    return arguments.length ? (tickValues = _ == null ? null : Array.from(_), axis) : tickValues && tickValues.slice();\n  };\n\n  axis.tickFormat = function(_) {\n    return arguments.length ? (tickFormat = _, axis) : tickFormat;\n  };\n\n  axis.tickSize = function(_) {\n    return arguments.length ? (tickSizeInner = tickSizeOuter = +_, axis) : tickSizeInner;\n  };\n\n  axis.tickSizeInner = function(_) {\n    return arguments.length ? (tickSizeInner = +_, axis) : tickSizeInner;\n  };\n\n  axis.tickSizeOuter = function(_) {\n    return arguments.length ? (tickSizeOuter = +_, axis) : tickSizeOuter;\n  };\n\n  axis.tickPadding = function(_) {\n    return arguments.length ? (tickPadding = +_, axis) : tickPadding;\n  };\n\n  axis.offset = function(_) {\n    return arguments.length ? (offset = +_, axis) : offset;\n  };\n\n  return axis;\n}\n\nexport function axisTop(scale) {\n  return axis(top, scale);\n}\n\nexport function axisRight(scale) {\n  return axis(right, scale);\n}\n\nexport function axisBottom(scale) {\n  return axis(bottom, scale);\n}\n\nexport function axisLeft(scale) {\n  return axis(left, scale);\n}\n", "export {\n  axisTop,\n  axisRight,\n  axisBottom,\n  axisLeft\n} from \"./axis.js\";\n", "function none() {}\n\nexport default function(selector) {\n  return selector == null ? none : function() {\n    return this.querySelector(selector);\n  };\n}\n", "import {Selection} from \"./index.js\";\nimport selector from \"../selector.js\";\n\nexport default function(select) {\n  if (typeof select !== \"function\") select = selector(select);\n\n  for (var groups = this._groups, m = groups.length, subgroups = new Array(m), j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, subgroup = subgroups[j] = new Array(n), node, subnode, i = 0; i < n; ++i) {\n      if ((node = group[i]) && (subnode = select.call(node, node.__data__, i, group))) {\n        if (\"__data__\" in node) subnode.__data__ = node.__data__;\n        subgroup[i] = subnode;\n      }\n    }\n  }\n\n  return new Selection(subgroups, this._parents);\n}\n", "// Given something array like (or null), returns something that is strictly an\n// array. This is used to ensure that array-like objects passed to d3.selectAll\n// or selection.selectAll are converted into proper arrays when creating a\n// selection; we don’t ever want to create a selection backed by a live\n// HTMLCollection or NodeList. However, note that selection.selectAll will use a\n// static NodeList as a group, since it safely derived from querySelectorAll.\nexport default function array(x) {\n  return x == null ? [] : Array.isArray(x) ? x : Array.from(x);\n}\n", "function empty() {\n  return [];\n}\n\nexport default function(selector) {\n  return selector == null ? empty : function() {\n    return this.querySelectorAll(selector);\n  };\n}\n", "import {Selection} from \"./index.js\";\nimport array from \"../array.js\";\nimport selectorAll from \"../selectorAll.js\";\n\nfunction arrayAll(select) {\n  return function() {\n    return array(select.apply(this, arguments));\n  };\n}\n\nexport default function(select) {\n  if (typeof select === \"function\") select = arrayAll(select);\n  else select = selectorAll(select);\n\n  for (var groups = this._groups, m = groups.length, subgroups = [], parents = [], j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, node, i = 0; i < n; ++i) {\n      if (node = group[i]) {\n        subgroups.push(select.call(node, node.__data__, i, group));\n        parents.push(node);\n      }\n    }\n  }\n\n  return new Selection(subgroups, parents);\n}\n", "export default function(selector) {\n  return function() {\n    return this.matches(selector);\n  };\n}\n\nexport function childMatcher(selector) {\n  return function(node) {\n    return node.matches(selector);\n  };\n}\n\n", "import {childMatcher} from \"../matcher.js\";\n\nvar find = Array.prototype.find;\n\nfunction childFind(match) {\n  return function() {\n    return find.call(this.children, match);\n  };\n}\n\nfunction childFirst() {\n  return this.firstElementChild;\n}\n\nexport default function(match) {\n  return this.select(match == null ? childFirst\n      : childFind(typeof match === \"function\" ? match : childMatcher(match)));\n}\n", "import {childMatcher} from \"../matcher.js\";\n\nvar filter = Array.prototype.filter;\n\nfunction children() {\n  return Array.from(this.children);\n}\n\nfunction childrenFilter(match) {\n  return function() {\n    return filter.call(this.children, match);\n  };\n}\n\nexport default function(match) {\n  return this.selectAll(match == null ? children\n      : childrenFilter(typeof match === \"function\" ? match : childMatcher(match)));\n}\n", "import {Selection} from \"./index.js\";\nimport matcher from \"../matcher.js\";\n\nexport default function(match) {\n  if (typeof match !== \"function\") match = matcher(match);\n\n  for (var groups = this._groups, m = groups.length, subgroups = new Array(m), j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, subgroup = subgroups[j] = [], node, i = 0; i < n; ++i) {\n      if ((node = group[i]) && match.call(node, node.__data__, i, group)) {\n        subgroup.push(node);\n      }\n    }\n  }\n\n  return new Selection(subgroups, this._parents);\n}\n", "export default function(update) {\n  return new Array(update.length);\n}\n", "import sparse from \"./sparse.js\";\nimport {Selection} from \"./index.js\";\n\nexport default function() {\n  return new Selection(this._enter || this._groups.map(sparse), this._parents);\n}\n\nexport function EnterNode(parent, datum) {\n  this.ownerDocument = parent.ownerDocument;\n  this.namespaceURI = parent.namespaceURI;\n  this._next = null;\n  this._parent = parent;\n  this.__data__ = datum;\n}\n\nEnterNode.prototype = {\n  constructor: EnterNode,\n  appendChild: function(child) { return this._parent.insertBefore(child, this._next); },\n  insertBefore: function(child, next) { return this._parent.insertBefore(child, next); },\n  querySelector: function(selector) { return this._parent.querySelector(selector); },\n  querySelectorAll: function(selector) { return this._parent.querySelectorAll(selector); }\n};\n", "export default function(x) {\n  return function() {\n    return x;\n  };\n}\n", "import {Selection} from \"./index.js\";\nimport {EnterNode} from \"./enter.js\";\nimport constant from \"../constant.js\";\n\nfunction bindIndex(parent, group, enter, update, exit, data) {\n  var i = 0,\n      node,\n      groupLength = group.length,\n      dataLength = data.length;\n\n  // Put any non-null nodes that fit into update.\n  // Put any null nodes into enter.\n  // Put any remaining data into enter.\n  for (; i < dataLength; ++i) {\n    if (node = group[i]) {\n      node.__data__ = data[i];\n      update[i] = node;\n    } else {\n      enter[i] = new EnterNode(parent, data[i]);\n    }\n  }\n\n  // Put any non-null nodes that don’t fit into exit.\n  for (; i < groupLength; ++i) {\n    if (node = group[i]) {\n      exit[i] = node;\n    }\n  }\n}\n\nfunction bindKey(parent, group, enter, update, exit, data, key) {\n  var i,\n      node,\n      nodeByKeyValue = new Map,\n      groupLength = group.length,\n      dataLength = data.length,\n      keyValues = new Array(groupLength),\n      keyValue;\n\n  // Compute the key for each node.\n  // If multiple nodes have the same key, the duplicates are added to exit.\n  for (i = 0; i < groupLength; ++i) {\n    if (node = group[i]) {\n      keyValues[i] = keyValue = key.call(node, node.__data__, i, group) + \"\";\n      if (nodeByKeyValue.has(keyValue)) {\n        exit[i] = node;\n      } else {\n        nodeByKeyValue.set(keyValue, node);\n      }\n    }\n  }\n\n  // Compute the key for each datum.\n  // If there a node associated with this key, join and add it to update.\n  // If there is not (or the key is a duplicate), add it to enter.\n  for (i = 0; i < dataLength; ++i) {\n    keyValue = key.call(parent, data[i], i, data) + \"\";\n    if (node = nodeByKeyValue.get(keyValue)) {\n      update[i] = node;\n      node.__data__ = data[i];\n      nodeByKeyValue.delete(keyValue);\n    } else {\n      enter[i] = new EnterNode(parent, data[i]);\n    }\n  }\n\n  // Add any remaining nodes that were not bound to data to exit.\n  for (i = 0; i < groupLength; ++i) {\n    if ((node = group[i]) && (nodeByKeyValue.get(keyValues[i]) === node)) {\n      exit[i] = node;\n    }\n  }\n}\n\nfunction datum(node) {\n  return node.__data__;\n}\n\nexport default function(value, key) {\n  if (!arguments.length) return Array.from(this, datum);\n\n  var bind = key ? bindKey : bindIndex,\n      parents = this._parents,\n      groups = this._groups;\n\n  if (typeof value !== \"function\") value = constant(value);\n\n  for (var m = groups.length, update = new Array(m), enter = new Array(m), exit = new Array(m), j = 0; j < m; ++j) {\n    var parent = parents[j],\n        group = groups[j],\n        groupLength = group.length,\n        data = arraylike(value.call(parent, parent && parent.__data__, j, parents)),\n        dataLength = data.length,\n        enterGroup = enter[j] = new Array(dataLength),\n        updateGroup = update[j] = new Array(dataLength),\n        exitGroup = exit[j] = new Array(groupLength);\n\n    bind(parent, group, enterGroup, updateGroup, exitGroup, data, key);\n\n    // Now connect the enter nodes to their following update node, such that\n    // appendChild can insert the materialized enter node before this node,\n    // rather than at the end of the parent node.\n    for (var i0 = 0, i1 = 0, previous, next; i0 < dataLength; ++i0) {\n      if (previous = enterGroup[i0]) {\n        if (i0 >= i1) i1 = i0 + 1;\n        while (!(next = updateGroup[i1]) && ++i1 < dataLength);\n        previous._next = next || null;\n      }\n    }\n  }\n\n  update = new Selection(update, parents);\n  update._enter = enter;\n  update._exit = exit;\n  return update;\n}\n\n// Given some data, this returns an array-like view of it: an object that\n// exposes a length property and allows numeric indexing. Note that unlike\n// selectAll, this isn’t worried about “live” collections because the resulting\n// array will only be used briefly while data is being bound. (It is possible to\n// cause the data to change while iterating by using a key function, but please\n// don’t; we’d rather avoid a gratuitous copy.)\nfunction arraylike(data) {\n  return typeof data === \"object\" && \"length\" in data\n    ? data // Array, TypedArray, NodeList, array-like\n    : Array.from(data); // Map, Set, iterable, string, or anything else\n}\n", "import sparse from \"./sparse.js\";\nimport {Selection} from \"./index.js\";\n\nexport default function() {\n  return new Selection(this._exit || this._groups.map(sparse), this._parents);\n}\n", "export default function(onenter, onupdate, onexit) {\n  var enter = this.enter(), update = this, exit = this.exit();\n  if (typeof onenter === \"function\") {\n    enter = onenter(enter);\n    if (enter) enter = enter.selection();\n  } else {\n    enter = enter.append(onenter + \"\");\n  }\n  if (onupdate != null) {\n    update = onupdate(update);\n    if (update) update = update.selection();\n  }\n  if (onexit == null) exit.remove(); else onexit(exit);\n  return enter && update ? enter.merge(update).order() : update;\n}\n", "import {Selection} from \"./index.js\";\n\nexport default function(context) {\n  var selection = context.selection ? context.selection() : context;\n\n  for (var groups0 = this._groups, groups1 = selection._groups, m0 = groups0.length, m1 = groups1.length, m = Math.min(m0, m1), merges = new Array(m0), j = 0; j < m; ++j) {\n    for (var group0 = groups0[j], group1 = groups1[j], n = group0.length, merge = merges[j] = new Array(n), node, i = 0; i < n; ++i) {\n      if (node = group0[i] || group1[i]) {\n        merge[i] = node;\n      }\n    }\n  }\n\n  for (; j < m0; ++j) {\n    merges[j] = groups0[j];\n  }\n\n  return new Selection(merges, this._parents);\n}\n", "export default function() {\n\n  for (var groups = this._groups, j = -1, m = groups.length; ++j < m;) {\n    for (var group = groups[j], i = group.length - 1, next = group[i], node; --i >= 0;) {\n      if (node = group[i]) {\n        if (next && node.compareDocumentPosition(next) ^ 4) next.parentNode.insertBefore(node, next);\n        next = node;\n      }\n    }\n  }\n\n  return this;\n}\n", "import {Selection} from \"./index.js\";\n\nexport default function(compare) {\n  if (!compare) compare = ascending;\n\n  function compareNode(a, b) {\n    return a && b ? compare(a.__data__, b.__data__) : !a - !b;\n  }\n\n  for (var groups = this._groups, m = groups.length, sortgroups = new Array(m), j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, sortgroup = sortgroups[j] = new Array(n), node, i = 0; i < n; ++i) {\n      if (node = group[i]) {\n        sortgroup[i] = node;\n      }\n    }\n    sortgroup.sort(compareNode);\n  }\n\n  return new Selection(sortgroups, this._parents).order();\n}\n\nfunction ascending(a, b) {\n  return a < b ? -1 : a > b ? 1 : a >= b ? 0 : NaN;\n}\n", "export default function() {\n  var callback = arguments[0];\n  arguments[0] = this;\n  callback.apply(null, arguments);\n  return this;\n}\n", "export default function() {\n  return Array.from(this);\n}\n", "export default function() {\n\n  for (var groups = this._groups, j = 0, m = groups.length; j < m; ++j) {\n    for (var group = groups[j], i = 0, n = group.length; i < n; ++i) {\n      var node = group[i];\n      if (node) return node;\n    }\n  }\n\n  return null;\n}\n", "export default function() {\n  let size = 0;\n  for (const node of this) ++size; // eslint-disable-line no-unused-vars\n  return size;\n}\n", "export default function() {\n  return !this.node();\n}\n", "export default function(callback) {\n\n  for (var groups = this._groups, j = 0, m = groups.length; j < m; ++j) {\n    for (var group = groups[j], i = 0, n = group.length, node; i < n; ++i) {\n      if (node = group[i]) callback.call(node, node.__data__, i, group);\n    }\n  }\n\n  return this;\n}\n", "export var xhtml = \"http://www.w3.org/1999/xhtml\";\n\nexport default {\n  svg: \"http://www.w3.org/2000/svg\",\n  xhtml: xhtml,\n  xlink: \"http://www.w3.org/1999/xlink\",\n  xml: \"http://www.w3.org/XML/1998/namespace\",\n  xmlns: \"http://www.w3.org/2000/xmlns/\"\n};\n", "import namespaces from \"./namespaces.js\";\n\nexport default function(name) {\n  var prefix = name += \"\", i = prefix.indexOf(\":\");\n  if (i >= 0 && (prefix = name.slice(0, i)) !== \"xmlns\") name = name.slice(i + 1);\n  return namespaces.hasOwnProperty(prefix) ? {space: namespaces[prefix], local: name} : name; // eslint-disable-line no-prototype-builtins\n}\n", "import namespace from \"../namespace.js\";\n\nfunction attrRemove(name) {\n  return function() {\n    this.removeAttribute(name);\n  };\n}\n\nfunction attrRemoveNS(fullname) {\n  return function() {\n    this.removeAttributeNS(fullname.space, fullname.local);\n  };\n}\n\nfunction attrConstant(name, value) {\n  return function() {\n    this.setAttribute(name, value);\n  };\n}\n\nfunction attrConstantNS(fullname, value) {\n  return function() {\n    this.setAttributeNS(fullname.space, fullname.local, value);\n  };\n}\n\nfunction attrFunction(name, value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    if (v == null) this.removeAttribute(name);\n    else this.setAttribute(name, v);\n  };\n}\n\nfunction attrFunctionNS(fullname, value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    if (v == null) this.removeAttributeNS(fullname.space, fullname.local);\n    else this.setAttributeNS(fullname.space, fullname.local, v);\n  };\n}\n\nexport default function(name, value) {\n  var fullname = namespace(name);\n\n  if (arguments.length < 2) {\n    var node = this.node();\n    return fullname.local\n        ? node.getAttributeNS(fullname.space, fullname.local)\n        : node.getAttribute(fullname);\n  }\n\n  return this.each((value == null\n      ? (fullname.local ? attrRemoveNS : attrRemove) : (typeof value === \"function\"\n      ? (fullname.local ? attrFunctionNS : attrFunction)\n      : (fullname.local ? attrConstantNS : attrConstant)))(fullname, value));\n}\n", "export default function(node) {\n  return (node.ownerDocument && node.ownerDocument.defaultView) // node is a Node\n      || (node.document && node) // node is a Window\n      || node.defaultView; // node is a Document\n}\n", "import defaultView from \"../window.js\";\n\nfunction styleRemove(name) {\n  return function() {\n    this.style.removeProperty(name);\n  };\n}\n\nfunction styleConstant(name, value, priority) {\n  return function() {\n    this.style.setProperty(name, value, priority);\n  };\n}\n\nfunction styleFunction(name, value, priority) {\n  return function() {\n    var v = value.apply(this, arguments);\n    if (v == null) this.style.removeProperty(name);\n    else this.style.setProperty(name, v, priority);\n  };\n}\n\nexport default function(name, value, priority) {\n  return arguments.length > 1\n      ? this.each((value == null\n            ? styleRemove : typeof value === \"function\"\n            ? styleFunction\n            : styleConstant)(name, value, priority == null ? \"\" : priority))\n      : styleValue(this.node(), name);\n}\n\nexport function styleValue(node, name) {\n  return node.style.getPropertyValue(name)\n      || defaultView(node).getComputedStyle(node, null).getPropertyValue(name);\n}\n", "function propertyRemove(name) {\n  return function() {\n    delete this[name];\n  };\n}\n\nfunction propertyConstant(name, value) {\n  return function() {\n    this[name] = value;\n  };\n}\n\nfunction propertyFunction(name, value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    if (v == null) delete this[name];\n    else this[name] = v;\n  };\n}\n\nexport default function(name, value) {\n  return arguments.length > 1\n      ? this.each((value == null\n          ? propertyRemove : typeof value === \"function\"\n          ? propertyFunction\n          : propertyConstant)(name, value))\n      : this.node()[name];\n}\n", "function classArray(string) {\n  return string.trim().split(/^|\\s+/);\n}\n\nfunction classList(node) {\n  return node.classList || new ClassList(node);\n}\n\nfunction ClassList(node) {\n  this._node = node;\n  this._names = classArray(node.getAttribute(\"class\") || \"\");\n}\n\nClassList.prototype = {\n  add: function(name) {\n    var i = this._names.indexOf(name);\n    if (i < 0) {\n      this._names.push(name);\n      this._node.setAttribute(\"class\", this._names.join(\" \"));\n    }\n  },\n  remove: function(name) {\n    var i = this._names.indexOf(name);\n    if (i >= 0) {\n      this._names.splice(i, 1);\n      this._node.setAttribute(\"class\", this._names.join(\" \"));\n    }\n  },\n  contains: function(name) {\n    return this._names.indexOf(name) >= 0;\n  }\n};\n\nfunction classedAdd(node, names) {\n  var list = classList(node), i = -1, n = names.length;\n  while (++i < n) list.add(names[i]);\n}\n\nfunction classedRemove(node, names) {\n  var list = classList(node), i = -1, n = names.length;\n  while (++i < n) list.remove(names[i]);\n}\n\nfunction classedTrue(names) {\n  return function() {\n    classedAdd(this, names);\n  };\n}\n\nfunction classedFalse(names) {\n  return function() {\n    classedRemove(this, names);\n  };\n}\n\nfunction classedFunction(names, value) {\n  return function() {\n    (value.apply(this, arguments) ? classedAdd : classedRemove)(this, names);\n  };\n}\n\nexport default function(name, value) {\n  var names = classArray(name + \"\");\n\n  if (arguments.length < 2) {\n    var list = classList(this.node()), i = -1, n = names.length;\n    while (++i < n) if (!list.contains(names[i])) return false;\n    return true;\n  }\n\n  return this.each((typeof value === \"function\"\n      ? classedFunction : value\n      ? classedTrue\n      : classedFalse)(names, value));\n}\n", "function textRemove() {\n  this.textContent = \"\";\n}\n\nfunction textConstant(value) {\n  return function() {\n    this.textContent = value;\n  };\n}\n\nfunction textFunction(value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    this.textContent = v == null ? \"\" : v;\n  };\n}\n\nexport default function(value) {\n  return arguments.length\n      ? this.each(value == null\n          ? textRemove : (typeof value === \"function\"\n          ? textFunction\n          : textConstant)(value))\n      : this.node().textContent;\n}\n", "function htmlRemove() {\n  this.innerHTML = \"\";\n}\n\nfunction htmlConstant(value) {\n  return function() {\n    this.innerHTML = value;\n  };\n}\n\nfunction htmlFunction(value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    this.innerHTML = v == null ? \"\" : v;\n  };\n}\n\nexport default function(value) {\n  return arguments.length\n      ? this.each(value == null\n          ? htmlRemove : (typeof value === \"function\"\n          ? htmlFunction\n          : htmlConstant)(value))\n      : this.node().innerHTML;\n}\n", "function raise() {\n  if (this.nextSibling) this.parentNode.appendChild(this);\n}\n\nexport default function() {\n  return this.each(raise);\n}\n", "function lower() {\n  if (this.previousSibling) this.parentNode.insertBefore(this, this.parentNode.firstChild);\n}\n\nexport default function() {\n  return this.each(lower);\n}\n", "import namespace from \"./namespace.js\";\nimport {xhtml} from \"./namespaces.js\";\n\nfunction creatorInherit(name) {\n  return function() {\n    var document = this.ownerDocument,\n        uri = this.namespaceURI;\n    return uri === xhtml && document.documentElement.namespaceURI === xhtml\n        ? document.createElement(name)\n        : document.createElementNS(uri, name);\n  };\n}\n\nfunction creatorFixed(fullname) {\n  return function() {\n    return this.ownerDocument.createElementNS(fullname.space, fullname.local);\n  };\n}\n\nexport default function(name) {\n  var fullname = namespace(name);\n  return (fullname.local\n      ? creatorFixed\n      : creatorInherit)(fullname);\n}\n", "import creator from \"../creator.js\";\n\nexport default function(name) {\n  var create = typeof name === \"function\" ? name : creator(name);\n  return this.select(function() {\n    return this.appendChild(create.apply(this, arguments));\n  });\n}\n", "import creator from \"../creator.js\";\nimport selector from \"../selector.js\";\n\nfunction constantNull() {\n  return null;\n}\n\nexport default function(name, before) {\n  var create = typeof name === \"function\" ? name : creator(name),\n      select = before == null ? constantNull : typeof before === \"function\" ? before : selector(before);\n  return this.select(function() {\n    return this.insertBefore(create.apply(this, arguments), select.apply(this, arguments) || null);\n  });\n}\n", "function remove() {\n  var parent = this.parentNode;\n  if (parent) parent.removeChild(this);\n}\n\nexport default function() {\n  return this.each(remove);\n}\n", "function selection_cloneShallow() {\n  var clone = this.cloneNode(false), parent = this.parentNode;\n  return parent ? parent.insertBefore(clone, this.nextSibling) : clone;\n}\n\nfunction selection_cloneDeep() {\n  var clone = this.cloneNode(true), parent = this.parentNode;\n  return parent ? parent.insertBefore(clone, this.nextSibling) : clone;\n}\n\nexport default function(deep) {\n  return this.select(deep ? selection_cloneDeep : selection_cloneShallow);\n}\n", "export default function(value) {\n  return arguments.length\n      ? this.property(\"__data__\", value)\n      : this.node().__data__;\n}\n", "function contextListener(listener) {\n  return function(event) {\n    listener.call(this, event, this.__data__);\n  };\n}\n\nfunction parseTypenames(typenames) {\n  return typenames.trim().split(/^|\\s+/).map(function(t) {\n    var name = \"\", i = t.indexOf(\".\");\n    if (i >= 0) name = t.slice(i + 1), t = t.slice(0, i);\n    return {type: t, name: name};\n  });\n}\n\nfunction onRemove(typename) {\n  return function() {\n    var on = this.__on;\n    if (!on) return;\n    for (var j = 0, i = -1, m = on.length, o; j < m; ++j) {\n      if (o = on[j], (!typename.type || o.type === typename.type) && o.name === typename.name) {\n        this.removeEventListener(o.type, o.listener, o.options);\n      } else {\n        on[++i] = o;\n      }\n    }\n    if (++i) on.length = i;\n    else delete this.__on;\n  };\n}\n\nfunction onAdd(typename, value, options) {\n  return function() {\n    var on = this.__on, o, listener = contextListener(value);\n    if (on) for (var j = 0, m = on.length; j < m; ++j) {\n      if ((o = on[j]).type === typename.type && o.name === typename.name) {\n        this.removeEventListener(o.type, o.listener, o.options);\n        this.addEventListener(o.type, o.listener = listener, o.options = options);\n        o.value = value;\n        return;\n      }\n    }\n    this.addEventListener(typename.type, listener, options);\n    o = {type: typename.type, name: typename.name, value: value, listener: listener, options: options};\n    if (!on) this.__on = [o];\n    else on.push(o);\n  };\n}\n\nexport default function(typename, value, options) {\n  var typenames = parseTypenames(typename + \"\"), i, n = typenames.length, t;\n\n  if (arguments.length < 2) {\n    var on = this.node().__on;\n    if (on) for (var j = 0, m = on.length, o; j < m; ++j) {\n      for (i = 0, o = on[j]; i < n; ++i) {\n        if ((t = typenames[i]).type === o.type && t.name === o.name) {\n          return o.value;\n        }\n      }\n    }\n    return;\n  }\n\n  on = value ? onAdd : onRemove;\n  for (i = 0; i < n; ++i) this.each(on(typenames[i], value, options));\n  return this;\n}\n", "import defaultView from \"../window.js\";\n\nfunction dispatchEvent(node, type, params) {\n  var window = defaultView(node),\n      event = window.CustomEvent;\n\n  if (typeof event === \"function\") {\n    event = new event(type, params);\n  } else {\n    event = window.document.createEvent(\"Event\");\n    if (params) event.initEvent(type, params.bubbles, params.cancelable), event.detail = params.detail;\n    else event.initEvent(type, false, false);\n  }\n\n  node.dispatchEvent(event);\n}\n\nfunction dispatchConstant(type, params) {\n  return function() {\n    return dispatchEvent(this, type, params);\n  };\n}\n\nfunction dispatchFunction(type, params) {\n  return function() {\n    return dispatchEvent(this, type, params.apply(this, arguments));\n  };\n}\n\nexport default function(type, params) {\n  return this.each((typeof params === \"function\"\n      ? dispatchFunction\n      : dispatchConstant)(type, params));\n}\n", "export default function*() {\n  for (var groups = this._groups, j = 0, m = groups.length; j < m; ++j) {\n    for (var group = groups[j], i = 0, n = group.length, node; i < n; ++i) {\n      if (node = group[i]) yield node;\n    }\n  }\n}\n", "import selection_select from \"./select.js\";\nimport selection_selectAll from \"./selectAll.js\";\nimport selection_selectChild from \"./selectChild.js\";\nimport selection_selectChildren from \"./selectChildren.js\";\nimport selection_filter from \"./filter.js\";\nimport selection_data from \"./data.js\";\nimport selection_enter from \"./enter.js\";\nimport selection_exit from \"./exit.js\";\nimport selection_join from \"./join.js\";\nimport selection_merge from \"./merge.js\";\nimport selection_order from \"./order.js\";\nimport selection_sort from \"./sort.js\";\nimport selection_call from \"./call.js\";\nimport selection_nodes from \"./nodes.js\";\nimport selection_node from \"./node.js\";\nimport selection_size from \"./size.js\";\nimport selection_empty from \"./empty.js\";\nimport selection_each from \"./each.js\";\nimport selection_attr from \"./attr.js\";\nimport selection_style from \"./style.js\";\nimport selection_property from \"./property.js\";\nimport selection_classed from \"./classed.js\";\nimport selection_text from \"./text.js\";\nimport selection_html from \"./html.js\";\nimport selection_raise from \"./raise.js\";\nimport selection_lower from \"./lower.js\";\nimport selection_append from \"./append.js\";\nimport selection_insert from \"./insert.js\";\nimport selection_remove from \"./remove.js\";\nimport selection_clone from \"./clone.js\";\nimport selection_datum from \"./datum.js\";\nimport selection_on from \"./on.js\";\nimport selection_dispatch from \"./dispatch.js\";\nimport selection_iterator from \"./iterator.js\";\n\nexport var root = [null];\n\nexport function Selection(groups, parents) {\n  this._groups = groups;\n  this._parents = parents;\n}\n\nfunction selection() {\n  return new Selection([[document.documentElement]], root);\n}\n\nfunction selection_selection() {\n  return this;\n}\n\nSelection.prototype = selection.prototype = {\n  constructor: Selection,\n  select: selection_select,\n  selectAll: selection_selectAll,\n  selectChild: selection_selectChild,\n  selectChildren: selection_selectChildren,\n  filter: selection_filter,\n  data: selection_data,\n  enter: selection_enter,\n  exit: selection_exit,\n  join: selection_join,\n  merge: selection_merge,\n  selection: selection_selection,\n  order: selection_order,\n  sort: selection_sort,\n  call: selection_call,\n  nodes: selection_nodes,\n  node: selection_node,\n  size: selection_size,\n  empty: selection_empty,\n  each: selection_each,\n  attr: selection_attr,\n  style: selection_style,\n  property: selection_property,\n  classed: selection_classed,\n  text: selection_text,\n  html: selection_html,\n  raise: selection_raise,\n  lower: selection_lower,\n  append: selection_append,\n  insert: selection_insert,\n  remove: selection_remove,\n  clone: selection_clone,\n  datum: selection_datum,\n  on: selection_on,\n  dispatch: selection_dispatch,\n  [Symbol.iterator]: selection_iterator\n};\n\nexport default selection;\n", "import {Timer} from \"./timer.js\";\n\nexport default function(callback, delay, time) {\n  var t = new Timer;\n  delay = delay == null ? 0 : +delay;\n  t.restart(elapsed => {\n    t.stop();\n    callback(elapsed + delay);\n  }, delay, time);\n  return t;\n}\n", "import {dispatch} from \"d3-dispatch\";\nimport {timer, timeout} from \"d3-timer\";\n\nvar emptyOn = dispatch(\"start\", \"end\", \"cancel\", \"interrupt\");\nvar emptyTween = [];\n\nexport var CREATED = 0;\nexport var SCHEDULED = 1;\nexport var STARTING = 2;\nexport var STARTED = 3;\nexport var RUNNING = 4;\nexport var ENDING = 5;\nexport var ENDED = 6;\n\nexport default function(node, name, id, index, group, timing) {\n  var schedules = node.__transition;\n  if (!schedules) node.__transition = {};\n  else if (id in schedules) return;\n  create(node, id, {\n    name: name,\n    index: index, // For context during callback.\n    group: group, // For context during callback.\n    on: emptyOn,\n    tween: emptyTween,\n    time: timing.time,\n    delay: timing.delay,\n    duration: timing.duration,\n    ease: timing.ease,\n    timer: null,\n    state: CREATED\n  });\n}\n\nexport function init(node, id) {\n  var schedule = get(node, id);\n  if (schedule.state > CREATED) throw new Error(\"too late; already scheduled\");\n  return schedule;\n}\n\nexport function set(node, id) {\n  var schedule = get(node, id);\n  if (schedule.state > STARTED) throw new Error(\"too late; already running\");\n  return schedule;\n}\n\nexport function get(node, id) {\n  var schedule = node.__transition;\n  if (!schedule || !(schedule = schedule[id])) throw new Error(\"transition not found\");\n  return schedule;\n}\n\nfunction create(node, id, self) {\n  var schedules = node.__transition,\n      tween;\n\n  // Initialize the self timer when the transition is created.\n  // Note the actual delay is not known until the first callback!\n  schedules[id] = self;\n  self.timer = timer(schedule, 0, self.time);\n\n  function schedule(elapsed) {\n    self.state = SCHEDULED;\n    self.timer.restart(start, self.delay, self.time);\n\n    // If the elapsed delay is less than our first sleep, start immediately.\n    if (self.delay <= elapsed) start(elapsed - self.delay);\n  }\n\n  function start(elapsed) {\n    var i, j, n, o;\n\n    // If the state is not SCHEDULED, then we previously errored on start.\n    if (self.state !== SCHEDULED) return stop();\n\n    for (i in schedules) {\n      o = schedules[i];\n      if (o.name !== self.name) continue;\n\n      // While this element already has a starting transition during this frame,\n      // defer starting an interrupting transition until that transition has a\n      // chance to tick (and possibly end); see d3/d3-transition#54!\n      if (o.state === STARTED) return timeout(start);\n\n      // Interrupt the active transition, if any.\n      if (o.state === RUNNING) {\n        o.state = ENDED;\n        o.timer.stop();\n        o.on.call(\"interrupt\", node, node.__data__, o.index, o.group);\n        delete schedules[i];\n      }\n\n      // Cancel any pre-empted transitions.\n      else if (+i < id) {\n        o.state = ENDED;\n        o.timer.stop();\n        o.on.call(\"cancel\", node, node.__data__, o.index, o.group);\n        delete schedules[i];\n      }\n    }\n\n    // Defer the first tick to end of the current frame; see d3/d3#1576.\n    // Note the transition may be canceled after start and before the first tick!\n    // Note this must be scheduled before the start event; see d3/d3-transition#16!\n    // Assuming this is successful, subsequent callbacks go straight to tick.\n    timeout(function() {\n      if (self.state === STARTED) {\n        self.state = RUNNING;\n        self.timer.restart(tick, self.delay, self.time);\n        tick(elapsed);\n      }\n    });\n\n    // Dispatch the start event.\n    // Note this must be done before the tween are initialized.\n    self.state = STARTING;\n    self.on.call(\"start\", node, node.__data__, self.index, self.group);\n    if (self.state !== STARTING) return; // interrupted\n    self.state = STARTED;\n\n    // Initialize the tween, deleting null tween.\n    tween = new Array(n = self.tween.length);\n    for (i = 0, j = -1; i < n; ++i) {\n      if (o = self.tween[i].value.call(node, node.__data__, self.index, self.group)) {\n        tween[++j] = o;\n      }\n    }\n    tween.length = j + 1;\n  }\n\n  function tick(elapsed) {\n    var t = elapsed < self.duration ? self.ease.call(null, elapsed / self.duration) : (self.timer.restart(stop), self.state = ENDING, 1),\n        i = -1,\n        n = tween.length;\n\n    while (++i < n) {\n      tween[i].call(node, t);\n    }\n\n    // Dispatch the end event.\n    if (self.state === ENDING) {\n      self.on.call(\"end\", node, node.__data__, self.index, self.group);\n      stop();\n    }\n  }\n\n  function stop() {\n    self.state = ENDED;\n    self.timer.stop();\n    delete schedules[id];\n    for (var i in schedules) return; // eslint-disable-line no-unused-vars\n    delete node.__transition;\n  }\n}\n", "import {STARTING, ENDING, ENDED} from \"./transition/schedule.js\";\n\nexport default function(node, name) {\n  var schedules = node.__transition,\n      schedule,\n      active,\n      empty = true,\n      i;\n\n  if (!schedules) return;\n\n  name = name == null ? null : name + \"\";\n\n  for (i in schedules) {\n    if ((schedule = schedules[i]).name !== name) { empty = false; continue; }\n    active = schedule.state > STARTING && schedule.state < ENDING;\n    schedule.state = ENDED;\n    schedule.timer.stop();\n    schedule.on.call(active ? \"interrupt\" : \"cancel\", node, node.__data__, schedule.index, schedule.group);\n    delete schedules[i];\n  }\n\n  if (empty) delete node.__transition;\n}\n", "import interrupt from \"../interrupt.js\";\n\nexport default function(name) {\n  return this.each(function() {\n    interrupt(this, name);\n  });\n}\n", "import {get, set} from \"./schedule.js\";\n\nfunction tweenRemove(id, name) {\n  var tween0, tween1;\n  return function() {\n    var schedule = set(this, id),\n        tween = schedule.tween;\n\n    // If this node shared tween with the previous node,\n    // just assign the updated shared tween and we’re done!\n    // Otherwise, copy-on-write.\n    if (tween !== tween0) {\n      tween1 = tween0 = tween;\n      for (var i = 0, n = tween1.length; i < n; ++i) {\n        if (tween1[i].name === name) {\n          tween1 = tween1.slice();\n          tween1.splice(i, 1);\n          break;\n        }\n      }\n    }\n\n    schedule.tween = tween1;\n  };\n}\n\nfunction tweenFunction(id, name, value) {\n  var tween0, tween1;\n  if (typeof value !== \"function\") throw new Error;\n  return function() {\n    var schedule = set(this, id),\n        tween = schedule.tween;\n\n    // If this node shared tween with the previous node,\n    // just assign the updated shared tween and we’re done!\n    // Otherwise, copy-on-write.\n    if (tween !== tween0) {\n      tween1 = (tween0 = tween).slice();\n      for (var t = {name: name, value: value}, i = 0, n = tween1.length; i < n; ++i) {\n        if (tween1[i].name === name) {\n          tween1[i] = t;\n          break;\n        }\n      }\n      if (i === n) tween1.push(t);\n    }\n\n    schedule.tween = tween1;\n  };\n}\n\nexport default function(name, value) {\n  var id = this._id;\n\n  name += \"\";\n\n  if (arguments.length < 2) {\n    var tween = get(this.node(), id).tween;\n    for (var i = 0, n = tween.length, t; i < n; ++i) {\n      if ((t = tween[i]).name === name) {\n        return t.value;\n      }\n    }\n    return null;\n  }\n\n  return this.each((value == null ? tweenRemove : tweenFunction)(id, name, value));\n}\n\nexport function tweenValue(transition, name, value) {\n  var id = transition._id;\n\n  transition.each(function() {\n    var schedule = set(this, id);\n    (schedule.value || (schedule.value = {}))[name] = value.apply(this, arguments);\n  });\n\n  return function(node) {\n    return get(node, id).value[name];\n  };\n}\n", "import {color} from \"d3-color\";\nimport {interpolateNumber, interpolateRgb, interpolateString} from \"d3-interpolate\";\n\nexport default function(a, b) {\n  var c;\n  return (typeof b === \"number\" ? interpolateNumber\n      : b instanceof color ? interpolateRgb\n      : (c = color(b)) ? (b = c, interpolateRgb)\n      : interpolateString)(a, b);\n}\n", "import {interpolateTransformSvg as interpolateTransform} from \"d3-interpolate\";\nimport {namespace} from \"d3-selection\";\nimport {tweenValue} from \"./tween.js\";\nimport interpolate from \"./interpolate.js\";\n\nfunction attrRemove(name) {\n  return function() {\n    this.removeAttribute(name);\n  };\n}\n\nfunction attrRemoveNS(fullname) {\n  return function() {\n    this.removeAttributeNS(fullname.space, fullname.local);\n  };\n}\n\nfunction attrConstant(name, interpolate, value1) {\n  var string00,\n      string1 = value1 + \"\",\n      interpolate0;\n  return function() {\n    var string0 = this.getAttribute(name);\n    return string0 === string1 ? null\n        : string0 === string00 ? interpolate0\n        : interpolate0 = interpolate(string00 = string0, value1);\n  };\n}\n\nfunction attrConstantNS(fullname, interpolate, value1) {\n  var string00,\n      string1 = value1 + \"\",\n      interpolate0;\n  return function() {\n    var string0 = this.getAttributeNS(fullname.space, fullname.local);\n    return string0 === string1 ? null\n        : string0 === string00 ? interpolate0\n        : interpolate0 = interpolate(string00 = string0, value1);\n  };\n}\n\nfunction attrFunction(name, interpolate, value) {\n  var string00,\n      string10,\n      interpolate0;\n  return function() {\n    var string0, value1 = value(this), string1;\n    if (value1 == null) return void this.removeAttribute(name);\n    string0 = this.getAttribute(name);\n    string1 = value1 + \"\";\n    return string0 === string1 ? null\n        : string0 === string00 && string1 === string10 ? interpolate0\n        : (string10 = string1, interpolate0 = interpolate(string00 = string0, value1));\n  };\n}\n\nfunction attrFunctionNS(fullname, interpolate, value) {\n  var string00,\n      string10,\n      interpolate0;\n  return function() {\n    var string0, value1 = value(this), string1;\n    if (value1 == null) return void this.removeAttributeNS(fullname.space, fullname.local);\n    string0 = this.getAttributeNS(fullname.space, fullname.local);\n    string1 = value1 + \"\";\n    return string0 === string1 ? null\n        : string0 === string00 && string1 === string10 ? interpolate0\n        : (string10 = string1, interpolate0 = interpolate(string00 = string0, value1));\n  };\n}\n\nexport default function(name, value) {\n  var fullname = namespace(name), i = fullname === \"transform\" ? interpolateTransform : interpolate;\n  return this.attrTween(name, typeof value === \"function\"\n      ? (fullname.local ? attrFunctionNS : attrFunction)(fullname, i, tweenValue(this, \"attr.\" + name, value))\n      : value == null ? (fullname.local ? attrRemoveNS : attrRemove)(fullname)\n      : (fullname.local ? attrConstantNS : attrConstant)(fullname, i, value));\n}\n", "import {namespace} from \"d3-selection\";\n\nfunction attrInterpolate(name, i) {\n  return function(t) {\n    this.setAttribute(name, i.call(this, t));\n  };\n}\n\nfunction attrInterpolateNS(fullname, i) {\n  return function(t) {\n    this.setAttributeNS(fullname.space, fullname.local, i.call(this, t));\n  };\n}\n\nfunction attrTweenNS(fullname, value) {\n  var t0, i0;\n  function tween() {\n    var i = value.apply(this, arguments);\n    if (i !== i0) t0 = (i0 = i) && attrInterpolateNS(fullname, i);\n    return t0;\n  }\n  tween._value = value;\n  return tween;\n}\n\nfunction attrTween(name, value) {\n  var t0, i0;\n  function tween() {\n    var i = value.apply(this, arguments);\n    if (i !== i0) t0 = (i0 = i) && attrInterpolate(name, i);\n    return t0;\n  }\n  tween._value = value;\n  return tween;\n}\n\nexport default function(name, value) {\n  var key = \"attr.\" + name;\n  if (arguments.length < 2) return (key = this.tween(key)) && key._value;\n  if (value == null) return this.tween(key, null);\n  if (typeof value !== \"function\") throw new Error;\n  var fullname = namespace(name);\n  return this.tween(key, (fullname.local ? attrTweenNS : attrTween)(fullname, value));\n}\n", "import {get, init} from \"./schedule.js\";\n\nfunction delayFunction(id, value) {\n  return function() {\n    init(this, id).delay = +value.apply(this, arguments);\n  };\n}\n\nfunction delayConstant(id, value) {\n  return value = +value, function() {\n    init(this, id).delay = value;\n  };\n}\n\nexport default function(value) {\n  var id = this._id;\n\n  return arguments.length\n      ? this.each((typeof value === \"function\"\n          ? delayFunction\n          : delayConstant)(id, value))\n      : get(this.node(), id).delay;\n}\n", "import {get, set} from \"./schedule.js\";\n\nfunction durationFunction(id, value) {\n  return function() {\n    set(this, id).duration = +value.apply(this, arguments);\n  };\n}\n\nfunction durationConstant(id, value) {\n  return value = +value, function() {\n    set(this, id).duration = value;\n  };\n}\n\nexport default function(value) {\n  var id = this._id;\n\n  return arguments.length\n      ? this.each((typeof value === \"function\"\n          ? durationFunction\n          : durationConstant)(id, value))\n      : get(this.node(), id).duration;\n}\n", "import {get, set} from \"./schedule.js\";\n\nfunction easeConstant(id, value) {\n  if (typeof value !== \"function\") throw new Error;\n  return function() {\n    set(this, id).ease = value;\n  };\n}\n\nexport default function(value) {\n  var id = this._id;\n\n  return arguments.length\n      ? this.each(easeConstant(id, value))\n      : get(this.node(), id).ease;\n}\n", "import {set} from \"./schedule.js\";\n\nfunction easeVarying(id, value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    if (typeof v !== \"function\") throw new Error;\n    set(this, id).ease = v;\n  };\n}\n\nexport default function(value) {\n  if (typeof value !== \"function\") throw new Error;\n  return this.each(easeVarying(this._id, value));\n}\n", "import {matcher} from \"d3-selection\";\nimport {Transition} from \"./index.js\";\n\nexport default function(match) {\n  if (typeof match !== \"function\") match = matcher(match);\n\n  for (var groups = this._groups, m = groups.length, subgroups = new Array(m), j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, subgroup = subgroups[j] = [], node, i = 0; i < n; ++i) {\n      if ((node = group[i]) && match.call(node, node.__data__, i, group)) {\n        subgroup.push(node);\n      }\n    }\n  }\n\n  return new Transition(subgroups, this._parents, this._name, this._id);\n}\n", "import {Transition} from \"./index.js\";\n\nexport default function(transition) {\n  if (transition._id !== this._id) throw new Error;\n\n  for (var groups0 = this._groups, groups1 = transition._groups, m0 = groups0.length, m1 = groups1.length, m = Math.min(m0, m1), merges = new Array(m0), j = 0; j < m; ++j) {\n    for (var group0 = groups0[j], group1 = groups1[j], n = group0.length, merge = merges[j] = new Array(n), node, i = 0; i < n; ++i) {\n      if (node = group0[i] || group1[i]) {\n        merge[i] = node;\n      }\n    }\n  }\n\n  for (; j < m0; ++j) {\n    merges[j] = groups0[j];\n  }\n\n  return new Transition(merges, this._parents, this._name, this._id);\n}\n", "import {get, set, init} from \"./schedule.js\";\n\nfunction start(name) {\n  return (name + \"\").trim().split(/^|\\s+/).every(function(t) {\n    var i = t.indexOf(\".\");\n    if (i >= 0) t = t.slice(0, i);\n    return !t || t === \"start\";\n  });\n}\n\nfunction onFunction(id, name, listener) {\n  var on0, on1, sit = start(name) ? init : set;\n  return function() {\n    var schedule = sit(this, id),\n        on = schedule.on;\n\n    // If this node shared a dispatch with the previous node,\n    // just assign the updated shared dispatch and we’re done!\n    // Otherwise, copy-on-write.\n    if (on !== on0) (on1 = (on0 = on).copy()).on(name, listener);\n\n    schedule.on = on1;\n  };\n}\n\nexport default function(name, listener) {\n  var id = this._id;\n\n  return arguments.length < 2\n      ? get(this.node(), id).on.on(name)\n      : this.each(onFunction(id, name, listener));\n}\n", "function removeFunction(id) {\n  return function() {\n    var parent = this.parentNode;\n    for (var i in this.__transition) if (+i !== id) return;\n    if (parent) parent.removeChild(this);\n  };\n}\n\nexport default function() {\n  return this.on(\"end.remove\", removeFunction(this._id));\n}\n", "import {selector} from \"d3-selection\";\nimport {Transition} from \"./index.js\";\nimport schedule, {get} from \"./schedule.js\";\n\nexport default function(select) {\n  var name = this._name,\n      id = this._id;\n\n  if (typeof select !== \"function\") select = selector(select);\n\n  for (var groups = this._groups, m = groups.length, subgroups = new Array(m), j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, subgroup = subgroups[j] = new Array(n), node, subnode, i = 0; i < n; ++i) {\n      if ((node = group[i]) && (subnode = select.call(node, node.__data__, i, group))) {\n        if (\"__data__\" in node) subnode.__data__ = node.__data__;\n        subgroup[i] = subnode;\n        schedule(subgroup[i], name, id, i, subgroup, get(node, id));\n      }\n    }\n  }\n\n  return new Transition(subgroups, this._parents, name, id);\n}\n", "import {selectorAll} from \"d3-selection\";\nimport {Transition} from \"./index.js\";\nimport schedule, {get} from \"./schedule.js\";\n\nexport default function(select) {\n  var name = this._name,\n      id = this._id;\n\n  if (typeof select !== \"function\") select = selectorAll(select);\n\n  for (var groups = this._groups, m = groups.length, subgroups = [], parents = [], j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, node, i = 0; i < n; ++i) {\n      if (node = group[i]) {\n        for (var children = select.call(node, node.__data__, i, group), child, inherit = get(node, id), k = 0, l = children.length; k < l; ++k) {\n          if (child = children[k]) {\n            schedule(child, name, id, k, children, inherit);\n          }\n        }\n        subgroups.push(children);\n        parents.push(node);\n      }\n    }\n  }\n\n  return new Transition(subgroups, parents, name, id);\n}\n", "import {selection} from \"d3-selection\";\n\nvar Selection = selection.prototype.constructor;\n\nexport default function() {\n  return new Selection(this._groups, this._parents);\n}\n", "import {interpolateTransformCss as interpolateTransform} from \"d3-interpolate\";\nimport {style} from \"d3-selection\";\nimport {set} from \"./schedule.js\";\nimport {tweenValue} from \"./tween.js\";\nimport interpolate from \"./interpolate.js\";\n\nfunction styleNull(name, interpolate) {\n  var string00,\n      string10,\n      interpolate0;\n  return function() {\n    var string0 = style(this, name),\n        string1 = (this.style.removeProperty(name), style(this, name));\n    return string0 === string1 ? null\n        : string0 === string00 && string1 === string10 ? interpolate0\n        : interpolate0 = interpolate(string00 = string0, string10 = string1);\n  };\n}\n\nfunction styleRemove(name) {\n  return function() {\n    this.style.removeProperty(name);\n  };\n}\n\nfunction styleConstant(name, interpolate, value1) {\n  var string00,\n      string1 = value1 + \"\",\n      interpolate0;\n  return function() {\n    var string0 = style(this, name);\n    return string0 === string1 ? null\n        : string0 === string00 ? interpolate0\n        : interpolate0 = interpolate(string00 = string0, value1);\n  };\n}\n\nfunction styleFunction(name, interpolate, value) {\n  var string00,\n      string10,\n      interpolate0;\n  return function() {\n    var string0 = style(this, name),\n        value1 = value(this),\n        string1 = value1 + \"\";\n    if (value1 == null) string1 = value1 = (this.style.removeProperty(name), style(this, name));\n    return string0 === string1 ? null\n        : string0 === string00 && string1 === string10 ? interpolate0\n        : (string10 = string1, interpolate0 = interpolate(string00 = string0, value1));\n  };\n}\n\nfunction styleMaybeRemove(id, name) {\n  var on0, on1, listener0, key = \"style.\" + name, event = \"end.\" + key, remove;\n  return function() {\n    var schedule = set(this, id),\n        on = schedule.on,\n        listener = schedule.value[key] == null ? remove || (remove = styleRemove(name)) : undefined;\n\n    // If this node shared a dispatch with the previous node,\n    // just assign the updated shared dispatch and we’re done!\n    // Otherwise, copy-on-write.\n    if (on !== on0 || listener0 !== listener) (on1 = (on0 = on).copy()).on(event, listener0 = listener);\n\n    schedule.on = on1;\n  };\n}\n\nexport default function(name, value, priority) {\n  var i = (name += \"\") === \"transform\" ? interpolateTransform : interpolate;\n  return value == null ? this\n      .styleTween(name, styleNull(name, i))\n      .on(\"end.style.\" + name, styleRemove(name))\n    : typeof value === \"function\" ? this\n      .styleTween(name, styleFunction(name, i, tweenValue(this, \"style.\" + name, value)))\n      .each(styleMaybeRemove(this._id, name))\n    : this\n      .styleTween(name, styleConstant(name, i, value), priority)\n      .on(\"end.style.\" + name, null);\n}\n", "function styleInterpolate(name, i, priority) {\n  return function(t) {\n    this.style.setProperty(name, i.call(this, t), priority);\n  };\n}\n\nfunction styleTween(name, value, priority) {\n  var t, i0;\n  function tween() {\n    var i = value.apply(this, arguments);\n    if (i !== i0) t = (i0 = i) && styleInterpolate(name, i, priority);\n    return t;\n  }\n  tween._value = value;\n  return tween;\n}\n\nexport default function(name, value, priority) {\n  var key = \"style.\" + (name += \"\");\n  if (arguments.length < 2) return (key = this.tween(key)) && key._value;\n  if (value == null) return this.tween(key, null);\n  if (typeof value !== \"function\") throw new Error;\n  return this.tween(key, styleTween(name, value, priority == null ? \"\" : priority));\n}\n", "import {tweenValue} from \"./tween.js\";\n\nfunction textConstant(value) {\n  return function() {\n    this.textContent = value;\n  };\n}\n\nfunction textFunction(value) {\n  return function() {\n    var value1 = value(this);\n    this.textContent = value1 == null ? \"\" : value1;\n  };\n}\n\nexport default function(value) {\n  return this.tween(\"text\", typeof value === \"function\"\n      ? textFunction(tweenValue(this, \"text\", value))\n      : textConstant(value == null ? \"\" : value + \"\"));\n}\n", "function textInterpolate(i) {\n  return function(t) {\n    this.textContent = i.call(this, t);\n  };\n}\n\nfunction textTween(value) {\n  var t0, i0;\n  function tween() {\n    var i = value.apply(this, arguments);\n    if (i !== i0) t0 = (i0 = i) && textInterpolate(i);\n    return t0;\n  }\n  tween._value = value;\n  return tween;\n}\n\nexport default function(value) {\n  var key = \"text\";\n  if (arguments.length < 1) return (key = this.tween(key)) && key._value;\n  if (value == null) return this.tween(key, null);\n  if (typeof value !== \"function\") throw new Error;\n  return this.tween(key, textTween(value));\n}\n", "import {Transition, newId} from \"./index.js\";\nimport schedule, {get} from \"./schedule.js\";\n\nexport default function() {\n  var name = this._name,\n      id0 = this._id,\n      id1 = newId();\n\n  for (var groups = this._groups, m = groups.length, j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, node, i = 0; i < n; ++i) {\n      if (node = group[i]) {\n        var inherit = get(node, id0);\n        schedule(node, name, id1, i, group, {\n          time: inherit.time + inherit.delay + inherit.duration,\n          delay: 0,\n          duration: inherit.duration,\n          ease: inherit.ease\n        });\n      }\n    }\n  }\n\n  return new Transition(groups, this._parents, name, id1);\n}\n", "import {set} from \"./schedule.js\";\n\nexport default function() {\n  var on0, on1, that = this, id = that._id, size = that.size();\n  return new Promise(function(resolve, reject) {\n    var cancel = {value: reject},\n        end = {value: function() { if (--size === 0) resolve(); }};\n\n    that.each(function() {\n      var schedule = set(this, id),\n          on = schedule.on;\n\n      // If this node shared a dispatch with the previous node,\n      // just assign the updated shared dispatch and we’re done!\n      // Otherwise, copy-on-write.\n      if (on !== on0) {\n        on1 = (on0 = on).copy();\n        on1._.cancel.push(cancel);\n        on1._.interrupt.push(cancel);\n        on1._.end.push(end);\n      }\n\n      schedule.on = on1;\n    });\n\n    // The selection was empty, resolve end immediately\n    if (size === 0) resolve();\n  });\n}\n", "import {selection} from \"d3-selection\";\nimport transition_attr from \"./attr.js\";\nimport transition_attrTween from \"./attrTween.js\";\nimport transition_delay from \"./delay.js\";\nimport transition_duration from \"./duration.js\";\nimport transition_ease from \"./ease.js\";\nimport transition_easeVarying from \"./easeVarying.js\";\nimport transition_filter from \"./filter.js\";\nimport transition_merge from \"./merge.js\";\nimport transition_on from \"./on.js\";\nimport transition_remove from \"./remove.js\";\nimport transition_select from \"./select.js\";\nimport transition_selectAll from \"./selectAll.js\";\nimport transition_selection from \"./selection.js\";\nimport transition_style from \"./style.js\";\nimport transition_styleTween from \"./styleTween.js\";\nimport transition_text from \"./text.js\";\nimport transition_textTween from \"./textTween.js\";\nimport transition_transition from \"./transition.js\";\nimport transition_tween from \"./tween.js\";\nimport transition_end from \"./end.js\";\n\nvar id = 0;\n\nexport function Transition(groups, parents, name, id) {\n  this._groups = groups;\n  this._parents = parents;\n  this._name = name;\n  this._id = id;\n}\n\nexport default function transition(name) {\n  return selection().transition(name);\n}\n\nexport function newId() {\n  return ++id;\n}\n\nvar selection_prototype = selection.prototype;\n\nTransition.prototype = transition.prototype = {\n  constructor: Transition,\n  select: transition_select,\n  selectAll: transition_selectAll,\n  selectChild: selection_prototype.selectChild,\n  selectChildren: selection_prototype.selectChildren,\n  filter: transition_filter,\n  merge: transition_merge,\n  selection: transition_selection,\n  transition: transition_transition,\n  call: selection_prototype.call,\n  nodes: selection_prototype.nodes,\n  node: selection_prototype.node,\n  size: selection_prototype.size,\n  empty: selection_prototype.empty,\n  each: selection_prototype.each,\n  on: transition_on,\n  attr: transition_attr,\n  attrTween: transition_attrTween,\n  style: transition_style,\n  styleTween: transition_styleTween,\n  text: transition_text,\n  textTween: transition_textTween,\n  remove: transition_remove,\n  tween: transition_tween,\n  delay: transition_delay,\n  duration: transition_duration,\n  ease: transition_ease,\n  easeVarying: transition_easeVarying,\n  end: transition_end,\n  [Symbol.iterator]: selection_prototype[Symbol.iterator]\n};\n", "export function cubicIn(t) {\n  return t * t * t;\n}\n\nexport function cubicOut(t) {\n  return --t * t * t + 1;\n}\n\nexport function cubicInOut(t) {\n  return ((t *= 2) <= 1 ? t * t * t : (t -= 2) * t * t + 2) / 2;\n}\n", "import {Transition, newId} from \"../transition/index.js\";\nimport schedule from \"../transition/schedule.js\";\nimport {easeCubicInOut} from \"d3-ease\";\nimport {now} from \"d3-timer\";\n\nvar defaultTiming = {\n  time: null, // Set on use.\n  delay: 0,\n  duration: 250,\n  ease: easeCubicInOut\n};\n\nfunction inherit(node, id) {\n  var timing;\n  while (!(timing = node.__transition) || !(timing = timing[id])) {\n    if (!(node = node.parentNode)) {\n      throw new Error(`transition ${id} not found`);\n    }\n  }\n  return timing;\n}\n\nexport default function(name) {\n  var id,\n      timing;\n\n  if (name instanceof Transition) {\n    id = name._id, name = name._name;\n  } else {\n    id = newId(), (timing = defaultTiming).time = now(), name = name == null ? null : name + \"\";\n  }\n\n  for (var groups = this._groups, m = groups.length, j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, node, i = 0; i < n; ++i) {\n      if (node = group[i]) {\n        schedule(node, name, id, i, group, timing || inherit(node, id));\n      }\n    }\n  }\n\n  return new Transition(groups, this._parents, name, id);\n}\n", "import {selection} from \"d3-selection\";\nimport selection_interrupt from \"./interrupt.js\";\nimport selection_transition from \"./transition.js\";\n\nselection.prototype.interrupt = selection_interrupt;\nselection.prototype.transition = selection_transition;\n", "import \"./selection/index.js\";\nexport {default as transition} from \"./transition/index.js\";\nexport {default as active} from \"./active.js\";\nexport {default as interrupt} from \"./interrupt.js\";\n", "import {dispatch} from \"d3-dispatch\";\nimport {dragDisable, dragEnable} from \"d3-drag\";\nimport {interpolate} from \"d3-interpolate\";\nimport {pointer, select} from \"d3-selection\";\nimport {interrupt} from \"d3-transition\";\nimport constant from \"./constant.js\";\nimport BrushEvent from \"./event.js\";\nimport noevent, {nopropagation} from \"./noevent.js\";\n\nvar MODE_DRAG = {name: \"drag\"},\n    MODE_SPACE = {name: \"space\"},\n    MODE_HANDLE = {name: \"handle\"},\n    MODE_CENTER = {name: \"center\"};\n\nconst {abs, max, min} = Math;\n\nfunction number1(e) {\n  return [+e[0], +e[1]];\n}\n\nfunction number2(e) {\n  return [number1(e[0]), number1(e[1])];\n}\n\nvar X = {\n  name: \"x\",\n  handles: [\"w\", \"e\"].map(type),\n  input: function(x, e) { return x == null ? null : [[+x[0], e[0][1]], [+x[1], e[1][1]]]; },\n  output: function(xy) { return xy && [xy[0][0], xy[1][0]]; }\n};\n\nvar Y = {\n  name: \"y\",\n  handles: [\"n\", \"s\"].map(type),\n  input: function(y, e) { return y == null ? null : [[e[0][0], +y[0]], [e[1][0], +y[1]]]; },\n  output: function(xy) { return xy && [xy[0][1], xy[1][1]]; }\n};\n\nvar XY = {\n  name: \"xy\",\n  handles: [\"n\", \"w\", \"e\", \"s\", \"nw\", \"ne\", \"sw\", \"se\"].map(type),\n  input: function(xy) { return xy == null ? null : number2(xy); },\n  output: function(xy) { return xy; }\n};\n\nvar cursors = {\n  overlay: \"crosshair\",\n  selection: \"move\",\n  n: \"ns-resize\",\n  e: \"ew-resize\",\n  s: \"ns-resize\",\n  w: \"ew-resize\",\n  nw: \"nwse-resize\",\n  ne: \"nesw-resize\",\n  se: \"nwse-resize\",\n  sw: \"nesw-resize\"\n};\n\nvar flipX = {\n  e: \"w\",\n  w: \"e\",\n  nw: \"ne\",\n  ne: \"nw\",\n  se: \"sw\",\n  sw: \"se\"\n};\n\nvar flipY = {\n  n: \"s\",\n  s: \"n\",\n  nw: \"sw\",\n  ne: \"se\",\n  se: \"ne\",\n  sw: \"nw\"\n};\n\nvar signsX = {\n  overlay: +1,\n  selection: +1,\n  n: null,\n  e: +1,\n  s: null,\n  w: -1,\n  nw: -1,\n  ne: +1,\n  se: +1,\n  sw: -1\n};\n\nvar signsY = {\n  overlay: +1,\n  selection: +1,\n  n: -1,\n  e: null,\n  s: +1,\n  w: null,\n  nw: -1,\n  ne: -1,\n  se: +1,\n  sw: +1\n};\n\nfunction type(t) {\n  return {type: t};\n}\n\n// Ignore right-click, since that should open the context menu.\nfunction defaultFilter(event) {\n  return !event.ctrlKey && !event.button;\n}\n\nfunction defaultExtent() {\n  var svg = this.ownerSVGElement || this;\n  if (svg.hasAttribute(\"viewBox\")) {\n    svg = svg.viewBox.baseVal;\n    return [[svg.x, svg.y], [svg.x + svg.width, svg.y + svg.height]];\n  }\n  return [[0, 0], [svg.width.baseVal.value, svg.height.baseVal.value]];\n}\n\nfunction defaultTouchable() {\n  return navigator.maxTouchPoints || (\"ontouchstart\" in this);\n}\n\n// Like d3.local, but with the name “__brush” rather than auto-generated.\nfunction local(node) {\n  while (!node.__brush) if (!(node = node.parentNode)) return;\n  return node.__brush;\n}\n\nfunction empty(extent) {\n  return extent[0][0] === extent[1][0]\n      || extent[0][1] === extent[1][1];\n}\n\nexport function brushSelection(node) {\n  var state = node.__brush;\n  return state ? state.dim.output(state.selection) : null;\n}\n\nexport function brushX() {\n  return brush(X);\n}\n\nexport function brushY() {\n  return brush(Y);\n}\n\nexport default function() {\n  return brush(XY);\n}\n\nfunction brush(dim) {\n  var extent = defaultExtent,\n      filter = defaultFilter,\n      touchable = defaultTouchable,\n      keys = true,\n      listeners = dispatch(\"start\", \"brush\", \"end\"),\n      handleSize = 6,\n      touchending;\n\n  function brush(group) {\n    var overlay = group\n        .property(\"__brush\", initialize)\n      .selectAll(\".overlay\")\n      .data([type(\"overlay\")]);\n\n    overlay.enter().append(\"rect\")\n        .attr(\"class\", \"overlay\")\n        .attr(\"pointer-events\", \"all\")\n        .attr(\"cursor\", cursors.overlay)\n      .merge(overlay)\n        .each(function() {\n          var extent = local(this).extent;\n          select(this)\n              .attr(\"x\", extent[0][0])\n              .attr(\"y\", extent[0][1])\n              .attr(\"width\", extent[1][0] - extent[0][0])\n              .attr(\"height\", extent[1][1] - extent[0][1]);\n        });\n\n    group.selectAll(\".selection\")\n      .data([type(\"selection\")])\n      .enter().append(\"rect\")\n        .attr(\"class\", \"selection\")\n        .attr(\"cursor\", cursors.selection)\n        .attr(\"fill\", \"#777\")\n        .attr(\"fill-opacity\", 0.3)\n        .attr(\"stroke\", \"#fff\")\n        .attr(\"shape-rendering\", \"crispEdges\");\n\n    var handle = group.selectAll(\".handle\")\n      .data(dim.handles, function(d) { return d.type; });\n\n    handle.exit().remove();\n\n    handle.enter().append(\"rect\")\n        .attr(\"class\", function(d) { return \"handle handle--\" + d.type; })\n        .attr(\"cursor\", function(d) { return cursors[d.type]; });\n\n    group\n        .each(redraw)\n        .attr(\"fill\", \"none\")\n        .attr(\"pointer-events\", \"all\")\n        .on(\"mousedown.brush\", started)\n      .filter(touchable)\n        .on(\"touchstart.brush\", started)\n        .on(\"touchmove.brush\", touchmoved)\n        .on(\"touchend.brush touchcancel.brush\", touchended)\n        .style(\"touch-action\", \"none\")\n        .style(\"-webkit-tap-highlight-color\", \"rgba(0,0,0,0)\");\n  }\n\n  brush.move = function(group, selection, event) {\n    if (group.tween) {\n      group\n          .on(\"start.brush\", function(event) { emitter(this, arguments).beforestart().start(event); })\n          .on(\"interrupt.brush end.brush\", function(event) { emitter(this, arguments).end(event); })\n          .tween(\"brush\", function() {\n            var that = this,\n                state = that.__brush,\n                emit = emitter(that, arguments),\n                selection0 = state.selection,\n                selection1 = dim.input(typeof selection === \"function\" ? selection.apply(this, arguments) : selection, state.extent),\n                i = interpolate(selection0, selection1);\n\n            function tween(t) {\n              state.selection = t === 1 && selection1 === null ? null : i(t);\n              redraw.call(that);\n              emit.brush();\n            }\n\n            return selection0 !== null && selection1 !== null ? tween : tween(1);\n          });\n    } else {\n      group\n          .each(function() {\n            var that = this,\n                args = arguments,\n                state = that.__brush,\n                selection1 = dim.input(typeof selection === \"function\" ? selection.apply(that, args) : selection, state.extent),\n                emit = emitter(that, args).beforestart();\n\n            interrupt(that);\n            state.selection = selection1 === null ? null : selection1;\n            redraw.call(that);\n            emit.start(event).brush(event).end(event);\n          });\n    }\n  };\n\n  brush.clear = function(group, event) {\n    brush.move(group, null, event);\n  };\n\n  function redraw() {\n    var group = select(this),\n        selection = local(this).selection;\n\n    if (selection) {\n      group.selectAll(\".selection\")\n          .style(\"display\", null)\n          .attr(\"x\", selection[0][0])\n          .attr(\"y\", selection[0][1])\n          .attr(\"width\", selection[1][0] - selection[0][0])\n          .attr(\"height\", selection[1][1] - selection[0][1]);\n\n      group.selectAll(\".handle\")\n          .style(\"display\", null)\n          .attr(\"x\", function(d) { return d.type[d.type.length - 1] === \"e\" ? selection[1][0] - handleSize / 2 : selection[0][0] - handleSize / 2; })\n          .attr(\"y\", function(d) { return d.type[0] === \"s\" ? selection[1][1] - handleSize / 2 : selection[0][1] - handleSize / 2; })\n          .attr(\"width\", function(d) { return d.type === \"n\" || d.type === \"s\" ? selection[1][0] - selection[0][0] + handleSize : handleSize; })\n          .attr(\"height\", function(d) { return d.type === \"e\" || d.type === \"w\" ? selection[1][1] - selection[0][1] + handleSize : handleSize; });\n    }\n\n    else {\n      group.selectAll(\".selection,.handle\")\n          .style(\"display\", \"none\")\n          .attr(\"x\", null)\n          .attr(\"y\", null)\n          .attr(\"width\", null)\n          .attr(\"height\", null);\n    }\n  }\n\n  function emitter(that, args, clean) {\n    var emit = that.__brush.emitter;\n    return emit && (!clean || !emit.clean) ? emit : new Emitter(that, args, clean);\n  }\n\n  function Emitter(that, args, clean) {\n    this.that = that;\n    this.args = args;\n    this.state = that.__brush;\n    this.active = 0;\n    this.clean = clean;\n  }\n\n  Emitter.prototype = {\n    beforestart: function() {\n      if (++this.active === 1) this.state.emitter = this, this.starting = true;\n      return this;\n    },\n    start: function(event, mode) {\n      if (this.starting) this.starting = false, this.emit(\"start\", event, mode);\n      else this.emit(\"brush\", event);\n      return this;\n    },\n    brush: function(event, mode) {\n      this.emit(\"brush\", event, mode);\n      return this;\n    },\n    end: function(event, mode) {\n      if (--this.active === 0) delete this.state.emitter, this.emit(\"end\", event, mode);\n      return this;\n    },\n    emit: function(type, event, mode) {\n      var d = select(this.that).datum();\n      listeners.call(\n        type,\n        this.that,\n        new BrushEvent(type, {\n          sourceEvent: event,\n          target: brush,\n          selection: dim.output(this.state.selection),\n          mode,\n          dispatch: listeners\n        }),\n        d\n      );\n    }\n  };\n\n  function started(event) {\n    if (touchending && !event.touches) return;\n    if (!filter.apply(this, arguments)) return;\n\n    var that = this,\n        type = event.target.__data__.type,\n        mode = (keys && event.metaKey ? type = \"overlay\" : type) === \"selection\" ? MODE_DRAG : (keys && event.altKey ? MODE_CENTER : MODE_HANDLE),\n        signX = dim === Y ? null : signsX[type],\n        signY = dim === X ? null : signsY[type],\n        state = local(that),\n        extent = state.extent,\n        selection = state.selection,\n        W = extent[0][0], w0, w1,\n        N = extent[0][1], n0, n1,\n        E = extent[1][0], e0, e1,\n        S = extent[1][1], s0, s1,\n        dx = 0,\n        dy = 0,\n        moving,\n        shifting = signX && signY && keys && event.shiftKey,\n        lockX,\n        lockY,\n        points = Array.from(event.touches || [event], t => {\n          const i = t.identifier;\n          t = pointer(t, that);\n          t.point0 = t.slice();\n          t.identifier = i;\n          return t;\n        });\n\n    interrupt(that);\n    var emit = emitter(that, arguments, true).beforestart();\n\n    if (type === \"overlay\") {\n      if (selection) moving = true;\n      const pts = [points[0], points[1] || points[0]];\n      state.selection = selection = [[\n          w0 = dim === Y ? W : min(pts[0][0], pts[1][0]),\n          n0 = dim === X ? N : min(pts[0][1], pts[1][1])\n        ], [\n          e0 = dim === Y ? E : max(pts[0][0], pts[1][0]),\n          s0 = dim === X ? S : max(pts[0][1], pts[1][1])\n        ]];\n      if (points.length > 1) move(event);\n    } else {\n      w0 = selection[0][0];\n      n0 = selection[0][1];\n      e0 = selection[1][0];\n      s0 = selection[1][1];\n    }\n\n    w1 = w0;\n    n1 = n0;\n    e1 = e0;\n    s1 = s0;\n\n    var group = select(that)\n        .attr(\"pointer-events\", \"none\");\n\n    var overlay = group.selectAll(\".overlay\")\n        .attr(\"cursor\", cursors[type]);\n\n    if (event.touches) {\n      emit.moved = moved;\n      emit.ended = ended;\n    } else {\n      var view = select(event.view)\n          .on(\"mousemove.brush\", moved, true)\n          .on(\"mouseup.brush\", ended, true);\n      if (keys) view\n          .on(\"keydown.brush\", keydowned, true)\n          .on(\"keyup.brush\", keyupped, true)\n\n      dragDisable(event.view);\n    }\n\n    redraw.call(that);\n    emit.start(event, mode.name);\n\n    function moved(event) {\n      for (const p of event.changedTouches || [event]) {\n        for (const d of points)\n          if (d.identifier === p.identifier) d.cur = pointer(p, that);\n      }\n      if (shifting && !lockX && !lockY && points.length === 1) {\n        const point = points[0];\n        if (abs(point.cur[0] - point[0]) > abs(point.cur[1] - point[1]))\n          lockY = true;\n        else\n          lockX = true;\n      }\n      for (const point of points)\n        if (point.cur) point[0] = point.cur[0], point[1] = point.cur[1];\n      moving = true;\n      noevent(event);\n      move(event);\n    }\n\n    function move(event) {\n      const point = points[0], point0 = point.point0;\n      var t;\n\n      dx = point[0] - point0[0];\n      dy = point[1] - point0[1];\n\n      switch (mode) {\n        case MODE_SPACE:\n        case MODE_DRAG: {\n          if (signX) dx = max(W - w0, min(E - e0, dx)), w1 = w0 + dx, e1 = e0 + dx;\n          if (signY) dy = max(N - n0, min(S - s0, dy)), n1 = n0 + dy, s1 = s0 + dy;\n          break;\n        }\n        case MODE_HANDLE: {\n          if (points[1]) {\n            if (signX) w1 = max(W, min(E, points[0][0])), e1 = max(W, min(E, points[1][0])), signX = 1;\n            if (signY) n1 = max(N, min(S, points[0][1])), s1 = max(N, min(S, points[1][1])), signY = 1;\n          } else {\n            if (signX < 0) dx = max(W - w0, min(E - w0, dx)), w1 = w0 + dx, e1 = e0;\n            else if (signX > 0) dx = max(W - e0, min(E - e0, dx)), w1 = w0, e1 = e0 + dx;\n            if (signY < 0) dy = max(N - n0, min(S - n0, dy)), n1 = n0 + dy, s1 = s0;\n            else if (signY > 0) dy = max(N - s0, min(S - s0, dy)), n1 = n0, s1 = s0 + dy;\n          }\n          break;\n        }\n        case MODE_CENTER: {\n          if (signX) w1 = max(W, min(E, w0 - dx * signX)), e1 = max(W, min(E, e0 + dx * signX));\n          if (signY) n1 = max(N, min(S, n0 - dy * signY)), s1 = max(N, min(S, s0 + dy * signY));\n          break;\n        }\n      }\n\n      if (e1 < w1) {\n        signX *= -1;\n        t = w0, w0 = e0, e0 = t;\n        t = w1, w1 = e1, e1 = t;\n        if (type in flipX) overlay.attr(\"cursor\", cursors[type = flipX[type]]);\n      }\n\n      if (s1 < n1) {\n        signY *= -1;\n        t = n0, n0 = s0, s0 = t;\n        t = n1, n1 = s1, s1 = t;\n        if (type in flipY) overlay.attr(\"cursor\", cursors[type = flipY[type]]);\n      }\n\n      if (state.selection) selection = state.selection; // May be set by brush.move!\n      if (lockX) w1 = selection[0][0], e1 = selection[1][0];\n      if (lockY) n1 = selection[0][1], s1 = selection[1][1];\n\n      if (selection[0][0] !== w1\n          || selection[0][1] !== n1\n          || selection[1][0] !== e1\n          || selection[1][1] !== s1) {\n        state.selection = [[w1, n1], [e1, s1]];\n        redraw.call(that);\n        emit.brush(event, mode.name);\n      }\n    }\n\n    function ended(event) {\n      nopropagation(event);\n      if (event.touches) {\n        if (event.touches.length) return;\n        if (touchending) clearTimeout(touchending);\n        touchending = setTimeout(function() { touchending = null; }, 500); // Ghost clicks are delayed!\n      } else {\n        dragEnable(event.view, moving);\n        view.on(\"keydown.brush keyup.brush mousemove.brush mouseup.brush\", null);\n      }\n      group.attr(\"pointer-events\", \"all\");\n      overlay.attr(\"cursor\", cursors.overlay);\n      if (state.selection) selection = state.selection; // May be set by brush.move (on start)!\n      if (empty(selection)) state.selection = null, redraw.call(that);\n      emit.end(event, mode.name);\n    }\n\n    function keydowned(event) {\n      switch (event.keyCode) {\n        case 16: { // SHIFT\n          shifting = signX && signY;\n          break;\n        }\n        case 18: { // ALT\n          if (mode === MODE_HANDLE) {\n            if (signX) e0 = e1 - dx * signX, w0 = w1 + dx * signX;\n            if (signY) s0 = s1 - dy * signY, n0 = n1 + dy * signY;\n            mode = MODE_CENTER;\n            move(event);\n          }\n          break;\n        }\n        case 32: { // SPACE; takes priority over ALT\n          if (mode === MODE_HANDLE || mode === MODE_CENTER) {\n            if (signX < 0) e0 = e1 - dx; else if (signX > 0) w0 = w1 - dx;\n            if (signY < 0) s0 = s1 - dy; else if (signY > 0) n0 = n1 - dy;\n            mode = MODE_SPACE;\n            overlay.attr(\"cursor\", cursors.selection);\n            move(event);\n          }\n          break;\n        }\n        default: return;\n      }\n      noevent(event);\n    }\n\n    function keyupped(event) {\n      switch (event.keyCode) {\n        case 16: { // SHIFT\n          if (shifting) {\n            lockX = lockY = shifting = false;\n            move(event);\n          }\n          break;\n        }\n        case 18: { // ALT\n          if (mode === MODE_CENTER) {\n            if (signX < 0) e0 = e1; else if (signX > 0) w0 = w1;\n            if (signY < 0) s0 = s1; else if (signY > 0) n0 = n1;\n            mode = MODE_HANDLE;\n            move(event);\n          }\n          break;\n        }\n        case 32: { // SPACE\n          if (mode === MODE_SPACE) {\n            if (event.altKey) {\n              if (signX) e0 = e1 - dx * signX, w0 = w1 + dx * signX;\n              if (signY) s0 = s1 - dy * signY, n0 = n1 + dy * signY;\n              mode = MODE_CENTER;\n            } else {\n              if (signX < 0) e0 = e1; else if (signX > 0) w0 = w1;\n              if (signY < 0) s0 = s1; else if (signY > 0) n0 = n1;\n              mode = MODE_HANDLE;\n            }\n            overlay.attr(\"cursor\", cursors[type]);\n            move(event);\n          }\n          break;\n        }\n        default: return;\n      }\n      noevent(event);\n    }\n  }\n\n  function touchmoved(event) {\n    emitter(this, arguments).moved(event);\n  }\n\n  function touchended(event) {\n    emitter(this, arguments).ended(event);\n  }\n\n  function initialize() {\n    var state = this.__brush || {selection: null};\n    state.extent = number2(extent.apply(this, arguments));\n    state.dim = dim;\n    return state;\n  }\n\n  brush.extent = function(_) {\n    return arguments.length ? (extent = typeof _ === \"function\" ? _ : constant(number2(_)), brush) : extent;\n  };\n\n  brush.filter = function(_) {\n    return arguments.length ? (filter = typeof _ === \"function\" ? _ : constant(!!_), brush) : filter;\n  };\n\n  brush.touchable = function(_) {\n    return arguments.length ? (touchable = typeof _ === \"function\" ? _ : constant(!!_), brush) : touchable;\n  };\n\n  brush.handleSize = function(_) {\n    return arguments.length ? (handleSize = +_, brush) : handleSize;\n  };\n\n  brush.keyModifiers = function(_) {\n    return arguments.length ? (keys = !!_, brush) : keys;\n  };\n\n  brush.on = function() {\n    var value = listeners.on.apply(listeners, arguments);\n    return value === listeners ? brush : value;\n  };\n\n  return brush;\n}\n", "export {\n  default as brush,\n  brushX,\n  brushY,\n  brushSelection\n} from \"./brush.js\";\n", "import {range as sequence} from \"d3-array\";\nimport {initRange} from \"./init.js\";\nimport ordinal from \"./ordinal.js\";\n\nexport default function band() {\n  var scale = ordinal().unknown(undefined),\n      domain = scale.domain,\n      ordinalRange = scale.range,\n      r0 = 0,\n      r1 = 1,\n      step,\n      bandwidth,\n      round = false,\n      paddingInner = 0,\n      paddingOuter = 0,\n      align = 0.5;\n\n  delete scale.unknown;\n\n  function rescale() {\n    var n = domain().length,\n        reverse = r1 < r0,\n        start = reverse ? r1 : r0,\n        stop = reverse ? r0 : r1;\n    step = (stop - start) / Math.max(1, n - paddingInner + paddingOuter * 2);\n    if (round) step = Math.floor(step);\n    start += (stop - start - step * (n - paddingInner)) * align;\n    bandwidth = step * (1 - paddingInner);\n    if (round) start = Math.round(start), bandwidth = Math.round(bandwidth);\n    var values = sequence(n).map(function(i) { return start + step * i; });\n    return ordinalRange(reverse ? values.reverse() : values);\n  }\n\n  scale.domain = function(_) {\n    return arguments.length ? (domain(_), rescale()) : domain();\n  };\n\n  scale.range = function(_) {\n    return arguments.length ? ([r0, r1] = _, r0 = +r0, r1 = +r1, rescale()) : [r0, r1];\n  };\n\n  scale.rangeRound = function(_) {\n    return [r0, r1] = _, r0 = +r0, r1 = +r1, round = true, rescale();\n  };\n\n  scale.bandwidth = function() {\n    return bandwidth;\n  };\n\n  scale.step = function() {\n    return step;\n  };\n\n  scale.round = function(_) {\n    return arguments.length ? (round = !!_, rescale()) : round;\n  };\n\n  scale.padding = function(_) {\n    return arguments.length ? (paddingInner = Math.min(1, paddingOuter = +_), rescale()) : paddingInner;\n  };\n\n  scale.paddingInner = function(_) {\n    return arguments.length ? (paddingInner = Math.min(1, _), rescale()) : paddingInner;\n  };\n\n  scale.paddingOuter = function(_) {\n    return arguments.length ? (paddingOuter = +_, rescale()) : paddingOuter;\n  };\n\n  scale.align = function(_) {\n    return arguments.length ? (align = Math.max(0, Math.min(1, _)), rescale()) : align;\n  };\n\n  scale.copy = function() {\n    return band(domain(), [r0, r1])\n        .round(round)\n        .paddingInner(paddingInner)\n        .paddingOuter(paddingOuter)\n        .align(align);\n  };\n\n  return initRange.apply(rescale(), arguments);\n}\n\nfunction pointish(scale) {\n  var copy = scale.copy;\n\n  scale.padding = scale.paddingOuter;\n  delete scale.paddingInner;\n  delete scale.paddingOuter;\n\n  scale.copy = function() {\n    return pointish(copy());\n  };\n\n  return scale;\n}\n\nexport function point() {\n  return pointish(band.apply(null, arguments).paddingInner(1));\n}\n", "export {\n  default as scaleBand,\n  point as scalePoint\n} from \"./band.js\";\n\nexport {\n  default as scaleIdentity\n} from \"./identity.js\";\n\nexport {\n  default as scaleLinear\n} from \"./linear.js\";\n\nexport {\n  default as scaleLog\n} from \"./log.js\";\n\nexport {\n  default as scaleSymlog\n} from \"./symlog.js\";\n\nexport {\n  default as scaleOrdinal,\n  implicit as scaleImplicit\n} from \"./ordinal.js\";\n\nexport {\n  default as scalePow,\n  sqrt as scaleSqrt\n} from \"./pow.js\";\n\nexport {\n  default as scaleRadial\n} from \"./radial.js\";\n\nexport {\n  default as scaleQuantile\n} from \"./quantile.js\";\n\nexport {\n  default as scaleQuantize\n} from \"./quantize.js\";\n\nexport {\n  default as scaleThreshold\n} from \"./threshold.js\";\n\nexport {\n  default as scaleTime\n} from \"./time.js\";\n\nexport {\n  default as scaleUtc\n} from \"./utcTime.js\";\n\nexport {\n  default as scaleSequential,\n  sequentialLog as scaleSequentialLog,\n  sequentialPow as scaleSequentialPow,\n  sequentialSqrt as scaleSequentialSqrt,\n  sequentialSymlog as scaleSequentialSymlog\n} from \"./sequential.js\";\n\nexport {\n  default as scaleSequentialQuantile\n} from \"./sequentialQuantile.js\";\n\nexport {\n  default as scaleDiverging,\n  divergingLog as scaleDivergingLog,\n  divergingPow as scaleDivergingPow,\n  divergingSqrt as scaleDivergingSqrt,\n  divergingSymlog as scaleDivergingSymlog\n} from \"./diverging.js\";\n\nexport {\n  default as tickFormat\n} from \"./tickFormat.js\";\n", "import colors from \"../colors.js\";\n\nexport default colors(\"4e79a7f28e2ce1575976b7b259a14fedc949af7aa1ff9da79c755fbab0ab\");\n", "export {default as schemeCategory10} from \"./categorical/category10.js\";\nexport {default as schemeAccent} from \"./categorical/Accent.js\";\nexport {default as schemeDark2} from \"./categorical/Dark2.js\";\nexport {default as schemeObservable10} from \"./categorical/observable10.js\";\nexport {default as schemePaired} from \"./categorical/Paired.js\";\nexport {default as schemePastel1} from \"./categorical/Pastel1.js\";\nexport {default as schemePastel2} from \"./categorical/Pastel2.js\";\nexport {default as schemeSet1} from \"./categorical/Set1.js\";\nexport {default as schemeSet2} from \"./categorical/Set2.js\";\nexport {default as schemeSet3} from \"./categorical/Set3.js\";\nexport {default as schemeTableau10} from \"./categorical/Tableau10.js\";\nexport {default as interpolateBrBG, scheme as schemeBrBG} from \"./diverging/BrBG.js\";\nexport {default as interpolatePRGn, scheme as schemePRGn} from \"./diverging/PRGn.js\";\nexport {default as interpolatePiYG, scheme as schemePiYG} from \"./diverging/PiYG.js\";\nexport {default as interpolatePuOr, scheme as schemePuOr} from \"./diverging/PuOr.js\";\nexport {default as interpolateRdBu, scheme as schemeRdBu} from \"./diverging/RdBu.js\";\nexport {default as interpolateRdGy, scheme as schemeRdGy} from \"./diverging/RdGy.js\";\nexport {default as interpolateRdYlBu, scheme as schemeRdYlBu} from \"./diverging/RdYlBu.js\";\nexport {default as interpolateRdYlGn, scheme as schemeRdYlGn} from \"./diverging/RdYlGn.js\";\nexport {default as interpolateSpectral, scheme as schemeSpectral} from \"./diverging/Spectral.js\";\nexport {default as interpolateBuGn, scheme as schemeBuGn} from \"./sequential-multi/BuGn.js\";\nexport {default as interpolateBuPu, scheme as schemeBuPu} from \"./sequential-multi/BuPu.js\";\nexport {default as interpolateGnBu, scheme as schemeGnBu} from \"./sequential-multi/GnBu.js\";\nexport {default as interpolateOrRd, scheme as schemeOrRd} from \"./sequential-multi/OrRd.js\";\nexport {default as interpolatePuBuGn, scheme as schemePuBuGn} from \"./sequential-multi/PuBuGn.js\";\nexport {default as interpolatePuBu, scheme as schemePuBu} from \"./sequential-multi/PuBu.js\";\nexport {default as interpolatePuRd, scheme as schemePuRd} from \"./sequential-multi/PuRd.js\";\nexport {default as interpolateRdPu, scheme as schemeRdPu} from \"./sequential-multi/RdPu.js\";\nexport {default as interpolateYlGnBu, scheme as schemeYlGnBu} from \"./sequential-multi/YlGnBu.js\";\nexport {default as interpolateYlGn, scheme as schemeYlGn} from \"./sequential-multi/YlGn.js\";\nexport {default as interpolateYlOrBr, scheme as schemeYlOrBr} from \"./sequential-multi/YlOrBr.js\";\nexport {default as interpolateYlOrRd, scheme as schemeYlOrRd} from \"./sequential-multi/YlOrRd.js\";\nexport {default as interpolateBlues, scheme as schemeBlues} from \"./sequential-single/Blues.js\";\nexport {default as interpolateGreens, scheme as schemeGreens} from \"./sequential-single/Greens.js\";\nexport {default as interpolateGreys, scheme as schemeGreys} from \"./sequential-single/Greys.js\";\nexport {default as interpolatePurples, scheme as schemePurples} from \"./sequential-single/Purples.js\";\nexport {default as interpolateReds, scheme as schemeReds} from \"./sequential-single/Reds.js\";\nexport {default as interpolateOranges, scheme as schemeOranges} from \"./sequential-single/Oranges.js\";\nexport {default as interpolateCividis} from \"./sequential-multi/cividis.js\";\nexport {default as interpolateCubehelixDefault} from \"./sequential-multi/cubehelix.js\";\nexport {default as interpolateRainbow, warm as interpolateWarm, cool as interpolateCool} from \"./sequential-multi/rainbow.js\";\nexport {default as interpolateSinebow} from \"./sequential-multi/sinebow.js\";\nexport {default as interpolateTurbo} from \"./sequential-multi/turbo.js\";\nexport {default as interpolateViridis, magma as interpolateMagma, inferno as interpolateInferno, plasma as interpolatePlasma} from \"./sequential-multi/viridis.js\";\n", "import {Selection, root} from \"./selection/index.js\";\n\nexport default function(selector) {\n  return typeof selector === \"string\"\n      ? new Selection([[document.querySelector(selector)]], [document.documentElement])\n      : new Selection([[selector]], root);\n}\n", "export {default as create} from \"./create.js\";\nexport {default as creator} from \"./creator.js\";\nexport {default as local} from \"./local.js\";\nexport {default as matcher} from \"./matcher.js\";\nexport {default as namespace} from \"./namespace.js\";\nexport {default as namespaces} from \"./namespaces.js\";\nexport {default as pointer} from \"./pointer.js\";\nexport {default as pointers} from \"./pointers.js\";\nexport {default as select} from \"./select.js\";\nexport {default as selectAll} from \"./selectAll.js\";\nexport {default as selection} from \"./selection/index.js\";\nexport {default as selector} from \"./selector.js\";\nexport {default as selectorAll} from \"./selectorAll.js\";\nexport {styleValue as style} from \"./selection/style.js\";\nexport {default as window} from \"./window.js\";\n", "export default function(a, b) {\n  return b < a ? -1 : b > a ? 1 : b >= a ? 0 : NaN;\n}\n", "export default function(d) {\n  return d;\n}\n", "import array from \"./array.js\";\nimport constant from \"./constant.js\";\nimport descending from \"./descending.js\";\nimport identity from \"./identity.js\";\nimport {tau} from \"./math.js\";\n\nexport default function() {\n  var value = identity,\n      sortValues = descending,\n      sort = null,\n      startAngle = constant(0),\n      endAngle = constant(tau),\n      padAngle = constant(0);\n\n  function pie(data) {\n    var i,\n        n = (data = array(data)).length,\n        j,\n        k,\n        sum = 0,\n        index = new Array(n),\n        arcs = new Array(n),\n        a0 = +startAngle.apply(this, arguments),\n        da = Math.min(tau, Math.max(-tau, endAngle.apply(this, arguments) - a0)),\n        a1,\n        p = Math.min(Math.abs(da) / n, padAngle.apply(this, arguments)),\n        pa = p * (da < 0 ? -1 : 1),\n        v;\n\n    for (i = 0; i < n; ++i) {\n      if ((v = arcs[index[i] = i] = +value(data[i], i, data)) > 0) {\n        sum += v;\n      }\n    }\n\n    // Optionally sort the arcs by previously-computed values or by data.\n    if (sortValues != null) index.sort(function(i, j) { return sortValues(arcs[i], arcs[j]); });\n    else if (sort != null) index.sort(function(i, j) { return sort(data[i], data[j]); });\n\n    // Compute the arcs! They are stored in the original data's order.\n    for (i = 0, k = sum ? (da - n * pa) / sum : 0; i < n; ++i, a0 = a1) {\n      j = index[i], v = arcs[j], a1 = a0 + (v > 0 ? v * k : 0) + pa, arcs[j] = {\n        data: data[j],\n        index: i,\n        value: v,\n        startAngle: a0,\n        endAngle: a1,\n        padAngle: p\n      };\n    }\n\n    return arcs;\n  }\n\n  pie.value = function(_) {\n    return arguments.length ? (value = typeof _ === \"function\" ? _ : constant(+_), pie) : value;\n  };\n\n  pie.sortValues = function(_) {\n    return arguments.length ? (sortValues = _, sort = null, pie) : sortValues;\n  };\n\n  pie.sort = function(_) {\n    return arguments.length ? (sort = _, sortValues = null, pie) : sort;\n  };\n\n  pie.startAngle = function(_) {\n    return arguments.length ? (startAngle = typeof _ === \"function\" ? _ : constant(+_), pie) : startAngle;\n  };\n\n  pie.endAngle = function(_) {\n    return arguments.length ? (endAngle = typeof _ === \"function\" ? _ : constant(+_), pie) : endAngle;\n  };\n\n  pie.padAngle = function(_) {\n    return arguments.length ? (padAngle = typeof _ === \"function\" ? _ : constant(+_), pie) : padAngle;\n  };\n\n  return pie;\n}\n", "import pointRadial from \"../pointRadial.js\";\n\nclass Bump {\n  constructor(context, x) {\n    this._context = context;\n    this._x = x;\n  }\n  areaStart() {\n    this._line = 0;\n  }\n  areaEnd() {\n    this._line = NaN;\n  }\n  lineStart() {\n    this._point = 0;\n  }\n  lineEnd() {\n    if (this._line || (this._line !== 0 && this._point === 1)) this._context.closePath();\n    this._line = 1 - this._line;\n  }\n  point(x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0: {\n        this._point = 1;\n        if (this._line) this._context.lineTo(x, y);\n        else this._context.moveTo(x, y);\n        break;\n      }\n      case 1: this._point = 2; // falls through\n      default: {\n        if (this._x) this._context.bezierCurveTo(this._x0 = (this._x0 + x) / 2, this._y0, this._x0, y, x, y);\n        else this._context.bezierCurveTo(this._x0, this._y0 = (this._y0 + y) / 2, x, this._y0, x, y);\n        break;\n      }\n    }\n    this._x0 = x, this._y0 = y;\n  }\n}\n\nclass BumpRadial {\n  constructor(context) {\n    this._context = context;\n  }\n  lineStart() {\n    this._point = 0;\n  }\n  lineEnd() {}\n  point(x, y) {\n    x = +x, y = +y;\n    if (this._point === 0) {\n      this._point = 1;\n    } else {\n      const p0 = pointRadial(this._x0, this._y0);\n      const p1 = pointRadial(this._x0, this._y0 = (this._y0 + y) / 2);\n      const p2 = pointRadial(x, this._y0);\n      const p3 = pointRadial(x, y);\n      this._context.moveTo(...p0);\n      this._context.bezierCurveTo(...p1, ...p2, ...p3);\n    }\n    this._x0 = x, this._y0 = y;\n  }\n}\n\nexport function bumpX(context) {\n  return new Bump(context, true);\n}\n\nexport function bumpY(context) {\n  return new Bump(context, false);\n}\n\nexport function bumpRadial(context) {\n  return new BumpRadial(context);\n}\n", "export {default as arc} from \"./arc.js\";\nexport {default as area} from \"./area.js\";\nexport {default as line} from \"./line.js\";\nexport {default as pie} from \"./pie.js\";\nexport {default as areaRadial, default as radialArea} from \"./areaRadial.js\"; // Note: radialArea is deprecated!\nexport {default as lineRadial, default as radialLine} from \"./lineRadial.js\"; // Note: radialLine is deprecated!\nexport {default as pointRadial} from \"./pointRadial.js\";\nexport {link, linkHorizontal, linkVertical, linkRadial} from \"./link.js\";\n\nexport {default as symbol, symbolsStroke, symbolsFill, symbolsFill as symbols} from \"./symbol.js\";\nexport {default as symbolAsterisk} from \"./symbol/asterisk.js\";\nexport {default as symbolCircle} from \"./symbol/circle.js\";\nexport {default as symbolCross} from \"./symbol/cross.js\";\nexport {default as symbolDiamond} from \"./symbol/diamond.js\";\nexport {default as symbolDiamond2} from \"./symbol/diamond2.js\";\nexport {default as symbolPlus} from \"./symbol/plus.js\";\nexport {default as symbolSquare} from \"./symbol/square.js\";\nexport {default as symbolSquare2} from \"./symbol/square2.js\";\nexport {default as symbolStar} from \"./symbol/star.js\";\nexport {default as symbolTriangle} from \"./symbol/triangle.js\";\nexport {default as symbolTriangle2} from \"./symbol/triangle2.js\";\nexport {default as symbolWye} from \"./symbol/wye.js\";\nexport {default as symbolTimes, default as symbolX} from \"./symbol/times.js\";\n\nexport {default as curveBasisClosed} from \"./curve/basisClosed.js\";\nexport {default as curveBasisOpen} from \"./curve/basisOpen.js\";\nexport {default as curveBasis} from \"./curve/basis.js\";\nexport {bumpX as curveBumpX, bumpY as curveBumpY} from \"./curve/bump.js\";\nexport {default as curveBundle} from \"./curve/bundle.js\";\nexport {default as curveCardinalClosed} from \"./curve/cardinalClosed.js\";\nexport {default as curveCardinalOpen} from \"./curve/cardinalOpen.js\";\nexport {default as curveCardinal} from \"./curve/cardinal.js\";\nexport {default as curveCatmullRomClosed} from \"./curve/catmullRomClosed.js\";\nexport {default as curveCatmullRomOpen} from \"./curve/catmullRomOpen.js\";\nexport {default as curveCatmullRom} from \"./curve/catmullRom.js\";\nexport {default as curveLinearClosed} from \"./curve/linearClosed.js\";\nexport {default as curveLinear} from \"./curve/linear.js\";\nexport {monotoneX as curveMonotoneX, monotoneY as curveMonotoneY} from \"./curve/monotone.js\";\nexport {default as curveNatural} from \"./curve/natural.js\";\nexport {default as curveStep, stepAfter as curveStepAfter, stepBefore as curveStepBefore} from \"./curve/step.js\";\n\nexport {default as stack} from \"./stack.js\";\nexport {default as stackOffsetExpand} from \"./offset/expand.js\";\nexport {default as stackOffsetDiverging} from \"./offset/diverging.js\";\nexport {default as stackOffsetNone} from \"./offset/none.js\";\nexport {default as stackOffsetSilhouette} from \"./offset/silhouette.js\";\nexport {default as stackOffsetWiggle} from \"./offset/wiggle.js\";\nexport {default as stackOrderAppearance} from \"./order/appearance.js\";\nexport {default as stackOrderAscending} from \"./order/ascending.js\";\nexport {default as stackOrderDescending} from \"./order/descending.js\";\nexport {default as stackOrderInsideOut} from \"./order/insideOut.js\";\nexport {default as stackOrderNone} from \"./order/none.js\";\nexport {default as stackOrderReverse} from \"./order/reverse.js\";\n", "export {\n  timeInterval\n} from \"./interval.js\";\n\nexport {\n  millisecond as utcMillisecond,\n  milliseconds as utcMilliseconds,\n  millisecond as timeMillisecond,\n  milliseconds as timeMilliseconds\n} from \"./millisecond.js\";\n\nexport {\n  second as utcSecond,\n  seconds as utcSeconds,\n  second as timeSecond,\n  seconds as timeSeconds\n} from \"./second.js\";\n\nexport {\n  timeMinute,\n  timeMinutes,\n  utcMinute,\n  utcMinutes\n} from \"./minute.js\";\n\nexport {\n  timeHour,\n  timeHours,\n  utcHour,\n  utcHours\n} from \"./hour.js\";\n\nexport {\n  timeDay,\n  timeDays,\n  utcDay,\n  utcDays,\n  unixDay,\n  unixDays\n} from \"./day.js\";\n\nexport {\n  timeSunday as timeWeek,\n  timeSundays as timeWeeks,\n  timeSunday,\n  timeSundays,\n  timeMonday,\n  timeMondays,\n  timeTuesday,\n  timeTuesdays,\n  timeWednesday,\n  timeWednesdays,\n  timeThursday,\n  timeThursdays,\n  timeFriday,\n  timeFridays,\n  timeSaturday,\n  timeSaturdays,\n  utcSunday as utcWeek,\n  utcSundays as utcWeeks,\n  utcSunday,\n  utcSundays,\n  utcMonday,\n  utcMondays,\n  utcTuesday,\n  utcTuesdays,\n  utcWednesday,\n  utcWednesdays,\n  utcThursday,\n  utcThursdays,\n  utcFriday,\n  utcFridays,\n  utcSaturday,\n  utcSaturdays\n} from \"./week.js\";\n\nexport {\n  timeMonth,\n  timeMonths,\n  utcMonth,\n  utcMonths\n} from \"./month.js\";\n\nexport {\n  timeYear,\n  timeYears,\n  utcYear,\n  utcYears\n} from \"./year.js\";\n\nexport {\n  utcTicks,\n  utcTickInterval,\n  timeTicks,\n  timeTickInterval\n} from \"./ticks.js\";\n", "export {default as timeFormatDefaultLocale, timeFormat, timeParse, utcFormat, utcParse} from \"./defaultLocale.js\";\nexport {default as timeFormatLocale} from \"./locale.js\";\nexport {default as isoFormat} from \"./isoFormat.js\";\nexport {default as isoParse} from \"./isoParse.js\";\n", "export function Transform(k, x, y) {\n  this.k = k;\n  this.x = x;\n  this.y = y;\n}\n\nTransform.prototype = {\n  constructor: Transform,\n  scale: function(k) {\n    return k === 1 ? this : new Transform(this.k * k, this.x, this.y);\n  },\n  translate: function(x, y) {\n    return x === 0 & y === 0 ? this : new Transform(this.k, this.x + this.k * x, this.y + this.k * y);\n  },\n  apply: function(point) {\n    return [point[0] * this.k + this.x, point[1] * this.k + this.y];\n  },\n  applyX: function(x) {\n    return x * this.k + this.x;\n  },\n  applyY: function(y) {\n    return y * this.k + this.y;\n  },\n  invert: function(location) {\n    return [(location[0] - this.x) / this.k, (location[1] - this.y) / this.k];\n  },\n  invertX: function(x) {\n    return (x - this.x) / this.k;\n  },\n  invertY: function(y) {\n    return (y - this.y) / this.k;\n  },\n  rescaleX: function(x) {\n    return x.copy().domain(x.range().map(this.invertX, this).map(x.invert, x));\n  },\n  rescaleY: function(y) {\n    return y.copy().domain(y.range().map(this.invertY, this).map(y.invert, y));\n  },\n  toString: function() {\n    return \"translate(\" + this.x + \",\" + this.y + \") scale(\" + this.k + \")\";\n  }\n};\n\nexport var identity = new Transform(1, 0, 0);\n\ntransform.prototype = Transform.prototype;\n\nexport default function transform(node) {\n  while (!node.__zoom) if (!(node = node.parentNode)) return identity;\n  return node.__zoom;\n}\n", "import {dispatch} from \"d3-dispatch\";\nimport {dragDisable, dragEnable} from \"d3-drag\";\nimport {interpolateZoom} from \"d3-interpolate\";\nimport {select, pointer} from \"d3-selection\";\nimport {interrupt} from \"d3-transition\";\nimport constant from \"./constant.js\";\nimport ZoomEvent from \"./event.js\";\nimport {Transform, identity} from \"./transform.js\";\nimport noevent, {nopropagation} from \"./noevent.js\";\n\n// Ignore right-click, since that should open the context menu.\n// except for pinch-to-zoom, which is sent as a wheel+ctrlKey event\nfunction defaultFilter(event) {\n  return (!event.ctrlKey || event.type === 'wheel') && !event.button;\n}\n\nfunction defaultExtent() {\n  var e = this;\n  if (e instanceof SVGElement) {\n    e = e.ownerSVGElement || e;\n    if (e.hasAttribute(\"viewBox\")) {\n      e = e.viewBox.baseVal;\n      return [[e.x, e.y], [e.x + e.width, e.y + e.height]];\n    }\n    return [[0, 0], [e.width.baseVal.value, e.height.baseVal.value]];\n  }\n  return [[0, 0], [e.clientWidth, e.clientHeight]];\n}\n\nfunction defaultTransform() {\n  return this.__zoom || identity;\n}\n\nfunction defaultWheelDelta(event) {\n  return -event.deltaY * (event.deltaMode === 1 ? 0.05 : event.deltaMode ? 1 : 0.002) * (event.ctrlKey ? 10 : 1);\n}\n\nfunction defaultTouchable() {\n  return navigator.maxTouchPoints || (\"ontouchstart\" in this);\n}\n\nfunction defaultConstrain(transform, extent, translateExtent) {\n  var dx0 = transform.invertX(extent[0][0]) - translateExtent[0][0],\n      dx1 = transform.invertX(extent[1][0]) - translateExtent[1][0],\n      dy0 = transform.invertY(extent[0][1]) - translateExtent[0][1],\n      dy1 = transform.invertY(extent[1][1]) - translateExtent[1][1];\n  return transform.translate(\n    dx1 > dx0 ? (dx0 + dx1) / 2 : Math.min(0, dx0) || Math.max(0, dx1),\n    dy1 > dy0 ? (dy0 + dy1) / 2 : Math.min(0, dy0) || Math.max(0, dy1)\n  );\n}\n\nexport default function() {\n  var filter = defaultFilter,\n      extent = defaultExtent,\n      constrain = defaultConstrain,\n      wheelDelta = defaultWheelDelta,\n      touchable = defaultTouchable,\n      scaleExtent = [0, Infinity],\n      translateExtent = [[-Infinity, -Infinity], [Infinity, Infinity]],\n      duration = 250,\n      interpolate = interpolateZoom,\n      listeners = dispatch(\"start\", \"zoom\", \"end\"),\n      touchstarting,\n      touchfirst,\n      touchending,\n      touchDelay = 500,\n      wheelDelay = 150,\n      clickDistance2 = 0,\n      tapDistance = 10;\n\n  function zoom(selection) {\n    selection\n        .property(\"__zoom\", defaultTransform)\n        .on(\"wheel.zoom\", wheeled, {passive: false})\n        .on(\"mousedown.zoom\", mousedowned)\n        .on(\"dblclick.zoom\", dblclicked)\n      .filter(touchable)\n        .on(\"touchstart.zoom\", touchstarted)\n        .on(\"touchmove.zoom\", touchmoved)\n        .on(\"touchend.zoom touchcancel.zoom\", touchended)\n        .style(\"-webkit-tap-highlight-color\", \"rgba(0,0,0,0)\");\n  }\n\n  zoom.transform = function(collection, transform, point, event) {\n    var selection = collection.selection ? collection.selection() : collection;\n    selection.property(\"__zoom\", defaultTransform);\n    if (collection !== selection) {\n      schedule(collection, transform, point, event);\n    } else {\n      selection.interrupt().each(function() {\n        gesture(this, arguments)\n          .event(event)\n          .start()\n          .zoom(null, typeof transform === \"function\" ? transform.apply(this, arguments) : transform)\n          .end();\n      });\n    }\n  };\n\n  zoom.scaleBy = function(selection, k, p, event) {\n    zoom.scaleTo(selection, function() {\n      var k0 = this.__zoom.k,\n          k1 = typeof k === \"function\" ? k.apply(this, arguments) : k;\n      return k0 * k1;\n    }, p, event);\n  };\n\n  zoom.scaleTo = function(selection, k, p, event) {\n    zoom.transform(selection, function() {\n      var e = extent.apply(this, arguments),\n          t0 = this.__zoom,\n          p0 = p == null ? centroid(e) : typeof p === \"function\" ? p.apply(this, arguments) : p,\n          p1 = t0.invert(p0),\n          k1 = typeof k === \"function\" ? k.apply(this, arguments) : k;\n      return constrain(translate(scale(t0, k1), p0, p1), e, translateExtent);\n    }, p, event);\n  };\n\n  zoom.translateBy = function(selection, x, y, event) {\n    zoom.transform(selection, function() {\n      return constrain(this.__zoom.translate(\n        typeof x === \"function\" ? x.apply(this, arguments) : x,\n        typeof y === \"function\" ? y.apply(this, arguments) : y\n      ), extent.apply(this, arguments), translateExtent);\n    }, null, event);\n  };\n\n  zoom.translateTo = function(selection, x, y, p, event) {\n    zoom.transform(selection, function() {\n      var e = extent.apply(this, arguments),\n          t = this.__zoom,\n          p0 = p == null ? centroid(e) : typeof p === \"function\" ? p.apply(this, arguments) : p;\n      return constrain(identity.translate(p0[0], p0[1]).scale(t.k).translate(\n        typeof x === \"function\" ? -x.apply(this, arguments) : -x,\n        typeof y === \"function\" ? -y.apply(this, arguments) : -y\n      ), e, translateExtent);\n    }, p, event);\n  };\n\n  function scale(transform, k) {\n    k = Math.max(scaleExtent[0], Math.min(scaleExtent[1], k));\n    return k === transform.k ? transform : new Transform(k, transform.x, transform.y);\n  }\n\n  function translate(transform, p0, p1) {\n    var x = p0[0] - p1[0] * transform.k, y = p0[1] - p1[1] * transform.k;\n    return x === transform.x && y === transform.y ? transform : new Transform(transform.k, x, y);\n  }\n\n  function centroid(extent) {\n    return [(+extent[0][0] + +extent[1][0]) / 2, (+extent[0][1] + +extent[1][1]) / 2];\n  }\n\n  function schedule(transition, transform, point, event) {\n    transition\n        .on(\"start.zoom\", function() { gesture(this, arguments).event(event).start(); })\n        .on(\"interrupt.zoom end.zoom\", function() { gesture(this, arguments).event(event).end(); })\n        .tween(\"zoom\", function() {\n          var that = this,\n              args = arguments,\n              g = gesture(that, args).event(event),\n              e = extent.apply(that, args),\n              p = point == null ? centroid(e) : typeof point === \"function\" ? point.apply(that, args) : point,\n              w = Math.max(e[1][0] - e[0][0], e[1][1] - e[0][1]),\n              a = that.__zoom,\n              b = typeof transform === \"function\" ? transform.apply(that, args) : transform,\n              i = interpolate(a.invert(p).concat(w / a.k), b.invert(p).concat(w / b.k));\n          return function(t) {\n            if (t === 1) t = b; // Avoid rounding error on end.\n            else { var l = i(t), k = w / l[2]; t = new Transform(k, p[0] - l[0] * k, p[1] - l[1] * k); }\n            g.zoom(null, t);\n          };\n        });\n  }\n\n  function gesture(that, args, clean) {\n    return (!clean && that.__zooming) || new Gesture(that, args);\n  }\n\n  function Gesture(that, args) {\n    this.that = that;\n    this.args = args;\n    this.active = 0;\n    this.sourceEvent = null;\n    this.extent = extent.apply(that, args);\n    this.taps = 0;\n  }\n\n  Gesture.prototype = {\n    event: function(event) {\n      if (event) this.sourceEvent = event;\n      return this;\n    },\n    start: function() {\n      if (++this.active === 1) {\n        this.that.__zooming = this;\n        this.emit(\"start\");\n      }\n      return this;\n    },\n    zoom: function(key, transform) {\n      if (this.mouse && key !== \"mouse\") this.mouse[1] = transform.invert(this.mouse[0]);\n      if (this.touch0 && key !== \"touch\") this.touch0[1] = transform.invert(this.touch0[0]);\n      if (this.touch1 && key !== \"touch\") this.touch1[1] = transform.invert(this.touch1[0]);\n      this.that.__zoom = transform;\n      this.emit(\"zoom\");\n      return this;\n    },\n    end: function() {\n      if (--this.active === 0) {\n        delete this.that.__zooming;\n        this.emit(\"end\");\n      }\n      return this;\n    },\n    emit: function(type) {\n      var d = select(this.that).datum();\n      listeners.call(\n        type,\n        this.that,\n        new ZoomEvent(type, {\n          sourceEvent: this.sourceEvent,\n          target: zoom,\n          type,\n          transform: this.that.__zoom,\n          dispatch: listeners\n        }),\n        d\n      );\n    }\n  };\n\n  function wheeled(event, ...args) {\n    if (!filter.apply(this, arguments)) return;\n    var g = gesture(this, args).event(event),\n        t = this.__zoom,\n        k = Math.max(scaleExtent[0], Math.min(scaleExtent[1], t.k * Math.pow(2, wheelDelta.apply(this, arguments)))),\n        p = pointer(event);\n\n    // If the mouse is in the same location as before, reuse it.\n    // If there were recent wheel events, reset the wheel idle timeout.\n    if (g.wheel) {\n      if (g.mouse[0][0] !== p[0] || g.mouse[0][1] !== p[1]) {\n        g.mouse[1] = t.invert(g.mouse[0] = p);\n      }\n      clearTimeout(g.wheel);\n    }\n\n    // If this wheel event won’t trigger a transform change, ignore it.\n    else if (t.k === k) return;\n\n    // Otherwise, capture the mouse point and location at the start.\n    else {\n      g.mouse = [p, t.invert(p)];\n      interrupt(this);\n      g.start();\n    }\n\n    noevent(event);\n    g.wheel = setTimeout(wheelidled, wheelDelay);\n    g.zoom(\"mouse\", constrain(translate(scale(t, k), g.mouse[0], g.mouse[1]), g.extent, translateExtent));\n\n    function wheelidled() {\n      g.wheel = null;\n      g.end();\n    }\n  }\n\n  function mousedowned(event, ...args) {\n    if (touchending || !filter.apply(this, arguments)) return;\n    var currentTarget = event.currentTarget,\n        g = gesture(this, args, true).event(event),\n        v = select(event.view).on(\"mousemove.zoom\", mousemoved, true).on(\"mouseup.zoom\", mouseupped, true),\n        p = pointer(event, currentTarget),\n        x0 = event.clientX,\n        y0 = event.clientY;\n\n    dragDisable(event.view);\n    nopropagation(event);\n    g.mouse = [p, this.__zoom.invert(p)];\n    interrupt(this);\n    g.start();\n\n    function mousemoved(event) {\n      noevent(event);\n      if (!g.moved) {\n        var dx = event.clientX - x0, dy = event.clientY - y0;\n        g.moved = dx * dx + dy * dy > clickDistance2;\n      }\n      g.event(event)\n       .zoom(\"mouse\", constrain(translate(g.that.__zoom, g.mouse[0] = pointer(event, currentTarget), g.mouse[1]), g.extent, translateExtent));\n    }\n\n    function mouseupped(event) {\n      v.on(\"mousemove.zoom mouseup.zoom\", null);\n      dragEnable(event.view, g.moved);\n      noevent(event);\n      g.event(event).end();\n    }\n  }\n\n  function dblclicked(event, ...args) {\n    if (!filter.apply(this, arguments)) return;\n    var t0 = this.__zoom,\n        p0 = pointer(event.changedTouches ? event.changedTouches[0] : event, this),\n        p1 = t0.invert(p0),\n        k1 = t0.k * (event.shiftKey ? 0.5 : 2),\n        t1 = constrain(translate(scale(t0, k1), p0, p1), extent.apply(this, args), translateExtent);\n\n    noevent(event);\n    if (duration > 0) select(this).transition().duration(duration).call(schedule, t1, p0, event);\n    else select(this).call(zoom.transform, t1, p0, event);\n  }\n\n  function touchstarted(event, ...args) {\n    if (!filter.apply(this, arguments)) return;\n    var touches = event.touches,\n        n = touches.length,\n        g = gesture(this, args, event.changedTouches.length === n).event(event),\n        started, i, t, p;\n\n    nopropagation(event);\n    for (i = 0; i < n; ++i) {\n      t = touches[i], p = pointer(t, this);\n      p = [p, this.__zoom.invert(p), t.identifier];\n      if (!g.touch0) g.touch0 = p, started = true, g.taps = 1 + !!touchstarting;\n      else if (!g.touch1 && g.touch0[2] !== p[2]) g.touch1 = p, g.taps = 0;\n    }\n\n    if (touchstarting) touchstarting = clearTimeout(touchstarting);\n\n    if (started) {\n      if (g.taps < 2) touchfirst = p[0], touchstarting = setTimeout(function() { touchstarting = null; }, touchDelay);\n      interrupt(this);\n      g.start();\n    }\n  }\n\n  function touchmoved(event, ...args) {\n    if (!this.__zooming) return;\n    var g = gesture(this, args).event(event),\n        touches = event.changedTouches,\n        n = touches.length, i, t, p, l;\n\n    noevent(event);\n    for (i = 0; i < n; ++i) {\n      t = touches[i], p = pointer(t, this);\n      if (g.touch0 && g.touch0[2] === t.identifier) g.touch0[0] = p;\n      else if (g.touch1 && g.touch1[2] === t.identifier) g.touch1[0] = p;\n    }\n    t = g.that.__zoom;\n    if (g.touch1) {\n      var p0 = g.touch0[0], l0 = g.touch0[1],\n          p1 = g.touch1[0], l1 = g.touch1[1],\n          dp = (dp = p1[0] - p0[0]) * dp + (dp = p1[1] - p0[1]) * dp,\n          dl = (dl = l1[0] - l0[0]) * dl + (dl = l1[1] - l0[1]) * dl;\n      t = scale(t, Math.sqrt(dp / dl));\n      p = [(p0[0] + p1[0]) / 2, (p0[1] + p1[1]) / 2];\n      l = [(l0[0] + l1[0]) / 2, (l0[1] + l1[1]) / 2];\n    }\n    else if (g.touch0) p = g.touch0[0], l = g.touch0[1];\n    else return;\n\n    g.zoom(\"touch\", constrain(translate(t, p, l), g.extent, translateExtent));\n  }\n\n  function touchended(event, ...args) {\n    if (!this.__zooming) return;\n    var g = gesture(this, args).event(event),\n        touches = event.changedTouches,\n        n = touches.length, i, t;\n\n    nopropagation(event);\n    if (touchending) clearTimeout(touchending);\n    touchending = setTimeout(function() { touchending = null; }, touchDelay);\n    for (i = 0; i < n; ++i) {\n      t = touches[i];\n      if (g.touch0 && g.touch0[2] === t.identifier) delete g.touch0;\n      else if (g.touch1 && g.touch1[2] === t.identifier) delete g.touch1;\n    }\n    if (g.touch1 && !g.touch0) g.touch0 = g.touch1, delete g.touch1;\n    if (g.touch0) g.touch0[1] = this.__zoom.invert(g.touch0[0]);\n    else {\n      g.end();\n      // If this was a dbltap, reroute to the (optional) dblclick.zoom handler.\n      if (g.taps === 2) {\n        t = pointer(t, this);\n        if (Math.hypot(touchfirst[0] - t[0], touchfirst[1] - t[1]) < tapDistance) {\n          var p = select(this).on(\"dblclick.zoom\");\n          if (p) p.apply(this, arguments);\n        }\n      }\n    }\n  }\n\n  zoom.wheelDelta = function(_) {\n    return arguments.length ? (wheelDelta = typeof _ === \"function\" ? _ : constant(+_), zoom) : wheelDelta;\n  };\n\n  zoom.filter = function(_) {\n    return arguments.length ? (filter = typeof _ === \"function\" ? _ : constant(!!_), zoom) : filter;\n  };\n\n  zoom.touchable = function(_) {\n    return arguments.length ? (touchable = typeof _ === \"function\" ? _ : constant(!!_), zoom) : touchable;\n  };\n\n  zoom.extent = function(_) {\n    return arguments.length ? (extent = typeof _ === \"function\" ? _ : constant([[+_[0][0], +_[0][1]], [+_[1][0], +_[1][1]]]), zoom) : extent;\n  };\n\n  zoom.scaleExtent = function(_) {\n    return arguments.length ? (scaleExtent[0] = +_[0], scaleExtent[1] = +_[1], zoom) : [scaleExtent[0], scaleExtent[1]];\n  };\n\n  zoom.translateExtent = function(_) {\n    return arguments.length ? (translateExtent[0][0] = +_[0][0], translateExtent[1][0] = +_[1][0], translateExtent[0][1] = +_[0][1], translateExtent[1][1] = +_[1][1], zoom) : [[translateExtent[0][0], translateExtent[0][1]], [translateExtent[1][0], translateExtent[1][1]]];\n  };\n\n  zoom.constrain = function(_) {\n    return arguments.length ? (constrain = _, zoom) : constrain;\n  };\n\n  zoom.duration = function(_) {\n    return arguments.length ? (duration = +_, zoom) : duration;\n  };\n\n  zoom.interpolate = function(_) {\n    return arguments.length ? (interpolate = _, zoom) : interpolate;\n  };\n\n  zoom.on = function() {\n    var value = listeners.on.apply(listeners, arguments);\n    return value === listeners ? zoom : value;\n  };\n\n  zoom.clickDistance = function(_) {\n    return arguments.length ? (clickDistance2 = (_ = +_) * _, zoom) : Math.sqrt(clickDistance2);\n  };\n\n  zoom.tapDistance = function(_) {\n    return arguments.length ? (tapDistance = +_, zoom) : tapDistance;\n  };\n\n  return zoom;\n}\n", "export {default as zoom} from \"./zoom.js\";\nexport {default as zoomTransform, identity as zoomIdentity, Transform as ZoomTransform} from \"./transform.js\";\n", "export * from \"d3-array\";\nexport * from \"d3-axis\";\nexport * from \"d3-brush\";\nexport * from \"d3-chord\";\nexport * from \"d3-color\";\nexport * from \"d3-contour\";\nexport * from \"d3-delaunay\";\nexport * from \"d3-dispatch\";\nexport * from \"d3-drag\";\nexport * from \"d3-dsv\";\nexport * from \"d3-ease\";\nexport * from \"d3-fetch\";\nexport * from \"d3-force\";\nexport * from \"d3-format\";\nexport * from \"d3-geo\";\nexport * from \"d3-hierarchy\";\nexport * from \"d3-interpolate\";\nexport * from \"d3-path\";\nexport * from \"d3-polygon\";\nexport * from \"d3-quadtree\";\nexport * from \"d3-random\";\nexport * from \"d3-scale\";\nexport * from \"d3-scale-chromatic\";\nexport * from \"d3-selection\";\nexport * from \"d3-shape\";\nexport * from \"d3-time\";\nexport * from \"d3-time-format\";\nexport * from \"d3-timer\";\nexport * from \"d3-transition\";\nexport * from \"d3-zoom\";\n"], "names": [], "sourceRoot": ""}