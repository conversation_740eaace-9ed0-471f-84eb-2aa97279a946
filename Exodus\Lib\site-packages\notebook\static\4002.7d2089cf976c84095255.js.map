{"version": 3, "file": "4002.7d2089cf976c84095255.js?v=7d2089cf976c84095255", "mappings": ";;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,mCAAmC,KAAK;AACxC,yJAAyJ,KAAK;AAC9J,oFAAoF,EAAE,cAAc,EAAE,cAAc,EAAE,qBAAqB,KAAK;;AAEhJ,8CAA8C,KAAK,kBAAkB,KAAK;AAC1E,oDAAoD,KAAK,iBAAiB,KAAK;AAC/E;AACA,mDAAmD,KAAK,kBAAkB,KAAK,gCAAgC,KAAK,kBAAkB,KAAK,uCAAuC,KAAK,iBAAiB,KAAK,sBAAsB,KAAK;;AAExO;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB,wBAAwB;AACxB,oBAAoB,KAAK,oBAAoB;AAC7C;;AAEA;AACA;;AAEA;AACA;AACA;AACA,kCAAkC,0BAA0B;AAC5D;AACA;;AAEA;AACA;AACA;AACA;AACA,6DAA6D;;AAE7D;AACA;;AAEA;AACA;;AAEA;AACA,oCAAoC,uBAAuB;AAC3D;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA,kBAAkB,kBAAkB;;AAEpC;AACA;;AAEA;AACA;AACA;AACA;;AAEO;AACP;AACA;AACA;AACA,YAAY,kCAAkC;AAC9C;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;;AAEA;AACA;;AAEA;AACA,mBAAmB;AACnB;;AAEA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA;AACA;AACA,GAAG;;AAEH;AACA,oBAAoB,uBAAuB,QAAQ;AACnD,oBAAoB,SAAS,EAAE;AAC/B;AACA;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/clojure.js"], "sourcesContent": ["var atoms = [\"false\", \"nil\", \"true\"];\nvar specialForms = [\".\", \"catch\", \"def\", \"do\", \"if\", \"monitor-enter\",\n                    \"monitor-exit\", \"new\", \"quote\", \"recur\", \"set!\", \"throw\", \"try\", \"var\"];\nvar coreSymbols = [\"*\", \"*'\", \"*1\", \"*2\", \"*3\", \"*agent*\",\n                   \"*allow-unresolved-vars*\", \"*assert*\", \"*clojure-version*\",\n                   \"*command-line-args*\", \"*compile-files*\", \"*compile-path*\",\n                   \"*compiler-options*\", \"*data-readers*\", \"*default-data-reader-fn*\", \"*e\",\n                   \"*err*\", \"*file*\", \"*flush-on-newline*\", \"*fn-loader*\", \"*in*\",\n                   \"*math-context*\", \"*ns*\", \"*out*\", \"*print-dup*\", \"*print-length*\",\n                   \"*print-level*\", \"*print-meta*\", \"*print-namespace-maps*\",\n                   \"*print-readably*\", \"*read-eval*\", \"*reader-resolver*\", \"*source-path*\",\n                   \"*suppress-read*\", \"*unchecked-math*\", \"*use-context-classloader*\",\n                   \"*verbose-defrecords*\", \"*warn-on-reflection*\", \"+\", \"+'\", \"-\", \"-'\",\n                   \"->\", \"->>\", \"->ArrayChunk\", \"->Eduction\", \"->Vec\", \"->VecNode\",\n                   \"->VecSeq\", \"-cache-protocol-fn\", \"-reset-methods\", \"..\", \"/\", \"<\", \"<=\",\n                   \"=\", \"==\", \">\", \">=\", \"EMPTY-NODE\", \"Inst\", \"StackTraceElement->vec\",\n                   \"Throwable->map\", \"accessor\", \"aclone\", \"add-classpath\", \"add-watch\",\n                   \"agent\", \"agent-error\", \"agent-errors\", \"aget\", \"alength\", \"alias\",\n                   \"all-ns\", \"alter\", \"alter-meta!\", \"alter-var-root\", \"amap\", \"ancestors\",\n                   \"and\", \"any?\", \"apply\", \"areduce\", \"array-map\", \"as->\", \"aset\",\n                   \"aset-boolean\", \"aset-byte\", \"aset-char\", \"aset-double\", \"aset-float\",\n                   \"aset-int\", \"aset-long\", \"aset-short\", \"assert\", \"assoc\", \"assoc!\",\n                   \"assoc-in\", \"associative?\", \"atom\", \"await\", \"await-for\", \"await1\",\n                   \"bases\", \"bean\", \"bigdec\", \"bigint\", \"biginteger\", \"binding\", \"bit-and\",\n                   \"bit-and-not\", \"bit-clear\", \"bit-flip\", \"bit-not\", \"bit-or\", \"bit-set\",\n                   \"bit-shift-left\", \"bit-shift-right\", \"bit-test\", \"bit-xor\", \"boolean\",\n                   \"boolean-array\", \"boolean?\", \"booleans\", \"bound-fn\", \"bound-fn*\",\n                   \"bound?\", \"bounded-count\", \"butlast\", \"byte\", \"byte-array\", \"bytes\",\n                   \"bytes?\", \"case\", \"cast\", \"cat\", \"char\", \"char-array\",\n                   \"char-escape-string\", \"char-name-string\", \"char?\", \"chars\", \"chunk\",\n                   \"chunk-append\", \"chunk-buffer\", \"chunk-cons\", \"chunk-first\", \"chunk-next\",\n                   \"chunk-rest\", \"chunked-seq?\", \"class\", \"class?\", \"clear-agent-errors\",\n                   \"clojure-version\", \"coll?\", \"comment\", \"commute\", \"comp\", \"comparator\",\n                   \"compare\", \"compare-and-set!\", \"compile\", \"complement\", \"completing\",\n                   \"concat\", \"cond\", \"cond->\", \"cond->>\", \"condp\", \"conj\", \"conj!\", \"cons\",\n                   \"constantly\", \"construct-proxy\", \"contains?\", \"count\", \"counted?\",\n                   \"create-ns\", \"create-struct\", \"cycle\", \"dec\", \"dec'\", \"decimal?\",\n                   \"declare\", \"dedupe\", \"default-data-readers\", \"definline\", \"definterface\",\n                   \"defmacro\", \"defmethod\", \"defmulti\", \"defn\", \"defn-\", \"defonce\",\n                   \"defprotocol\", \"defrecord\", \"defstruct\", \"deftype\", \"delay\", \"delay?\",\n                   \"deliver\", \"denominator\", \"deref\", \"derive\", \"descendants\", \"destructure\",\n                   \"disj\", \"disj!\", \"dissoc\", \"dissoc!\", \"distinct\", \"distinct?\", \"doall\",\n                   \"dorun\", \"doseq\", \"dosync\", \"dotimes\", \"doto\", \"double\", \"double-array\",\n                   \"double?\", \"doubles\", \"drop\", \"drop-last\", \"drop-while\", \"eduction\",\n                   \"empty\", \"empty?\", \"ensure\", \"ensure-reduced\", \"enumeration-seq\",\n                   \"error-handler\", \"error-mode\", \"eval\", \"even?\", \"every-pred\", \"every?\",\n                   \"ex-data\", \"ex-info\", \"extend\", \"extend-protocol\", \"extend-type\",\n                   \"extenders\", \"extends?\", \"false?\", \"ffirst\", \"file-seq\", \"filter\",\n                   \"filterv\", \"find\", \"find-keyword\", \"find-ns\", \"find-protocol-impl\",\n                   \"find-protocol-method\", \"find-var\", \"first\", \"flatten\", \"float\",\n                   \"float-array\", \"float?\", \"floats\", \"flush\", \"fn\", \"fn?\", \"fnext\", \"fnil\",\n                   \"for\", \"force\", \"format\", \"frequencies\", \"future\", \"future-call\",\n                   \"future-cancel\", \"future-cancelled?\", \"future-done?\", \"future?\",\n                   \"gen-class\", \"gen-interface\", \"gensym\", \"get\", \"get-in\", \"get-method\",\n                   \"get-proxy-class\", \"get-thread-bindings\", \"get-validator\", \"group-by\",\n                   \"halt-when\", \"hash\", \"hash-combine\", \"hash-map\", \"hash-ordered-coll\",\n                   \"hash-set\", \"hash-unordered-coll\", \"ident?\", \"identical?\", \"identity\",\n                   \"if-let\", \"if-not\", \"if-some\", \"ifn?\", \"import\", \"in-ns\", \"inc\", \"inc'\",\n                   \"indexed?\", \"init-proxy\", \"inst-ms\", \"inst-ms*\", \"inst?\", \"instance?\",\n                   \"int\", \"int-array\", \"int?\", \"integer?\", \"interleave\", \"intern\",\n                   \"interpose\", \"into\", \"into-array\", \"ints\", \"io!\", \"isa?\", \"iterate\",\n                   \"iterator-seq\", \"juxt\", \"keep\", \"keep-indexed\", \"key\", \"keys\", \"keyword\",\n                   \"keyword?\", \"last\", \"lazy-cat\", \"lazy-seq\", \"let\", \"letfn\", \"line-seq\",\n                   \"list\", \"list*\", \"list?\", \"load\", \"load-file\", \"load-reader\",\n                   \"load-string\", \"loaded-libs\", \"locking\", \"long\", \"long-array\", \"longs\",\n                   \"loop\", \"macroexpand\", \"macroexpand-1\", \"make-array\", \"make-hierarchy\",\n                   \"map\", \"map-entry?\", \"map-indexed\", \"map?\", \"mapcat\", \"mapv\", \"max\",\n                   \"max-key\", \"memfn\", \"memoize\", \"merge\", \"merge-with\", \"meta\",\n                   \"method-sig\", \"methods\", \"min\", \"min-key\", \"mix-collection-hash\", \"mod\",\n                   \"munge\", \"name\", \"namespace\", \"namespace-munge\", \"nat-int?\", \"neg-int?\",\n                   \"neg?\", \"newline\", \"next\", \"nfirst\", \"nil?\", \"nnext\", \"not\", \"not-any?\",\n                   \"not-empty\", \"not-every?\", \"not=\", \"ns\", \"ns-aliases\", \"ns-imports\",\n                   \"ns-interns\", \"ns-map\", \"ns-name\", \"ns-publics\", \"ns-refers\",\n                   \"ns-resolve\", \"ns-unalias\", \"ns-unmap\", \"nth\", \"nthnext\", \"nthrest\",\n                   \"num\", \"number?\", \"numerator\", \"object-array\", \"odd?\", \"or\", \"parents\",\n                   \"partial\", \"partition\", \"partition-all\", \"partition-by\", \"pcalls\", \"peek\",\n                   \"persistent!\", \"pmap\", \"pop\", \"pop!\", \"pop-thread-bindings\", \"pos-int?\",\n                   \"pos?\", \"pr\", \"pr-str\", \"prefer-method\", \"prefers\",\n                   \"primitives-classnames\", \"print\", \"print-ctor\", \"print-dup\",\n                   \"print-method\", \"print-simple\", \"print-str\", \"printf\", \"println\",\n                   \"println-str\", \"prn\", \"prn-str\", \"promise\", \"proxy\",\n                   \"proxy-call-with-super\", \"proxy-mappings\", \"proxy-name\", \"proxy-super\",\n                   \"push-thread-bindings\", \"pvalues\", \"qualified-ident?\",\n                   \"qualified-keyword?\", \"qualified-symbol?\", \"quot\", \"rand\", \"rand-int\",\n                   \"rand-nth\", \"random-sample\", \"range\", \"ratio?\", \"rational?\",\n                   \"rationalize\", \"re-find\", \"re-groups\", \"re-matcher\", \"re-matches\",\n                   \"re-pattern\", \"re-seq\", \"read\", \"read-line\", \"read-string\",\n                   \"reader-conditional\", \"reader-conditional?\", \"realized?\", \"record?\",\n                   \"reduce\", \"reduce-kv\", \"reduced\", \"reduced?\", \"reductions\", \"ref\",\n                   \"ref-history-count\", \"ref-max-history\", \"ref-min-history\", \"ref-set\",\n                   \"refer\", \"refer-clojure\", \"reify\", \"release-pending-sends\", \"rem\",\n                   \"remove\", \"remove-all-methods\", \"remove-method\", \"remove-ns\",\n                   \"remove-watch\", \"repeat\", \"repeatedly\", \"replace\", \"replicate\", \"require\",\n                   \"reset!\", \"reset-meta!\", \"reset-vals!\", \"resolve\", \"rest\",\n                   \"restart-agent\", \"resultset-seq\", \"reverse\", \"reversible?\", \"rseq\",\n                   \"rsubseq\", \"run!\", \"satisfies?\", \"second\", \"select-keys\", \"send\",\n                   \"send-off\", \"send-via\", \"seq\", \"seq?\", \"seqable?\", \"seque\", \"sequence\",\n                   \"sequential?\", \"set\", \"set-agent-send-executor!\",\n                   \"set-agent-send-off-executor!\", \"set-error-handler!\", \"set-error-mode!\",\n                   \"set-validator!\", \"set?\", \"short\", \"short-array\", \"shorts\", \"shuffle\",\n                   \"shutdown-agents\", \"simple-ident?\", \"simple-keyword?\", \"simple-symbol?\",\n                   \"slurp\", \"some\", \"some->\", \"some->>\", \"some-fn\", \"some?\", \"sort\",\n                   \"sort-by\", \"sorted-map\", \"sorted-map-by\", \"sorted-set\", \"sorted-set-by\",\n                   \"sorted?\", \"special-symbol?\", \"spit\", \"split-at\", \"split-with\", \"str\",\n                   \"string?\", \"struct\", \"struct-map\", \"subs\", \"subseq\", \"subvec\", \"supers\",\n                   \"swap!\", \"swap-vals!\", \"symbol\", \"symbol?\", \"sync\", \"tagged-literal\",\n                   \"tagged-literal?\", \"take\", \"take-last\", \"take-nth\", \"take-while\", \"test\",\n                   \"the-ns\", \"thread-bound?\", \"time\", \"to-array\", \"to-array-2d\",\n                   \"trampoline\", \"transduce\", \"transient\", \"tree-seq\", \"true?\", \"type\",\n                   \"unchecked-add\", \"unchecked-add-int\", \"unchecked-byte\", \"unchecked-char\",\n                   \"unchecked-dec\", \"unchecked-dec-int\", \"unchecked-divide-int\",\n                   \"unchecked-double\", \"unchecked-float\", \"unchecked-inc\",\n                   \"unchecked-inc-int\", \"unchecked-int\", \"unchecked-long\",\n                   \"unchecked-multiply\", \"unchecked-multiply-int\", \"unchecked-negate\",\n                   \"unchecked-negate-int\", \"unchecked-remainder-int\", \"unchecked-short\",\n                   \"unchecked-subtract\", \"unchecked-subtract-int\", \"underive\", \"unquote\",\n                   \"unquote-splicing\", \"unreduced\", \"unsigned-bit-shift-right\", \"update\",\n                   \"update-in\", \"update-proxy\", \"uri?\", \"use\", \"uuid?\", \"val\", \"vals\",\n                   \"var-get\", \"var-set\", \"var?\", \"vary-meta\", \"vec\", \"vector\", \"vector-of\",\n                   \"vector?\", \"volatile!\", \"volatile?\", \"vreset!\", \"vswap!\", \"when\",\n                   \"when-first\", \"when-let\", \"when-not\", \"when-some\", \"while\",\n                   \"with-bindings\", \"with-bindings*\", \"with-in-str\", \"with-loading-context\",\n                   \"with-local-vars\", \"with-meta\", \"with-open\", \"with-out-str\",\n                   \"with-precision\", \"with-redefs\", \"with-redefs-fn\", \"xml-seq\", \"zero?\",\n                   \"zipmap\"];\nvar haveBodyParameter = [\n  \"->\", \"->>\", \"as->\", \"binding\", \"bound-fn\", \"case\", \"catch\", \"comment\",\n  \"cond\", \"cond->\", \"cond->>\", \"condp\", \"def\", \"definterface\", \"defmethod\",\n  \"defn\", \"defmacro\", \"defprotocol\", \"defrecord\", \"defstruct\", \"deftype\",\n  \"do\", \"doseq\", \"dotimes\", \"doto\", \"extend\", \"extend-protocol\",\n  \"extend-type\", \"fn\", \"for\", \"future\", \"if\", \"if-let\", \"if-not\", \"if-some\",\n  \"let\", \"letfn\", \"locking\", \"loop\", \"ns\", \"proxy\", \"reify\", \"struct-map\",\n  \"some->\", \"some->>\", \"try\", \"when\", \"when-first\", \"when-let\", \"when-not\",\n  \"when-some\", \"while\", \"with-bindings\", \"with-bindings*\", \"with-in-str\",\n  \"with-loading-context\", \"with-local-vars\", \"with-meta\", \"with-open\",\n  \"with-out-str\", \"with-precision\", \"with-redefs\", \"with-redefs-fn\"];\n\nvar atom = createLookupMap(atoms);\nvar specialForm = createLookupMap(specialForms);\nvar coreSymbol = createLookupMap(coreSymbols);\nvar hasBodyParameter = createLookupMap(haveBodyParameter);\nvar delimiter = /^(?:[\\\\\\[\\]\\s\"(),;@^`{}~]|$)/;\nvar numberLiteral = /^(?:[+\\-]?\\d+(?:(?:N|(?:[eE][+\\-]?\\d+))|(?:\\.?\\d*(?:M|(?:[eE][+\\-]?\\d+))?)|\\/\\d+|[xX][0-9a-fA-F]+|r[0-9a-zA-Z]+)?(?=[\\\\\\[\\]\\s\"#'(),;@^`{}~]|$))/;\nvar characterLiteral = /^(?:\\\\(?:backspace|formfeed|newline|return|space|tab|o[0-7]{3}|u[0-9A-Fa-f]{4}|x[0-9A-Fa-f]{4}|.)?(?=[\\\\\\[\\]\\s\"(),;@^`{}~]|$))/;\n\n// simple-namespace := /^[^\\\\\\/\\[\\]\\d\\s\"#'(),;@^`{}~.][^\\\\\\[\\]\\s\"(),;@^`{}~.\\/]*/\n// simple-symbol    := /^(?:\\/|[^\\\\\\/\\[\\]\\d\\s\"#'(),;@^`{}~][^\\\\\\[\\]\\s\"(),;@^`{}~]*)/\n// qualified-symbol := (<simple-namespace>(<.><simple-namespace>)*</>)?<simple-symbol>\nvar qualifiedSymbol = /^(?:(?:[^\\\\\\/\\[\\]\\d\\s\"#'(),;@^`{}~.][^\\\\\\[\\]\\s\"(),;@^`{}~.\\/]*(?:\\.[^\\\\\\/\\[\\]\\d\\s\"#'(),;@^`{}~.][^\\\\\\[\\]\\s\"(),;@^`{}~.\\/]*)*\\/)?(?:\\/|[^\\\\\\/\\[\\]\\d\\s\"#'(),;@^`{}~][^\\\\\\[\\]\\s\"(),;@^`{}~]*)*(?=[\\\\\\[\\]\\s\"(),;@^`{}~]|$))/;\n\nfunction base(stream, state) {\n  if (stream.eatSpace() || stream.eat(\",\")) return [\"space\", null];\n  if (stream.match(numberLiteral)) return [null, \"number\"];\n  if (stream.match(characterLiteral)) return [null, \"string.special\"];\n  if (stream.eat(/^\"/)) return (state.tokenize = inString)(stream, state);\n  if (stream.eat(/^[(\\[{]/)) return [\"open\", \"bracket\"];\n  if (stream.eat(/^[)\\]}]/)) return [\"close\", \"bracket\"];\n  if (stream.eat(/^;/)) {stream.skipToEnd(); return [\"space\", \"comment\"];}\n  if (stream.eat(/^[#'@^`~]/)) return [null, \"meta\"];\n\n  var matches = stream.match(qualifiedSymbol);\n  var symbol = matches && matches[0];\n\n  if (!symbol) {\n    // advance stream by at least one character so we don't get stuck.\n    stream.next();\n    stream.eatWhile(function (c) {return !is(c, delimiter);});\n    return [null, \"error\"];\n  }\n\n  if (symbol === \"comment\" && state.lastToken === \"(\")\n    return (state.tokenize = inComment)(stream, state);\n  if (is(symbol, atom) || symbol.charAt(0) === \":\") return [\"symbol\", \"atom\"];\n  if (is(symbol, specialForm) || is(symbol, coreSymbol)) return [\"symbol\", \"keyword\"];\n  if (state.lastToken === \"(\") return [\"symbol\", \"builtin\"]; // other operator\n\n  return [\"symbol\", \"variable\"];\n}\n\nfunction inString(stream, state) {\n  var escaped = false, next;\n\n  while (next = stream.next()) {\n    if (next === \"\\\"\" && !escaped) {state.tokenize = base; break;}\n    escaped = !escaped && next === \"\\\\\";\n  }\n\n  return [null, \"string\"];\n}\n\nfunction inComment(stream, state) {\n  var parenthesisCount = 1;\n  var next;\n\n  while (next = stream.next()) {\n    if (next === \")\") parenthesisCount--;\n    if (next === \"(\") parenthesisCount++;\n    if (parenthesisCount === 0) {\n      stream.backUp(1);\n      state.tokenize = base;\n      break;\n    }\n  }\n\n  return [\"space\", \"comment\"];\n}\n\nfunction createLookupMap(words) {\n  var obj = {};\n\n  for (var i = 0; i < words.length; ++i) obj[words[i]] = true;\n\n  return obj;\n}\n\nfunction is(value, test) {\n  if (test instanceof RegExp) return test.test(value);\n  if (test instanceof Object) return test.propertyIsEnumerable(value);\n}\n\nexport const clojure = {\n  name: \"clojure\",\n  startState: function () {\n    return {\n      ctx: {prev: null, start: 0, indentTo: 0},\n      lastToken: null,\n      tokenize: base\n    };\n  },\n\n  token: function (stream, state) {\n    if (stream.sol() && (typeof state.ctx.indentTo !== \"number\"))\n      state.ctx.indentTo = state.ctx.start + 1;\n\n    var typeStylePair = state.tokenize(stream, state);\n    var type = typeStylePair[0];\n    var style = typeStylePair[1];\n    var current = stream.current();\n\n    if (type !== \"space\") {\n      if (state.lastToken === \"(\" && state.ctx.indentTo === null) {\n        if (type === \"symbol\" && is(current, hasBodyParameter))\n          state.ctx.indentTo = state.ctx.start + stream.indentUnit;\n        else state.ctx.indentTo = \"next\";\n      } else if (state.ctx.indentTo === \"next\") {\n        state.ctx.indentTo = stream.column();\n      }\n\n      state.lastToken = current;\n    }\n\n    if (type === \"open\")\n      state.ctx = {prev: state.ctx, start: stream.column(), indentTo: null};\n    else if (type === \"close\") state.ctx = state.ctx.prev || state.ctx;\n\n    return style;\n  },\n\n  indent: function (state) {\n    var i = state.ctx.indentTo;\n\n    return (typeof i === \"number\") ?\n      i :\n      state.ctx.start + 1;\n  },\n\n  languageData: {\n    closeBrackets: {brackets: [\"(\", \"[\", \"{\", '\"']},\n    commentTokens: {line: \";;\"},\n    autocomplete: [].concat(atoms, specialForms, coreSymbols)\n  }\n};\n"], "names": [], "sourceRoot": ""}