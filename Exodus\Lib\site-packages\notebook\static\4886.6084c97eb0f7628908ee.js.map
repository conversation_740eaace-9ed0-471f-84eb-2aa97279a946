{"version": 3, "file": "4886.6084c97eb0f7628908ee.js?v=6084c97eb0f7628908ee", "mappings": ";;;;;;;;;;;;;;;;;AAG8B;AAIA;AAIA;AAeA;;AAE9B;AACA;AACA,0BAA0B,qEAAM;AAChC,sBAAsB,gBAAgB,KAAK;AAC3C;AACA,GAAG;AACH;AACA,2BAA2B,qEAAM;AACjC,KAAK;AACL,UAAU;AACV,gBAAgB,4wDAA4wD;AAC5xD,kBAAkB,iiCAAiiC;AACnjC;AACA,mCAAmC,qEAAM;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA,qBAAqB;AACrB;AACA;AACA,qBAAqB;AACrB;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA,qBAAqB;AACrB;AACA;AACA,qBAAqB;AACrB;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,cAAc,0YAA0Y,IAAI,QAAQ,IAAI,WAAW,IAAI,WAAW,IAAI,WAAW,mBAAmB,YAAY,KAAK,YAAY,oBAAoB,aAAa,+KAA+K,aAAa,IAAI,aAAa,qCAAqC,iHAAiH,KAAK,aAAa,oBAAoB,0BAA0B,uCAAuC,6CAA6C,IAAI,uEAAuE,IAAI,uEAAuE,IAAI,uEAAuE,IAAI,aAAa,IAAI,aAAa,IAAI,uEAAuE,IAAI,iBAAiB,IAAI,qBAAqB,wFAAwF,iGAAiG,qBAAqB,aAAa,KAAK,qDAAqD,IAAI,uEAAuE,mLAAmL,8WAA8W,KAAK,8WAA8W,uDAAuD,oFAAoF,IAAI,uFAAuF,qBAAqB,0BAA0B,oBAAoB,qDAAqD,4HAA4H,6CAA6C,IAAI,6CAA6C,IAAI,iBAAiB,IAAI,aAAa,IAAI,cAAc,IAAI,cAAc,IAAI,4BAA4B,IAAI,4GAA4G,IAAI,cAAc,IAAI,kBAAkB,uCAAuC,0HAA0H,qBAAqB,wEAAwE,uDAAuD,aAAa,IAAI,gFAAgF,oBAAoB,uBAAuB,qBAAqB,aAAa,qBAAqB,wEAAwE,IAAI,sFAAsF,oBAAoB,sDAAsD,sBAAsB,cAAc,IAAI,kCAAkC,IAAI,0BAA0B,oBAAoB,cAAc,KAAK,cAAc,IAAI,+BAA+B,IAAI,wEAAwE,oBAAoB,cAAc,qBAAqB,4BAA4B,qBAAqB,4BAA4B,KAAK,cAAc,oBAAoB,SAAS,qBAAqB,0FAA0F,sOAAsO,SAAS,KAAK,cAAc,qBAAqB,sDAAsD,IAAI,aAAa,qBAAqB,cAAc,IAAI,WAAW,sCAAsC,wEAAwE,sCAAsC,cAAc,IAAI,+CAA+C,IAAI,kBAAkB,qBAAqB,aAAa,uEAAuE,cAAc,sCAAsC,cAAc,qBAAqB,4BAA4B,KAAK,mGAAmG,sCAAsC,aAAa,IAAI,cAAc,sCAAsC,aAAa,IAAI,cAAc,uEAAuE,cAAc,qBAAqB,0FAA0F;AACn+L,sBAAsB,mHAAmH;AACzI,gCAAgC,qEAAM;AACtC;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA,KAAK;AACL,2BAA2B,qEAAM;AACjC;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,qEAAM;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,qEAAM;AACZ,iEAAiE;AACjE;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,kCAAkC,qEAAM;AACxC;AACA;AACA,UAAU;AACV;AACA;AACA,OAAO;AACP;AACA,gCAAgC,qEAAM;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,6BAA6B,qEAAM;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,6BAA6B,qEAAM;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,4BAA4B,qEAAM;AAClC;AACA;AACA,OAAO;AACP;AACA,8BAA8B,qEAAM;AACpC;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA,OAAO;AACP;AACA,4BAA4B,qEAAM;AAClC;AACA,OAAO;AACP;AACA,iCAAiC,qEAAM;AACvC;AACA;AACA,OAAO;AACP;AACA,qCAAqC,qEAAM;AAC3C;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,oCAAoC,qEAAM;AAC1C;AACA;AACA;AACA,OAAO;AACP;AACA,kCAAkC,qEAAM;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,4BAA4B,qEAAM;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,kBAAkB;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA,gBAAgB;AAChB;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA,WAAW;AACX;AACA,OAAO;AACP;AACA,2BAA2B,qEAAM;AACjC;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,OAAO;AACP;AACA,6BAA6B,qEAAM;AACnC;AACA,OAAO;AACP;AACA,gCAAgC,qEAAM;AACtC;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,OAAO;AACP;AACA,qCAAqC,qEAAM;AAC3C;AACA;AACA,UAAU;AACV;AACA;AACA,OAAO;AACP,4DAA4D;AAC5D,gCAAgC,qEAAM;AACtC;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,OAAO;AACP;AACA,iCAAiC,qEAAM;AACvC;AACA,OAAO;AACP;AACA,sCAAsC,qEAAM;AAC5C;AACA,OAAO;AACP,iBAAiB;AACjB,qCAAqC,qEAAM;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,sJAAsJ,gKAAgK,eAAe,cAAc,gUAAgU,YAAY,qHAAqH,YAAY,YAAY,qCAAqC,2BAA2B,oaAAoa,EAAE,MAAM,aAAa,EAAE;AACvyC,oBAAoB,oBAAoB,2NAA2N,iBAAiB,mNAAmN,kBAAkB,2NAA2N,aAAa,mNAAmN,2BAA2B,2MAA2M,iBAAiB,sMAAsM,iBAAiB,sMAAsM,qBAAqB,2MAA2M,qBAAqB,+MAA+M,YAAY,mMAAmM,cAAc,mMAAmM,eAAe,2MAA2M,gBAAgB,2MAA2M,cAAc,2MAA2M,eAAe;AACzkG;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,qEAAM;AACR;AACA;AACA;AACA,CAAC;AACD;AACA;;AAEA;AAC4B;;AAE5B;AACA;AACA;AACA;AACA,IAAI,qEAAM;AACV;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,2EAAY,QAAQ,yEAAS;AACxD;AACA;AACA;AACA,wCAAwC,gFAAiB;AACzD;AACA,yBAAyB,gFAAiB,yBAAyB;AACnE;AACA,+BAA+B,gFAAiB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,8CAA8C,EAAE,gFAAiB,UAAU,EAAE,mCAAmC,gFAAiB,kBAAkB,GAAG,0BAA0B,gFAAiB,uBAAuB,OAAO;AAC3P,kDAAkD,wBAAwB;AAC1E,oCAAoC;AACpC,2CAA2C;AAC3C;AACA;AACA;AACA;AACA;AACA,kCAAkC;AAClC;AACA,0CAA0C;AAC1C;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,oCAAoC,qEAAM,UAAU,yEAAc,mBAAmB,yEAAS;AAC9F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC,qEAAM;AAC/C,wBAAwB,oDAAM;AAC9B;AACA,sBAAsB,oDAAM;AAC5B;AACA,kBAAkB,oDAAM;AACxB;AACA;AACA,mBAAmB,oDAAM;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yDAAyD,QAAQ;AACjE;AACA,OAAO;AACP;AACA,mBAAmB,oDAAM;AACzB;AACA,OAAO;AACP,KAAK;AACL;AACA,uBAAuB,sEAAW;AAClC,uBAAuB,sEAAW;AAClC,6BAA6B,4EAAiB;AAC9C,6BAA6B,4EAAiB;AAC9C,2BAA2B,0EAAe;AAC1C,2BAA2B,0EAAe;AAC1C,qCAAqC,qEAAM,OAAO,yEAAS;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,qEAAM;AACV;AACA;AACA,eAAe,yEAAc,mBAAmB,yEAAS;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,eAAe,yEAAc,mBAAmB,yEAAS;AACzD;AACA;AACA;AACA,YAAY,YAAY;AACxB;AACA,0CAA0C,MAAM,EAAE,uCAAuC,iCAAiC,QAAQ;AAClI;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,yEAAc,mBAAmB,yEAAS;AACzD,YAAY,kBAAkB;AAC9B;AACA;AACA;AACA,iBAAiB,yEAAc,yBAAyB,yEAAS;AACjE;AACA;AACA;AACA;AACA,eAAe,KAAK,EAAE,YAAY,EAAE,KAAK,IAAI,OAAO;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,sBAAsB,uBAAuB;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,yEAAc,mBAAmB,yEAAS;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,oEAAK;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,8DAAG;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC,2BAA2B;AACjE,MAAM;AACN;AACA;AACA,sCAAsC,2BAA2B;AACjE,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,mCAAmC,yEAAc;AACjD;AACA,MAAM,yEAAS;AACf;AACA,mCAAmC,yEAAc;AACjD;AACA,MAAM,yEAAS;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,kBAAkB;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,yEAAS;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,wEAAa;AACrC;AACA;AACA,UAAU;AACV;AACA,UAAU;AACV;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,kBAAkB,yEAAc,sBAAsB,yEAAS;AAC/D,mBAAmB,yEAAS;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wDAAwD,EAAE;AAC1D,wBAAwB,oBAAoB;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oDAAoD,OAAO;AAC3D;AACA;AACA;AACA;AACA,cAAc,wEAAa;AAC3B,aAAa;AACb;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,YAAY;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,yEAAS;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,mCAAmC;AACtD,qBAAqB,sCAAsC;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,IAAI;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,wEAAS;AACrB;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,uBAAuB;AACpC;AACA;;AAEA;AACA,gCAAgC,qEAAM;AACtC,UAAU;AACV;AACA,iBAAiB;AACjB;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA,WAAW;AACX;AACA;AACA,UAAU;AACV;AACA;AACA,UAAU;AACV;;AAEA;AACA,gBAAgB;AAChB;AACA;AACA,gBAAgB;AAChB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;AACZ,cAAc;AACd;AACA;;;AAGA;AACA,YAAY;AACZ;AACA;;AAEA;AACA;AACA;;AAEA;AACA,UAAU;AACV,YAAY;AACZ;;AAEA;AACA,YAAY;AACZ;AACA;;AAEA;AACA;AACA;AACA,UAAU;AACV;AACA;;AAEA;AACA,UAAU;AACV;AACA;;AAEA;AACA,YAAY;AACZ;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,UAAU,mBAAmB;AAC7B,YAAY,mBAAmB;AAC/B;AACA;;AAEA;AACA,UAAU,mBAAmB;AAC7B,YAAY,mBAAmB;AAC/B;AACA;;AAEA;AACA,UAAU,mBAAmB;AAC7B,YAAY,mBAAmB;AAC/B;AACA;;AAEA;AACA,UAAU,mBAAmB;AAC7B,YAAY,mBAAmB;AAC/B;AACA;;AAEA;AACA;AACA,YAAY,mBAAmB;AAC/B;AACA;;AAEA;AACA;AACA,YAAY,mBAAmB;AAC/B;AACA;;AAEA;AACA;AACA,YAAY,mBAAmB;AAC/B;AACA;;AAEA;AACA;AACA,YAAY,mBAAmB;AAC/B;AACA;;AAEA;AACA,UAAU,iBAAiB;AAC3B,YAAY,mBAAmB;AAC/B;AACA;;AAEA;AACA,UAAU,iBAAiB;AAC3B,YAAY,mBAAmB;AAC/B;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;;AAEA;AACA,6BAA6B,qEAAM;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,iCAAiC,qEAAM;AACvC;AACA,CAAC;AACD,2BAA2B,qEAAM;AACjC,EAAE,8DAAG;AACL,EAAE,8DAAG;AACL,UAAU,qCAAqC,EAAE,yEAAS;AAC1D;AACA,cAAc,+EAAiB;AAC/B;AACA,gCAAgC,2FAA4B;AAC5D;AACA;AACA;AACA;AACA,QAAQ,qEAAM;AACd;AACA,EAAE,wEAAa;AACf;AACA;AACA;AACA;AACA;AACA,EAAE,iFAAmB;AACrB,CAAC;AACD;AACA;AACA;AACA;AACA;;AAOE;;;;;;;;;;;;;;AC55D4B;;AAE9B;AAC4B;AAC5B,wCAAwC,qEAAM;AAC9C;AACA;AACA,qBAAqB,oDAAM;AAC3B;AACA,6CAA6C,oDAAM,mDAAmD,oDAAM;AAC5G,kCAAkC,GAAG;AACrC;AACA,CAAC;;AAED;AACA,0CAA0C,qEAAM;AAChD;AACA,UAAU,sBAAsB;AAChC,EAAE,+EAAgB;AAClB;AACA;AACA,EAAE,8DAAG,8BAA8B,SAAS,gBAAgB,QAAQ;AACpE,CAAC;AACD,qDAAqD,qEAAM;AAC3D,4CAA4C;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,oCAAoC,qEAAM;AAC1C,YAAY,aAAa,EAAE,aAAa,EAAE,OAAO,EAAE,OAAO;AAC1D,CAAC;;AAKC", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mermaid/dist/chunks/mermaid.core/chunk-A2AXSNBT.mjs", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mermaid/dist/chunks/mermaid.core/chunk-RZ5BOZE2.mjs"], "sourcesContent": ["import {\n  getDiagramElement,\n  setupViewPortForSVG\n} from \"./chunk-RZ5BOZE2.mjs\";\nimport {\n  getRegisteredLayoutAlgorithm,\n  render\n} from \"./chunk-TYCBKAJE.mjs\";\nimport {\n  getEdgeId,\n  utils_default\n} from \"./chunk-O4NI6UNU.mjs\";\nimport {\n  __name,\n  clear,\n  common_default,\n  getAccDescription,\n  getAccTitle,\n  getConfig2 as getConfig,\n  getDiagramTitle,\n  log,\n  parseGenericTypes,\n  sanitizeText,\n  setAccDescription,\n  setAccTitle,\n  setDiagramTitle\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/diagrams/class/parser/classDiagram.jison\nvar parser = function() {\n  var o = /* @__PURE__ */ __name(function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v) ;\n    return o2;\n  }, \"o\"), $V0 = [1, 18], $V1 = [1, 19], $V2 = [1, 20], $V3 = [1, 41], $V4 = [1, 42], $V5 = [1, 26], $V6 = [1, 24], $V7 = [1, 25], $V8 = [1, 32], $V9 = [1, 33], $Va = [1, 34], $Vb = [1, 45], $Vc = [1, 35], $Vd = [1, 36], $Ve = [1, 37], $Vf = [1, 38], $Vg = [1, 27], $Vh = [1, 28], $Vi = [1, 29], $Vj = [1, 30], $Vk = [1, 31], $Vl = [1, 44], $Vm = [1, 46], $Vn = [1, 43], $Vo = [1, 47], $Vp = [1, 9], $Vq = [1, 8, 9], $Vr = [1, 58], $Vs = [1, 59], $Vt = [1, 60], $Vu = [1, 61], $Vv = [1, 62], $Vw = [1, 63], $Vx = [1, 64], $Vy = [1, 8, 9, 41], $Vz = [1, 76], $VA = [1, 8, 9, 12, 13, 22, 39, 41, 44, 66, 67, 68, 69, 70, 71, 72, 77, 79], $VB = [1, 8, 9, 12, 13, 17, 20, 22, 39, 41, 44, 48, 58, 66, 67, 68, 69, 70, 71, 72, 77, 79, 84, 99, 101, 102], $VC = [13, 58, 84, 99, 101, 102], $VD = [13, 58, 71, 72, 84, 99, 101, 102], $VE = [13, 58, 66, 67, 68, 69, 70, 84, 99, 101, 102], $VF = [1, 98], $VG = [1, 115], $VH = [1, 107], $VI = [1, 113], $VJ = [1, 108], $VK = [1, 109], $VL = [1, 110], $VM = [1, 111], $VN = [1, 112], $VO = [1, 114], $VP = [22, 58, 59, 80, 84, 85, 86, 87, 88, 89], $VQ = [1, 8, 9, 39, 41, 44], $VR = [1, 8, 9, 22], $VS = [1, 143], $VT = [1, 8, 9, 59], $VU = [1, 8, 9, 22, 58, 59, 80, 84, 85, 86, 87, 88, 89];\n  var parser2 = {\n    trace: /* @__PURE__ */ __name(function trace() {\n    }, \"trace\"),\n    yy: {},\n    symbols_: { \"error\": 2, \"start\": 3, \"mermaidDoc\": 4, \"statements\": 5, \"graphConfig\": 6, \"CLASS_DIAGRAM\": 7, \"NEWLINE\": 8, \"EOF\": 9, \"statement\": 10, \"classLabel\": 11, \"SQS\": 12, \"STR\": 13, \"SQE\": 14, \"namespaceName\": 15, \"alphaNumToken\": 16, \"DOT\": 17, \"className\": 18, \"classLiteralName\": 19, \"GENERICTYPE\": 20, \"relationStatement\": 21, \"LABEL\": 22, \"namespaceStatement\": 23, \"classStatement\": 24, \"memberStatement\": 25, \"annotationStatement\": 26, \"clickStatement\": 27, \"styleStatement\": 28, \"cssClassStatement\": 29, \"noteStatement\": 30, \"classDefStatement\": 31, \"direction\": 32, \"acc_title\": 33, \"acc_title_value\": 34, \"acc_descr\": 35, \"acc_descr_value\": 36, \"acc_descr_multiline_value\": 37, \"namespaceIdentifier\": 38, \"STRUCT_START\": 39, \"classStatements\": 40, \"STRUCT_STOP\": 41, \"NAMESPACE\": 42, \"classIdentifier\": 43, \"STYLE_SEPARATOR\": 44, \"members\": 45, \"CLASS\": 46, \"ANNOTATION_START\": 47, \"ANNOTATION_END\": 48, \"MEMBER\": 49, \"SEPARATOR\": 50, \"relation\": 51, \"NOTE_FOR\": 52, \"noteText\": 53, \"NOTE\": 54, \"CLASSDEF\": 55, \"classList\": 56, \"stylesOpt\": 57, \"ALPHA\": 58, \"COMMA\": 59, \"direction_tb\": 60, \"direction_bt\": 61, \"direction_rl\": 62, \"direction_lr\": 63, \"relationType\": 64, \"lineType\": 65, \"AGGREGATION\": 66, \"EXTENSION\": 67, \"COMPOSITION\": 68, \"DEPENDENCY\": 69, \"LOLLIPOP\": 70, \"LINE\": 71, \"DOTTED_LINE\": 72, \"CALLBACK\": 73, \"LINK\": 74, \"LINK_TARGET\": 75, \"CLICK\": 76, \"CALLBACK_NAME\": 77, \"CALLBACK_ARGS\": 78, \"HREF\": 79, \"STYLE\": 80, \"CSSCLASS\": 81, \"style\": 82, \"styleComponent\": 83, \"NUM\": 84, \"COLON\": 85, \"UNIT\": 86, \"SPACE\": 87, \"BRKT\": 88, \"PCT\": 89, \"commentToken\": 90, \"textToken\": 91, \"graphCodeTokens\": 92, \"textNoTagsToken\": 93, \"TAGSTART\": 94, \"TAGEND\": 95, \"==\": 96, \"--\": 97, \"DEFAULT\": 98, \"MINUS\": 99, \"keywords\": 100, \"UNICODE_TEXT\": 101, \"BQUOTE_STR\": 102, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 7: \"CLASS_DIAGRAM\", 8: \"NEWLINE\", 9: \"EOF\", 12: \"SQS\", 13: \"STR\", 14: \"SQE\", 17: \"DOT\", 20: \"GENERICTYPE\", 22: \"LABEL\", 33: \"acc_title\", 34: \"acc_title_value\", 35: \"acc_descr\", 36: \"acc_descr_value\", 37: \"acc_descr_multiline_value\", 39: \"STRUCT_START\", 41: \"STRUCT_STOP\", 42: \"NAMESPACE\", 44: \"STYLE_SEPARATOR\", 46: \"CLASS\", 47: \"ANNOTATION_START\", 48: \"ANNOTATION_END\", 49: \"MEMBER\", 50: \"SEPARATOR\", 52: \"NOTE_FOR\", 54: \"NOTE\", 55: \"CLASSDEF\", 58: \"ALPHA\", 59: \"COMMA\", 60: \"direction_tb\", 61: \"direction_bt\", 62: \"direction_rl\", 63: \"direction_lr\", 66: \"AGGREGATION\", 67: \"EXTENSION\", 68: \"COMPOSITION\", 69: \"DEPENDENCY\", 70: \"LOLLIPOP\", 71: \"LINE\", 72: \"DOTTED_LINE\", 73: \"CALLBACK\", 74: \"LINK\", 75: \"LINK_TARGET\", 76: \"CLICK\", 77: \"CALLBACK_NAME\", 78: \"CALLBACK_ARGS\", 79: \"HREF\", 80: \"STYLE\", 81: \"CSSCLASS\", 84: \"NUM\", 85: \"COLON\", 86: \"UNIT\", 87: \"SPACE\", 88: \"BRKT\", 89: \"PCT\", 92: \"graphCodeTokens\", 94: \"TAGSTART\", 95: \"TAGEND\", 96: \"==\", 97: \"--\", 98: \"DEFAULT\", 99: \"MINUS\", 100: \"keywords\", 101: \"UNICODE_TEXT\", 102: \"BQUOTE_STR\" },\n    productions_: [0, [3, 1], [3, 1], [4, 1], [6, 4], [5, 1], [5, 2], [5, 3], [11, 3], [15, 1], [15, 3], [15, 2], [18, 1], [18, 3], [18, 1], [18, 2], [18, 2], [18, 2], [10, 1], [10, 2], [10, 1], [10, 1], [10, 1], [10, 1], [10, 1], [10, 1], [10, 1], [10, 1], [10, 1], [10, 1], [10, 2], [10, 2], [10, 1], [23, 4], [23, 5], [38, 2], [40, 1], [40, 2], [40, 3], [24, 1], [24, 3], [24, 4], [24, 6], [43, 2], [43, 3], [26, 4], [45, 1], [45, 2], [25, 1], [25, 2], [25, 1], [25, 1], [21, 3], [21, 4], [21, 4], [21, 5], [30, 3], [30, 2], [31, 3], [56, 1], [56, 3], [32, 1], [32, 1], [32, 1], [32, 1], [51, 3], [51, 2], [51, 2], [51, 1], [64, 1], [64, 1], [64, 1], [64, 1], [64, 1], [65, 1], [65, 1], [27, 3], [27, 4], [27, 3], [27, 4], [27, 4], [27, 5], [27, 3], [27, 4], [27, 4], [27, 5], [27, 4], [27, 5], [27, 5], [27, 6], [28, 3], [29, 3], [57, 1], [57, 3], [82, 1], [82, 2], [83, 1], [83, 1], [83, 1], [83, 1], [83, 1], [83, 1], [83, 1], [83, 1], [83, 1], [90, 1], [90, 1], [91, 1], [91, 1], [91, 1], [91, 1], [91, 1], [91, 1], [91, 1], [93, 1], [93, 1], [93, 1], [93, 1], [16, 1], [16, 1], [16, 1], [16, 1], [19, 1], [53, 1]],\n    performAction: /* @__PURE__ */ __name(function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 8:\n          this.$ = $$[$0 - 1];\n          break;\n        case 9:\n        case 12:\n        case 14:\n          this.$ = $$[$0];\n          break;\n        case 10:\n        case 13:\n          this.$ = $$[$0 - 2] + \".\" + $$[$0];\n          break;\n        case 11:\n        case 15:\n          this.$ = $$[$0 - 1] + $$[$0];\n          break;\n        case 16:\n        case 17:\n          this.$ = $$[$0 - 1] + \"~\" + $$[$0] + \"~\";\n          break;\n        case 18:\n          yy.addRelation($$[$0]);\n          break;\n        case 19:\n          $$[$0 - 1].title = yy.cleanupLabel($$[$0]);\n          yy.addRelation($$[$0 - 1]);\n          break;\n        case 30:\n          this.$ = $$[$0].trim();\n          yy.setAccTitle(this.$);\n          break;\n        case 31:\n        case 32:\n          this.$ = $$[$0].trim();\n          yy.setAccDescription(this.$);\n          break;\n        case 33:\n          yy.addClassesToNamespace($$[$0 - 3], $$[$0 - 1]);\n          break;\n        case 34:\n          yy.addClassesToNamespace($$[$0 - 4], $$[$0 - 1]);\n          break;\n        case 35:\n          this.$ = $$[$0];\n          yy.addNamespace($$[$0]);\n          break;\n        case 36:\n          this.$ = [$$[$0]];\n          break;\n        case 37:\n          this.$ = [$$[$0 - 1]];\n          break;\n        case 38:\n          $$[$0].unshift($$[$0 - 2]);\n          this.$ = $$[$0];\n          break;\n        case 40:\n          yy.setCssClass($$[$0 - 2], $$[$0]);\n          break;\n        case 41:\n          yy.addMembers($$[$0 - 3], $$[$0 - 1]);\n          break;\n        case 42:\n          yy.setCssClass($$[$0 - 5], $$[$0 - 3]);\n          yy.addMembers($$[$0 - 5], $$[$0 - 1]);\n          break;\n        case 43:\n          this.$ = $$[$0];\n          yy.addClass($$[$0]);\n          break;\n        case 44:\n          this.$ = $$[$0 - 1];\n          yy.addClass($$[$0 - 1]);\n          yy.setClassLabel($$[$0 - 1], $$[$0]);\n          break;\n        case 45:\n          yy.addAnnotation($$[$0], $$[$0 - 2]);\n          break;\n        case 46:\n        case 59:\n          this.$ = [$$[$0]];\n          break;\n        case 47:\n          $$[$0].push($$[$0 - 1]);\n          this.$ = $$[$0];\n          break;\n        case 48:\n          break;\n        case 49:\n          yy.addMember($$[$0 - 1], yy.cleanupLabel($$[$0]));\n          break;\n        case 50:\n          break;\n        case 51:\n          break;\n        case 52:\n          this.$ = { \"id1\": $$[$0 - 2], \"id2\": $$[$0], relation: $$[$0 - 1], relationTitle1: \"none\", relationTitle2: \"none\" };\n          break;\n        case 53:\n          this.$ = { id1: $$[$0 - 3], id2: $$[$0], relation: $$[$0 - 1], relationTitle1: $$[$0 - 2], relationTitle2: \"none\" };\n          break;\n        case 54:\n          this.$ = { id1: $$[$0 - 3], id2: $$[$0], relation: $$[$0 - 2], relationTitle1: \"none\", relationTitle2: $$[$0 - 1] };\n          break;\n        case 55:\n          this.$ = { id1: $$[$0 - 4], id2: $$[$0], relation: $$[$0 - 2], relationTitle1: $$[$0 - 3], relationTitle2: $$[$0 - 1] };\n          break;\n        case 56:\n          yy.addNote($$[$0], $$[$0 - 1]);\n          break;\n        case 57:\n          yy.addNote($$[$0]);\n          break;\n        case 58:\n          this.$ = $$[$0 - 2];\n          yy.defineClass($$[$0 - 1], $$[$0]);\n          break;\n        case 60:\n          this.$ = $$[$0 - 2].concat([$$[$0]]);\n          break;\n        case 61:\n          yy.setDirection(\"TB\");\n          break;\n        case 62:\n          yy.setDirection(\"BT\");\n          break;\n        case 63:\n          yy.setDirection(\"RL\");\n          break;\n        case 64:\n          yy.setDirection(\"LR\");\n          break;\n        case 65:\n          this.$ = { type1: $$[$0 - 2], type2: $$[$0], lineType: $$[$0 - 1] };\n          break;\n        case 66:\n          this.$ = { type1: \"none\", type2: $$[$0], lineType: $$[$0 - 1] };\n          break;\n        case 67:\n          this.$ = { type1: $$[$0 - 1], type2: \"none\", lineType: $$[$0] };\n          break;\n        case 68:\n          this.$ = { type1: \"none\", type2: \"none\", lineType: $$[$0] };\n          break;\n        case 69:\n          this.$ = yy.relationType.AGGREGATION;\n          break;\n        case 70:\n          this.$ = yy.relationType.EXTENSION;\n          break;\n        case 71:\n          this.$ = yy.relationType.COMPOSITION;\n          break;\n        case 72:\n          this.$ = yy.relationType.DEPENDENCY;\n          break;\n        case 73:\n          this.$ = yy.relationType.LOLLIPOP;\n          break;\n        case 74:\n          this.$ = yy.lineType.LINE;\n          break;\n        case 75:\n          this.$ = yy.lineType.DOTTED_LINE;\n          break;\n        case 76:\n        case 82:\n          this.$ = $$[$0 - 2];\n          yy.setClickEvent($$[$0 - 1], $$[$0]);\n          break;\n        case 77:\n        case 83:\n          this.$ = $$[$0 - 3];\n          yy.setClickEvent($$[$0 - 2], $$[$0 - 1]);\n          yy.setTooltip($$[$0 - 2], $$[$0]);\n          break;\n        case 78:\n          this.$ = $$[$0 - 2];\n          yy.setLink($$[$0 - 1], $$[$0]);\n          break;\n        case 79:\n          this.$ = $$[$0 - 3];\n          yy.setLink($$[$0 - 2], $$[$0 - 1], $$[$0]);\n          break;\n        case 80:\n          this.$ = $$[$0 - 3];\n          yy.setLink($$[$0 - 2], $$[$0 - 1]);\n          yy.setTooltip($$[$0 - 2], $$[$0]);\n          break;\n        case 81:\n          this.$ = $$[$0 - 4];\n          yy.setLink($$[$0 - 3], $$[$0 - 2], $$[$0]);\n          yy.setTooltip($$[$0 - 3], $$[$0 - 1]);\n          break;\n        case 84:\n          this.$ = $$[$0 - 3];\n          yy.setClickEvent($$[$0 - 2], $$[$0 - 1], $$[$0]);\n          break;\n        case 85:\n          this.$ = $$[$0 - 4];\n          yy.setClickEvent($$[$0 - 3], $$[$0 - 2], $$[$0 - 1]);\n          yy.setTooltip($$[$0 - 3], $$[$0]);\n          break;\n        case 86:\n          this.$ = $$[$0 - 3];\n          yy.setLink($$[$0 - 2], $$[$0]);\n          break;\n        case 87:\n          this.$ = $$[$0 - 4];\n          yy.setLink($$[$0 - 3], $$[$0 - 1], $$[$0]);\n          break;\n        case 88:\n          this.$ = $$[$0 - 4];\n          yy.setLink($$[$0 - 3], $$[$0 - 1]);\n          yy.setTooltip($$[$0 - 3], $$[$0]);\n          break;\n        case 89:\n          this.$ = $$[$0 - 5];\n          yy.setLink($$[$0 - 4], $$[$0 - 2], $$[$0]);\n          yy.setTooltip($$[$0 - 4], $$[$0 - 1]);\n          break;\n        case 90:\n          this.$ = $$[$0 - 2];\n          yy.setCssStyle($$[$0 - 1], $$[$0]);\n          break;\n        case 91:\n          yy.setCssClass($$[$0 - 1], $$[$0]);\n          break;\n        case 92:\n          this.$ = [$$[$0]];\n          break;\n        case 93:\n          $$[$0 - 2].push($$[$0]);\n          this.$ = $$[$0 - 2];\n          break;\n        case 95:\n          this.$ = $$[$0 - 1] + $$[$0];\n          break;\n      }\n    }, \"anonymous\"),\n    table: [{ 3: 1, 4: 2, 5: 3, 6: 4, 7: [1, 6], 10: 5, 16: 39, 18: 21, 19: 40, 21: 7, 23: 8, 24: 9, 25: 10, 26: 11, 27: 12, 28: 13, 29: 14, 30: 15, 31: 16, 32: 17, 33: $V0, 35: $V1, 37: $V2, 38: 22, 42: $V3, 43: 23, 46: $V4, 47: $V5, 49: $V6, 50: $V7, 52: $V8, 54: $V9, 55: $Va, 58: $Vb, 60: $Vc, 61: $Vd, 62: $Ve, 63: $Vf, 73: $Vg, 74: $Vh, 76: $Vi, 80: $Vj, 81: $Vk, 84: $Vl, 99: $Vm, 101: $Vn, 102: $Vo }, { 1: [3] }, { 1: [2, 1] }, { 1: [2, 2] }, { 1: [2, 3] }, o($Vp, [2, 5], { 8: [1, 48] }), { 8: [1, 49] }, o($Vq, [2, 18], { 22: [1, 50] }), o($Vq, [2, 20]), o($Vq, [2, 21]), o($Vq, [2, 22]), o($Vq, [2, 23]), o($Vq, [2, 24]), o($Vq, [2, 25]), o($Vq, [2, 26]), o($Vq, [2, 27]), o($Vq, [2, 28]), o($Vq, [2, 29]), { 34: [1, 51] }, { 36: [1, 52] }, o($Vq, [2, 32]), o($Vq, [2, 48], { 51: 53, 64: 56, 65: 57, 13: [1, 54], 22: [1, 55], 66: $Vr, 67: $Vs, 68: $Vt, 69: $Vu, 70: $Vv, 71: $Vw, 72: $Vx }), { 39: [1, 65] }, o($Vy, [2, 39], { 39: [1, 67], 44: [1, 66] }), o($Vq, [2, 50]), o($Vq, [2, 51]), { 16: 68, 58: $Vb, 84: $Vl, 99: $Vm, 101: $Vn }, { 16: 39, 18: 69, 19: 40, 58: $Vb, 84: $Vl, 99: $Vm, 101: $Vn, 102: $Vo }, { 16: 39, 18: 70, 19: 40, 58: $Vb, 84: $Vl, 99: $Vm, 101: $Vn, 102: $Vo }, { 16: 39, 18: 71, 19: 40, 58: $Vb, 84: $Vl, 99: $Vm, 101: $Vn, 102: $Vo }, { 58: [1, 72] }, { 13: [1, 73] }, { 16: 39, 18: 74, 19: 40, 58: $Vb, 84: $Vl, 99: $Vm, 101: $Vn, 102: $Vo }, { 13: $Vz, 53: 75 }, { 56: 77, 58: [1, 78] }, o($Vq, [2, 61]), o($Vq, [2, 62]), o($Vq, [2, 63]), o($Vq, [2, 64]), o($VA, [2, 12], { 16: 39, 19: 40, 18: 80, 17: [1, 79], 20: [1, 81], 58: $Vb, 84: $Vl, 99: $Vm, 101: $Vn, 102: $Vo }), o($VA, [2, 14], { 20: [1, 82] }), { 15: 83, 16: 84, 58: $Vb, 84: $Vl, 99: $Vm, 101: $Vn }, { 16: 39, 18: 85, 19: 40, 58: $Vb, 84: $Vl, 99: $Vm, 101: $Vn, 102: $Vo }, o($VB, [2, 118]), o($VB, [2, 119]), o($VB, [2, 120]), o($VB, [2, 121]), o([1, 8, 9, 12, 13, 20, 22, 39, 41, 44, 66, 67, 68, 69, 70, 71, 72, 77, 79], [2, 122]), o($Vp, [2, 6], { 10: 5, 21: 7, 23: 8, 24: 9, 25: 10, 26: 11, 27: 12, 28: 13, 29: 14, 30: 15, 31: 16, 32: 17, 18: 21, 38: 22, 43: 23, 16: 39, 19: 40, 5: 86, 33: $V0, 35: $V1, 37: $V2, 42: $V3, 46: $V4, 47: $V5, 49: $V6, 50: $V7, 52: $V8, 54: $V9, 55: $Va, 58: $Vb, 60: $Vc, 61: $Vd, 62: $Ve, 63: $Vf, 73: $Vg, 74: $Vh, 76: $Vi, 80: $Vj, 81: $Vk, 84: $Vl, 99: $Vm, 101: $Vn, 102: $Vo }), { 5: 87, 10: 5, 16: 39, 18: 21, 19: 40, 21: 7, 23: 8, 24: 9, 25: 10, 26: 11, 27: 12, 28: 13, 29: 14, 30: 15, 31: 16, 32: 17, 33: $V0, 35: $V1, 37: $V2, 38: 22, 42: $V3, 43: 23, 46: $V4, 47: $V5, 49: $V6, 50: $V7, 52: $V8, 54: $V9, 55: $Va, 58: $Vb, 60: $Vc, 61: $Vd, 62: $Ve, 63: $Vf, 73: $Vg, 74: $Vh, 76: $Vi, 80: $Vj, 81: $Vk, 84: $Vl, 99: $Vm, 101: $Vn, 102: $Vo }, o($Vq, [2, 19]), o($Vq, [2, 30]), o($Vq, [2, 31]), { 13: [1, 89], 16: 39, 18: 88, 19: 40, 58: $Vb, 84: $Vl, 99: $Vm, 101: $Vn, 102: $Vo }, { 51: 90, 64: 56, 65: 57, 66: $Vr, 67: $Vs, 68: $Vt, 69: $Vu, 70: $Vv, 71: $Vw, 72: $Vx }, o($Vq, [2, 49]), { 65: 91, 71: $Vw, 72: $Vx }, o($VC, [2, 68], { 64: 92, 66: $Vr, 67: $Vs, 68: $Vt, 69: $Vu, 70: $Vv }), o($VD, [2, 69]), o($VD, [2, 70]), o($VD, [2, 71]), o($VD, [2, 72]), o($VD, [2, 73]), o($VE, [2, 74]), o($VE, [2, 75]), { 8: [1, 94], 24: 95, 40: 93, 43: 23, 46: $V4 }, { 16: 96, 58: $Vb, 84: $Vl, 99: $Vm, 101: $Vn }, { 45: 97, 49: $VF }, { 48: [1, 99] }, { 13: [1, 100] }, { 13: [1, 101] }, { 77: [1, 102], 79: [1, 103] }, { 22: $VG, 57: 104, 58: $VH, 80: $VI, 82: 105, 83: 106, 84: $VJ, 85: $VK, 86: $VL, 87: $VM, 88: $VN, 89: $VO }, { 58: [1, 116] }, { 13: $Vz, 53: 117 }, o($Vq, [2, 57]), o($Vq, [2, 123]), { 22: $VG, 57: 118, 58: $VH, 59: [1, 119], 80: $VI, 82: 105, 83: 106, 84: $VJ, 85: $VK, 86: $VL, 87: $VM, 88: $VN, 89: $VO }, o($VP, [2, 59]), { 16: 39, 18: 120, 19: 40, 58: $Vb, 84: $Vl, 99: $Vm, 101: $Vn, 102: $Vo }, o($VA, [2, 15]), o($VA, [2, 16]), o($VA, [2, 17]), { 39: [2, 35] }, { 15: 122, 16: 84, 17: [1, 121], 39: [2, 9], 58: $Vb, 84: $Vl, 99: $Vm, 101: $Vn }, o($VQ, [2, 43], { 11: 123, 12: [1, 124] }), o($Vp, [2, 7]), { 9: [1, 125] }, o($VR, [2, 52]), { 16: 39, 18: 126, 19: 40, 58: $Vb, 84: $Vl, 99: $Vm, 101: $Vn, 102: $Vo }, { 13: [1, 128], 16: 39, 18: 127, 19: 40, 58: $Vb, 84: $Vl, 99: $Vm, 101: $Vn, 102: $Vo }, o($VC, [2, 67], { 64: 129, 66: $Vr, 67: $Vs, 68: $Vt, 69: $Vu, 70: $Vv }), o($VC, [2, 66]), { 41: [1, 130] }, { 24: 95, 40: 131, 43: 23, 46: $V4 }, { 8: [1, 132], 41: [2, 36] }, o($Vy, [2, 40], { 39: [1, 133] }), { 41: [1, 134] }, { 41: [2, 46], 45: 135, 49: $VF }, { 16: 39, 18: 136, 19: 40, 58: $Vb, 84: $Vl, 99: $Vm, 101: $Vn, 102: $Vo }, o($Vq, [2, 76], { 13: [1, 137] }), o($Vq, [2, 78], { 13: [1, 139], 75: [1, 138] }), o($Vq, [2, 82], { 13: [1, 140], 78: [1, 141] }), { 13: [1, 142] }, o($Vq, [2, 90], { 59: $VS }), o($VT, [2, 92], { 83: 144, 22: $VG, 58: $VH, 80: $VI, 84: $VJ, 85: $VK, 86: $VL, 87: $VM, 88: $VN, 89: $VO }), o($VU, [2, 94]), o($VU, [2, 96]), o($VU, [2, 97]), o($VU, [2, 98]), o($VU, [2, 99]), o($VU, [2, 100]), o($VU, [2, 101]), o($VU, [2, 102]), o($VU, [2, 103]), o($VU, [2, 104]), o($Vq, [2, 91]), o($Vq, [2, 56]), o($Vq, [2, 58], { 59: $VS }), { 58: [1, 145] }, o($VA, [2, 13]), { 15: 146, 16: 84, 58: $Vb, 84: $Vl, 99: $Vm, 101: $Vn }, { 39: [2, 11] }, o($VQ, [2, 44]), { 13: [1, 147] }, { 1: [2, 4] }, o($VR, [2, 54]), o($VR, [2, 53]), { 16: 39, 18: 148, 19: 40, 58: $Vb, 84: $Vl, 99: $Vm, 101: $Vn, 102: $Vo }, o($VC, [2, 65]), o($Vq, [2, 33]), { 41: [1, 149] }, { 24: 95, 40: 150, 41: [2, 37], 43: 23, 46: $V4 }, { 45: 151, 49: $VF }, o($Vy, [2, 41]), { 41: [2, 47] }, o($Vq, [2, 45]), o($Vq, [2, 77]), o($Vq, [2, 79]), o($Vq, [2, 80], { 75: [1, 152] }), o($Vq, [2, 83]), o($Vq, [2, 84], { 13: [1, 153] }), o($Vq, [2, 86], { 13: [1, 155], 75: [1, 154] }), { 22: $VG, 58: $VH, 80: $VI, 82: 156, 83: 106, 84: $VJ, 85: $VK, 86: $VL, 87: $VM, 88: $VN, 89: $VO }, o($VU, [2, 95]), o($VP, [2, 60]), { 39: [2, 10] }, { 14: [1, 157] }, o($VR, [2, 55]), o($Vq, [2, 34]), { 41: [2, 38] }, { 41: [1, 158] }, o($Vq, [2, 81]), o($Vq, [2, 85]), o($Vq, [2, 87]), o($Vq, [2, 88], { 75: [1, 159] }), o($VT, [2, 93], { 83: 144, 22: $VG, 58: $VH, 80: $VI, 84: $VJ, 85: $VK, 86: $VL, 87: $VM, 88: $VN, 89: $VO }), o($VQ, [2, 8]), o($Vy, [2, 42]), o($Vq, [2, 89])],\n    defaultActions: { 2: [2, 1], 3: [2, 2], 4: [2, 3], 83: [2, 35], 122: [2, 11], 125: [2, 4], 135: [2, 47], 146: [2, 10], 150: [2, 38] },\n    parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    }, \"parseError\"),\n    parse: /* @__PURE__ */ __name(function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n      }\n      __name(popStack, \"popStack\");\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      __name(lex, \"lex\");\n      var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n              if (recovering > 0) {\n                recovering--;\n              }\n            } else {\n              symbol = preErrorSymbol;\n              preErrorSymbol = null;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }, \"parse\")\n  };\n  var lexer = /* @__PURE__ */ function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      }, \"parseError\"),\n      // resets the lexer, sets new input\n      setInput: /* @__PURE__ */ __name(function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      }, \"setInput\"),\n      // consumes and returns one char from the input\n      input: /* @__PURE__ */ __name(function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      }, \"input\"),\n      // unshifts one char (or a string) into the input\n      unput: /* @__PURE__ */ __name(function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      }, \"unput\"),\n      // When called from action, caches matched text and appends it on next action\n      more: /* @__PURE__ */ __name(function() {\n        this._more = true;\n        return this;\n      }, \"more\"),\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: /* @__PURE__ */ __name(function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      }, \"reject\"),\n      // retain first n characters of the match\n      less: /* @__PURE__ */ __name(function(n) {\n        this.unput(this.match.slice(n));\n      }, \"less\"),\n      // displays already matched input, i.e. for error messages\n      pastInput: /* @__PURE__ */ __name(function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      }, \"pastInput\"),\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: /* @__PURE__ */ __name(function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      }, \"upcomingInput\"),\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: /* @__PURE__ */ __name(function() {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n      }, \"showPosition\"),\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: /* @__PURE__ */ __name(function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      }, \"test_match\"),\n      // return next match in input\n      next: /* @__PURE__ */ __name(function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      }, \"next\"),\n      // return next match that has a token\n      lex: /* @__PURE__ */ __name(function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      }, \"lex\"),\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: /* @__PURE__ */ __name(function begin(condition) {\n        this.conditionStack.push(condition);\n      }, \"begin\"),\n      // pop the previously active lexer condition state off the condition stack\n      popState: /* @__PURE__ */ __name(function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      }, \"popState\"),\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: /* @__PURE__ */ __name(function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      }, \"_currentRules\"),\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: /* @__PURE__ */ __name(function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      }, \"topState\"),\n      // alias for begin(condition)\n      pushState: /* @__PURE__ */ __name(function pushState(condition) {\n        this.begin(condition);\n      }, \"pushState\"),\n      // return the number of states currently on the stack\n      stateStackSize: /* @__PURE__ */ __name(function stateStackSize() {\n        return this.conditionStack.length;\n      }, \"stateStackSize\"),\n      options: {},\n      performAction: /* @__PURE__ */ __name(function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        var YYSTATE = YY_START;\n        switch ($avoiding_name_collisions) {\n          case 0:\n            return 60;\n            break;\n          case 1:\n            return 61;\n            break;\n          case 2:\n            return 62;\n            break;\n          case 3:\n            return 63;\n            break;\n          case 4:\n            break;\n          case 5:\n            break;\n          case 6:\n            this.begin(\"acc_title\");\n            return 33;\n            break;\n          case 7:\n            this.popState();\n            return \"acc_title_value\";\n            break;\n          case 8:\n            this.begin(\"acc_descr\");\n            return 35;\n            break;\n          case 9:\n            this.popState();\n            return \"acc_descr_value\";\n            break;\n          case 10:\n            this.begin(\"acc_descr_multiline\");\n            break;\n          case 11:\n            this.popState();\n            break;\n          case 12:\n            return \"acc_descr_multiline_value\";\n            break;\n          case 13:\n            return 8;\n            break;\n          case 14:\n            break;\n          case 15:\n            return 7;\n            break;\n          case 16:\n            return 7;\n            break;\n          case 17:\n            return \"EDGE_STATE\";\n            break;\n          case 18:\n            this.begin(\"callback_name\");\n            break;\n          case 19:\n            this.popState();\n            break;\n          case 20:\n            this.popState();\n            this.begin(\"callback_args\");\n            break;\n          case 21:\n            return 77;\n            break;\n          case 22:\n            this.popState();\n            break;\n          case 23:\n            return 78;\n            break;\n          case 24:\n            this.popState();\n            break;\n          case 25:\n            return \"STR\";\n            break;\n          case 26:\n            this.begin(\"string\");\n            break;\n          case 27:\n            return 80;\n            break;\n          case 28:\n            return 55;\n            break;\n          case 29:\n            this.begin(\"namespace\");\n            return 42;\n            break;\n          case 30:\n            this.popState();\n            return 8;\n            break;\n          case 31:\n            break;\n          case 32:\n            this.begin(\"namespace-body\");\n            return 39;\n            break;\n          case 33:\n            this.popState();\n            return 41;\n            break;\n          case 34:\n            return \"EOF_IN_STRUCT\";\n            break;\n          case 35:\n            return 8;\n            break;\n          case 36:\n            break;\n          case 37:\n            return \"EDGE_STATE\";\n            break;\n          case 38:\n            this.begin(\"class\");\n            return 46;\n            break;\n          case 39:\n            this.popState();\n            return 8;\n            break;\n          case 40:\n            break;\n          case 41:\n            this.popState();\n            this.popState();\n            return 41;\n            break;\n          case 42:\n            this.begin(\"class-body\");\n            return 39;\n            break;\n          case 43:\n            this.popState();\n            return 41;\n            break;\n          case 44:\n            return \"EOF_IN_STRUCT\";\n            break;\n          case 45:\n            return \"EDGE_STATE\";\n            break;\n          case 46:\n            return \"OPEN_IN_STRUCT\";\n            break;\n          case 47:\n            break;\n          case 48:\n            return \"MEMBER\";\n            break;\n          case 49:\n            return 81;\n            break;\n          case 50:\n            return 73;\n            break;\n          case 51:\n            return 74;\n            break;\n          case 52:\n            return 76;\n            break;\n          case 53:\n            return 52;\n            break;\n          case 54:\n            return 54;\n            break;\n          case 55:\n            return 47;\n            break;\n          case 56:\n            return 48;\n            break;\n          case 57:\n            return 79;\n            break;\n          case 58:\n            this.popState();\n            break;\n          case 59:\n            return \"GENERICTYPE\";\n            break;\n          case 60:\n            this.begin(\"generic\");\n            break;\n          case 61:\n            this.popState();\n            break;\n          case 62:\n            return \"BQUOTE_STR\";\n            break;\n          case 63:\n            this.begin(\"bqstring\");\n            break;\n          case 64:\n            return 75;\n            break;\n          case 65:\n            return 75;\n            break;\n          case 66:\n            return 75;\n            break;\n          case 67:\n            return 75;\n            break;\n          case 68:\n            return 67;\n            break;\n          case 69:\n            return 67;\n            break;\n          case 70:\n            return 69;\n            break;\n          case 71:\n            return 69;\n            break;\n          case 72:\n            return 68;\n            break;\n          case 73:\n            return 66;\n            break;\n          case 74:\n            return 70;\n            break;\n          case 75:\n            return 71;\n            break;\n          case 76:\n            return 72;\n            break;\n          case 77:\n            return 22;\n            break;\n          case 78:\n            return 44;\n            break;\n          case 79:\n            return 99;\n            break;\n          case 80:\n            return 17;\n            break;\n          case 81:\n            return \"PLUS\";\n            break;\n          case 82:\n            return 85;\n            break;\n          case 83:\n            return 59;\n            break;\n          case 84:\n            return 88;\n            break;\n          case 85:\n            return 88;\n            break;\n          case 86:\n            return 89;\n            break;\n          case 87:\n            return \"EQUALS\";\n            break;\n          case 88:\n            return \"EQUALS\";\n            break;\n          case 89:\n            return 58;\n            break;\n          case 90:\n            return 12;\n            break;\n          case 91:\n            return 14;\n            break;\n          case 92:\n            return \"PUNCTUATION\";\n            break;\n          case 93:\n            return 84;\n            break;\n          case 94:\n            return 101;\n            break;\n          case 95:\n            return 87;\n            break;\n          case 96:\n            return 87;\n            break;\n          case 97:\n            return 9;\n            break;\n        }\n      }, \"anonymous\"),\n      rules: [/^(?:.*direction\\s+TB[^\\n]*)/, /^(?:.*direction\\s+BT[^\\n]*)/, /^(?:.*direction\\s+RL[^\\n]*)/, /^(?:.*direction\\s+LR[^\\n]*)/, /^(?:%%(?!\\{)*[^\\n]*(\\r?\\n?)+)/, /^(?:%%[^\\n]*(\\r?\\n)*)/, /^(?:accTitle\\s*:\\s*)/, /^(?:(?!\\n||)*[^\\n]*)/, /^(?:accDescr\\s*:\\s*)/, /^(?:(?!\\n||)*[^\\n]*)/, /^(?:accDescr\\s*\\{\\s*)/, /^(?:[\\}])/, /^(?:[^\\}]*)/, /^(?:\\s*(\\r?\\n)+)/, /^(?:\\s+)/, /^(?:classDiagram-v2\\b)/, /^(?:classDiagram\\b)/, /^(?:\\[\\*\\])/, /^(?:call[\\s]+)/, /^(?:\\([\\s]*\\))/, /^(?:\\()/, /^(?:[^(]*)/, /^(?:\\))/, /^(?:[^)]*)/, /^(?:[\"])/, /^(?:[^\"]*)/, /^(?:[\"])/, /^(?:style\\b)/, /^(?:classDef\\b)/, /^(?:namespace\\b)/, /^(?:\\s*(\\r?\\n)+)/, /^(?:\\s+)/, /^(?:[{])/, /^(?:[}])/, /^(?:$)/, /^(?:\\s*(\\r?\\n)+)/, /^(?:\\s+)/, /^(?:\\[\\*\\])/, /^(?:class\\b)/, /^(?:\\s*(\\r?\\n)+)/, /^(?:\\s+)/, /^(?:[}])/, /^(?:[{])/, /^(?:[}])/, /^(?:$)/, /^(?:\\[\\*\\])/, /^(?:[{])/, /^(?:[\\n])/, /^(?:[^{}\\n]*)/, /^(?:cssClass\\b)/, /^(?:callback\\b)/, /^(?:link\\b)/, /^(?:click\\b)/, /^(?:note for\\b)/, /^(?:note\\b)/, /^(?:<<)/, /^(?:>>)/, /^(?:href\\b)/, /^(?:[~])/, /^(?:[^~]*)/, /^(?:~)/, /^(?:[`])/, /^(?:[^`]+)/, /^(?:[`])/, /^(?:_self\\b)/, /^(?:_blank\\b)/, /^(?:_parent\\b)/, /^(?:_top\\b)/, /^(?:\\s*<\\|)/, /^(?:\\s*\\|>)/, /^(?:\\s*>)/, /^(?:\\s*<)/, /^(?:\\s*\\*)/, /^(?:\\s*o\\b)/, /^(?:\\s*\\(\\))/, /^(?:--)/, /^(?:\\.\\.)/, /^(?::{1}[^:\\n;]+)/, /^(?::{3})/, /^(?:-)/, /^(?:\\.)/, /^(?:\\+)/, /^(?::)/, /^(?:,)/, /^(?:#)/, /^(?:#)/, /^(?:%)/, /^(?:=)/, /^(?:=)/, /^(?:\\w+)/, /^(?:\\[)/, /^(?:\\])/, /^(?:[!\"#$%&'*+,-.`?\\\\/])/, /^(?:[0-9]+)/, /^(?:[\\u00AA\\u00B5\\u00BA\\u00C0-\\u00D6\\u00D8-\\u00F6]|[\\u00F8-\\u02C1\\u02C6-\\u02D1\\u02E0-\\u02E4\\u02EC\\u02EE\\u0370-\\u0374\\u0376\\u0377]|[\\u037A-\\u037D\\u0386\\u0388-\\u038A\\u038C\\u038E-\\u03A1\\u03A3-\\u03F5]|[\\u03F7-\\u0481\\u048A-\\u0527\\u0531-\\u0556\\u0559\\u0561-\\u0587\\u05D0-\\u05EA]|[\\u05F0-\\u05F2\\u0620-\\u064A\\u066E\\u066F\\u0671-\\u06D3\\u06D5\\u06E5\\u06E6\\u06EE]|[\\u06EF\\u06FA-\\u06FC\\u06FF\\u0710\\u0712-\\u072F\\u074D-\\u07A5\\u07B1\\u07CA-\\u07EA]|[\\u07F4\\u07F5\\u07FA\\u0800-\\u0815\\u081A\\u0824\\u0828\\u0840-\\u0858\\u08A0]|[\\u08A2-\\u08AC\\u0904-\\u0939\\u093D\\u0950\\u0958-\\u0961\\u0971-\\u0977]|[\\u0979-\\u097F\\u0985-\\u098C\\u098F\\u0990\\u0993-\\u09A8\\u09AA-\\u09B0\\u09B2]|[\\u09B6-\\u09B9\\u09BD\\u09CE\\u09DC\\u09DD\\u09DF-\\u09E1\\u09F0\\u09F1\\u0A05-\\u0A0A]|[\\u0A0F\\u0A10\\u0A13-\\u0A28\\u0A2A-\\u0A30\\u0A32\\u0A33\\u0A35\\u0A36\\u0A38\\u0A39]|[\\u0A59-\\u0A5C\\u0A5E\\u0A72-\\u0A74\\u0A85-\\u0A8D\\u0A8F-\\u0A91\\u0A93-\\u0AA8]|[\\u0AAA-\\u0AB0\\u0AB2\\u0AB3\\u0AB5-\\u0AB9\\u0ABD\\u0AD0\\u0AE0\\u0AE1\\u0B05-\\u0B0C]|[\\u0B0F\\u0B10\\u0B13-\\u0B28\\u0B2A-\\u0B30\\u0B32\\u0B33\\u0B35-\\u0B39\\u0B3D\\u0B5C]|[\\u0B5D\\u0B5F-\\u0B61\\u0B71\\u0B83\\u0B85-\\u0B8A\\u0B8E-\\u0B90\\u0B92-\\u0B95\\u0B99]|[\\u0B9A\\u0B9C\\u0B9E\\u0B9F\\u0BA3\\u0BA4\\u0BA8-\\u0BAA\\u0BAE-\\u0BB9\\u0BD0]|[\\u0C05-\\u0C0C\\u0C0E-\\u0C10\\u0C12-\\u0C28\\u0C2A-\\u0C33\\u0C35-\\u0C39\\u0C3D]|[\\u0C58\\u0C59\\u0C60\\u0C61\\u0C85-\\u0C8C\\u0C8E-\\u0C90\\u0C92-\\u0CA8\\u0CAA-\\u0CB3]|[\\u0CB5-\\u0CB9\\u0CBD\\u0CDE\\u0CE0\\u0CE1\\u0CF1\\u0CF2\\u0D05-\\u0D0C\\u0D0E-\\u0D10]|[\\u0D12-\\u0D3A\\u0D3D\\u0D4E\\u0D60\\u0D61\\u0D7A-\\u0D7F\\u0D85-\\u0D96\\u0D9A-\\u0DB1]|[\\u0DB3-\\u0DBB\\u0DBD\\u0DC0-\\u0DC6\\u0E01-\\u0E30\\u0E32\\u0E33\\u0E40-\\u0E46\\u0E81]|[\\u0E82\\u0E84\\u0E87\\u0E88\\u0E8A\\u0E8D\\u0E94-\\u0E97\\u0E99-\\u0E9F\\u0EA1-\\u0EA3]|[\\u0EA5\\u0EA7\\u0EAA\\u0EAB\\u0EAD-\\u0EB0\\u0EB2\\u0EB3\\u0EBD\\u0EC0-\\u0EC4\\u0EC6]|[\\u0EDC-\\u0EDF\\u0F00\\u0F40-\\u0F47\\u0F49-\\u0F6C\\u0F88-\\u0F8C\\u1000-\\u102A]|[\\u103F\\u1050-\\u1055\\u105A-\\u105D\\u1061\\u1065\\u1066\\u106E-\\u1070\\u1075-\\u1081]|[\\u108E\\u10A0-\\u10C5\\u10C7\\u10CD\\u10D0-\\u10FA\\u10FC-\\u1248\\u124A-\\u124D]|[\\u1250-\\u1256\\u1258\\u125A-\\u125D\\u1260-\\u1288\\u128A-\\u128D\\u1290-\\u12B0]|[\\u12B2-\\u12B5\\u12B8-\\u12BE\\u12C0\\u12C2-\\u12C5\\u12C8-\\u12D6\\u12D8-\\u1310]|[\\u1312-\\u1315\\u1318-\\u135A\\u1380-\\u138F\\u13A0-\\u13F4\\u1401-\\u166C]|[\\u166F-\\u167F\\u1681-\\u169A\\u16A0-\\u16EA\\u1700-\\u170C\\u170E-\\u1711]|[\\u1720-\\u1731\\u1740-\\u1751\\u1760-\\u176C\\u176E-\\u1770\\u1780-\\u17B3\\u17D7]|[\\u17DC\\u1820-\\u1877\\u1880-\\u18A8\\u18AA\\u18B0-\\u18F5\\u1900-\\u191C]|[\\u1950-\\u196D\\u1970-\\u1974\\u1980-\\u19AB\\u19C1-\\u19C7\\u1A00-\\u1A16]|[\\u1A20-\\u1A54\\u1AA7\\u1B05-\\u1B33\\u1B45-\\u1B4B\\u1B83-\\u1BA0\\u1BAE\\u1BAF]|[\\u1BBA-\\u1BE5\\u1C00-\\u1C23\\u1C4D-\\u1C4F\\u1C5A-\\u1C7D\\u1CE9-\\u1CEC]|[\\u1CEE-\\u1CF1\\u1CF5\\u1CF6\\u1D00-\\u1DBF\\u1E00-\\u1F15\\u1F18-\\u1F1D]|[\\u1F20-\\u1F45\\u1F48-\\u1F4D\\u1F50-\\u1F57\\u1F59\\u1F5B\\u1F5D\\u1F5F-\\u1F7D]|[\\u1F80-\\u1FB4\\u1FB6-\\u1FBC\\u1FBE\\u1FC2-\\u1FC4\\u1FC6-\\u1FCC\\u1FD0-\\u1FD3]|[\\u1FD6-\\u1FDB\\u1FE0-\\u1FEC\\u1FF2-\\u1FF4\\u1FF6-\\u1FFC\\u2071\\u207F]|[\\u2090-\\u209C\\u2102\\u2107\\u210A-\\u2113\\u2115\\u2119-\\u211D\\u2124\\u2126\\u2128]|[\\u212A-\\u212D\\u212F-\\u2139\\u213C-\\u213F\\u2145-\\u2149\\u214E\\u2183\\u2184]|[\\u2C00-\\u2C2E\\u2C30-\\u2C5E\\u2C60-\\u2CE4\\u2CEB-\\u2CEE\\u2CF2\\u2CF3]|[\\u2D00-\\u2D25\\u2D27\\u2D2D\\u2D30-\\u2D67\\u2D6F\\u2D80-\\u2D96\\u2DA0-\\u2DA6]|[\\u2DA8-\\u2DAE\\u2DB0-\\u2DB6\\u2DB8-\\u2DBE\\u2DC0-\\u2DC6\\u2DC8-\\u2DCE]|[\\u2DD0-\\u2DD6\\u2DD8-\\u2DDE\\u2E2F\\u3005\\u3006\\u3031-\\u3035\\u303B\\u303C]|[\\u3041-\\u3096\\u309D-\\u309F\\u30A1-\\u30FA\\u30FC-\\u30FF\\u3105-\\u312D]|[\\u3131-\\u318E\\u31A0-\\u31BA\\u31F0-\\u31FF\\u3400-\\u4DB5\\u4E00-\\u9FCC]|[\\uA000-\\uA48C\\uA4D0-\\uA4FD\\uA500-\\uA60C\\uA610-\\uA61F\\uA62A\\uA62B]|[\\uA640-\\uA66E\\uA67F-\\uA697\\uA6A0-\\uA6E5\\uA717-\\uA71F\\uA722-\\uA788]|[\\uA78B-\\uA78E\\uA790-\\uA793\\uA7A0-\\uA7AA\\uA7F8-\\uA801\\uA803-\\uA805]|[\\uA807-\\uA80A\\uA80C-\\uA822\\uA840-\\uA873\\uA882-\\uA8B3\\uA8F2-\\uA8F7\\uA8FB]|[\\uA90A-\\uA925\\uA930-\\uA946\\uA960-\\uA97C\\uA984-\\uA9B2\\uA9CF\\uAA00-\\uAA28]|[\\uAA40-\\uAA42\\uAA44-\\uAA4B\\uAA60-\\uAA76\\uAA7A\\uAA80-\\uAAAF\\uAAB1\\uAAB5]|[\\uAAB6\\uAAB9-\\uAABD\\uAAC0\\uAAC2\\uAADB-\\uAADD\\uAAE0-\\uAAEA\\uAAF2-\\uAAF4]|[\\uAB01-\\uAB06\\uAB09-\\uAB0E\\uAB11-\\uAB16\\uAB20-\\uAB26\\uAB28-\\uAB2E]|[\\uABC0-\\uABE2\\uAC00-\\uD7A3\\uD7B0-\\uD7C6\\uD7CB-\\uD7FB\\uF900-\\uFA6D]|[\\uFA70-\\uFAD9\\uFB00-\\uFB06\\uFB13-\\uFB17\\uFB1D\\uFB1F-\\uFB28\\uFB2A-\\uFB36]|[\\uFB38-\\uFB3C\\uFB3E\\uFB40\\uFB41\\uFB43\\uFB44\\uFB46-\\uFBB1\\uFBD3-\\uFD3D]|[\\uFD50-\\uFD8F\\uFD92-\\uFDC7\\uFDF0-\\uFDFB\\uFE70-\\uFE74\\uFE76-\\uFEFC]|[\\uFF21-\\uFF3A\\uFF41-\\uFF5A\\uFF66-\\uFFBE\\uFFC2-\\uFFC7\\uFFCA-\\uFFCF]|[\\uFFD2-\\uFFD7\\uFFDA-\\uFFDC])/, /^(?:\\s)/, /^(?:\\s)/, /^(?:$)/],\n      conditions: { \"namespace-body\": { \"rules\": [26, 33, 34, 35, 36, 37, 38, 49, 50, 51, 52, 53, 54, 55, 56, 57, 60, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 97], \"inclusive\": false }, \"namespace\": { \"rules\": [26, 29, 30, 31, 32, 49, 50, 51, 52, 53, 54, 55, 56, 57, 60, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 97], \"inclusive\": false }, \"class-body\": { \"rules\": [26, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 60, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 97], \"inclusive\": false }, \"class\": { \"rules\": [26, 39, 40, 41, 42, 49, 50, 51, 52, 53, 54, 55, 56, 57, 60, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 97], \"inclusive\": false }, \"acc_descr_multiline\": { \"rules\": [11, 12, 26, 49, 50, 51, 52, 53, 54, 55, 56, 57, 60, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 97], \"inclusive\": false }, \"acc_descr\": { \"rules\": [9, 26, 49, 50, 51, 52, 53, 54, 55, 56, 57, 60, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 97], \"inclusive\": false }, \"acc_title\": { \"rules\": [7, 26, 49, 50, 51, 52, 53, 54, 55, 56, 57, 60, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 97], \"inclusive\": false }, \"callback_args\": { \"rules\": [22, 23, 26, 49, 50, 51, 52, 53, 54, 55, 56, 57, 60, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 97], \"inclusive\": false }, \"callback_name\": { \"rules\": [19, 20, 21, 26, 49, 50, 51, 52, 53, 54, 55, 56, 57, 60, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 97], \"inclusive\": false }, \"href\": { \"rules\": [26, 49, 50, 51, 52, 53, 54, 55, 56, 57, 60, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 97], \"inclusive\": false }, \"struct\": { \"rules\": [26, 49, 50, 51, 52, 53, 54, 55, 56, 57, 60, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 97], \"inclusive\": false }, \"generic\": { \"rules\": [26, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 97], \"inclusive\": false }, \"bqstring\": { \"rules\": [26, 49, 50, 51, 52, 53, 54, 55, 56, 57, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 97], \"inclusive\": false }, \"string\": { \"rules\": [24, 25, 26, 49, 50, 51, 52, 53, 54, 55, 56, 57, 60, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 97], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 1, 2, 3, 4, 5, 6, 8, 10, 13, 14, 15, 16, 17, 18, 26, 27, 28, 29, 38, 49, 50, 51, 52, 53, 54, 55, 56, 57, 60, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  __name(Parser, \"Parser\");\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nvar classDiagram_default = parser;\n\n// src/diagrams/class/classDb.ts\nimport { select } from \"d3\";\n\n// src/diagrams/class/classTypes.ts\nvar visibilityValues = [\"#\", \"+\", \"~\", \"-\", \"\"];\nvar ClassMember = class {\n  static {\n    __name(this, \"ClassMember\");\n  }\n  constructor(input, memberType) {\n    this.memberType = memberType;\n    this.visibility = \"\";\n    this.classifier = \"\";\n    this.text = \"\";\n    const sanitizedInput = sanitizeText(input, getConfig());\n    this.parseMember(sanitizedInput);\n  }\n  getDisplayDetails() {\n    let displayText = this.visibility + parseGenericTypes(this.id);\n    if (this.memberType === \"method\") {\n      displayText += `(${parseGenericTypes(this.parameters.trim())})`;\n      if (this.returnType) {\n        displayText += \" : \" + parseGenericTypes(this.returnType);\n      }\n    }\n    displayText = displayText.trim();\n    const cssStyle = this.parseClassifier();\n    return {\n      displayText,\n      cssStyle\n    };\n  }\n  parseMember(input) {\n    let potentialClassifier = \"\";\n    if (this.memberType === \"method\") {\n      const methodRegEx = /([#+~-])?(.+)\\((.*)\\)([\\s$*])?(.*)([$*])?/;\n      const match = methodRegEx.exec(input);\n      if (match) {\n        const detectedVisibility = match[1] ? match[1].trim() : \"\";\n        if (visibilityValues.includes(detectedVisibility)) {\n          this.visibility = detectedVisibility;\n        }\n        this.id = match[2];\n        this.parameters = match[3] ? match[3].trim() : \"\";\n        potentialClassifier = match[4] ? match[4].trim() : \"\";\n        this.returnType = match[5] ? match[5].trim() : \"\";\n        if (potentialClassifier === \"\") {\n          const lastChar = this.returnType.substring(this.returnType.length - 1);\n          if (/[$*]/.exec(lastChar)) {\n            potentialClassifier = lastChar;\n            this.returnType = this.returnType.substring(0, this.returnType.length - 1);\n          }\n        }\n      }\n    } else {\n      const length = input.length;\n      const firstChar = input.substring(0, 1);\n      const lastChar = input.substring(length - 1);\n      if (visibilityValues.includes(firstChar)) {\n        this.visibility = firstChar;\n      }\n      if (/[$*]/.exec(lastChar)) {\n        potentialClassifier = lastChar;\n      }\n      this.id = input.substring(\n        this.visibility === \"\" ? 0 : 1,\n        potentialClassifier === \"\" ? length : length - 1\n      );\n    }\n    this.classifier = potentialClassifier;\n    this.id = this.id.startsWith(\" \") ? \" \" + this.id.trim() : this.id.trim();\n    const combinedText = `${this.visibility ? \"\\\\\" + this.visibility : \"\"}${parseGenericTypes(this.id)}${this.memberType === \"method\" ? `(${parseGenericTypes(this.parameters)})${this.returnType ? \" : \" + parseGenericTypes(this.returnType) : \"\"}` : \"\"}`;\n    this.text = combinedText.replaceAll(\"<\", \"&lt;\").replaceAll(\">\", \"&gt;\");\n    if (this.text.startsWith(\"\\\\&lt;\")) {\n      this.text = this.text.replace(\"\\\\&lt;\", \"~\");\n    }\n  }\n  parseClassifier() {\n    switch (this.classifier) {\n      case \"*\":\n        return \"font-style:italic;\";\n      case \"$\":\n        return \"text-decoration:underline;\";\n      default:\n        return \"\";\n    }\n  }\n};\n\n// src/diagrams/class/classDb.ts\nvar MERMAID_DOM_ID_PREFIX = \"classId-\";\nvar classCounter = 0;\nvar sanitizeText2 = /* @__PURE__ */ __name((txt) => common_default.sanitizeText(txt, getConfig()), \"sanitizeText\");\nvar ClassDB = class {\n  constructor() {\n    this.relations = [];\n    this.classes = /* @__PURE__ */ new Map();\n    this.styleClasses = /* @__PURE__ */ new Map();\n    this.notes = [];\n    this.interfaces = [];\n    // private static classCounter = 0;\n    this.namespaces = /* @__PURE__ */ new Map();\n    this.namespaceCounter = 0;\n    this.functions = [];\n    this.lineType = {\n      LINE: 0,\n      DOTTED_LINE: 1\n    };\n    this.relationType = {\n      AGGREGATION: 0,\n      EXTENSION: 1,\n      COMPOSITION: 2,\n      DEPENDENCY: 3,\n      LOLLIPOP: 4\n    };\n    this.setupToolTips = /* @__PURE__ */ __name((element) => {\n      let tooltipElem = select(\".mermaidTooltip\");\n      if ((tooltipElem._groups || tooltipElem)[0][0] === null) {\n        tooltipElem = select(\"body\").append(\"div\").attr(\"class\", \"mermaidTooltip\").style(\"opacity\", 0);\n      }\n      const svg = select(element).select(\"svg\");\n      const nodes = svg.selectAll(\"g.node\");\n      nodes.on(\"mouseover\", (event) => {\n        const el = select(event.currentTarget);\n        const title = el.attr(\"title\");\n        if (title === null) {\n          return;\n        }\n        const rect = this.getBoundingClientRect();\n        tooltipElem.transition().duration(200).style(\"opacity\", \".9\");\n        tooltipElem.text(el.attr(\"title\")).style(\"left\", window.scrollX + rect.left + (rect.right - rect.left) / 2 + \"px\").style(\"top\", window.scrollY + rect.top - 14 + document.body.scrollTop + \"px\");\n        tooltipElem.html(tooltipElem.html().replace(/&lt;br\\/&gt;/g, \"<br/>\"));\n        el.classed(\"hover\", true);\n      }).on(\"mouseout\", (event) => {\n        tooltipElem.transition().duration(500).style(\"opacity\", 0);\n        const el = select(event.currentTarget);\n        el.classed(\"hover\", false);\n      });\n    }, \"setupToolTips\");\n    this.direction = \"TB\";\n    this.setAccTitle = setAccTitle;\n    this.getAccTitle = getAccTitle;\n    this.setAccDescription = setAccDescription;\n    this.getAccDescription = getAccDescription;\n    this.setDiagramTitle = setDiagramTitle;\n    this.getDiagramTitle = getDiagramTitle;\n    this.getConfig = /* @__PURE__ */ __name(() => getConfig().class, \"getConfig\");\n    this.functions.push(this.setupToolTips.bind(this));\n    this.clear();\n    this.addRelation = this.addRelation.bind(this);\n    this.addClassesToNamespace = this.addClassesToNamespace.bind(this);\n    this.addNamespace = this.addNamespace.bind(this);\n    this.setCssClass = this.setCssClass.bind(this);\n    this.addMembers = this.addMembers.bind(this);\n    this.addClass = this.addClass.bind(this);\n    this.setClassLabel = this.setClassLabel.bind(this);\n    this.addAnnotation = this.addAnnotation.bind(this);\n    this.addMember = this.addMember.bind(this);\n    this.cleanupLabel = this.cleanupLabel.bind(this);\n    this.addNote = this.addNote.bind(this);\n    this.defineClass = this.defineClass.bind(this);\n    this.setDirection = this.setDirection.bind(this);\n    this.setLink = this.setLink.bind(this);\n    this.bindFunctions = this.bindFunctions.bind(this);\n    this.clear = this.clear.bind(this);\n    this.setTooltip = this.setTooltip.bind(this);\n    this.setClickEvent = this.setClickEvent.bind(this);\n    this.setCssStyle = this.setCssStyle.bind(this);\n  }\n  static {\n    __name(this, \"ClassDB\");\n  }\n  splitClassNameAndType(_id) {\n    const id = common_default.sanitizeText(_id, getConfig());\n    let genericType = \"\";\n    let className = id;\n    if (id.indexOf(\"~\") > 0) {\n      const split = id.split(\"~\");\n      className = sanitizeText2(split[0]);\n      genericType = sanitizeText2(split[1]);\n    }\n    return { className, type: genericType };\n  }\n  setClassLabel(_id, label) {\n    const id = common_default.sanitizeText(_id, getConfig());\n    if (label) {\n      label = sanitizeText2(label);\n    }\n    const { className } = this.splitClassNameAndType(id);\n    this.classes.get(className).label = label;\n    this.classes.get(className).text = `${label}${this.classes.get(className).type ? `<${this.classes.get(className).type}>` : \"\"}`;\n  }\n  /**\n   * Function called by parser when a node definition has been found.\n   *\n   * @param id - Id of the class to add\n   * @public\n   */\n  addClass(_id) {\n    const id = common_default.sanitizeText(_id, getConfig());\n    const { className, type } = this.splitClassNameAndType(id);\n    if (this.classes.has(className)) {\n      return;\n    }\n    const name = common_default.sanitizeText(className, getConfig());\n    this.classes.set(name, {\n      id: name,\n      type,\n      label: name,\n      text: `${name}${type ? `&lt;${type}&gt;` : \"\"}`,\n      shape: \"classBox\",\n      cssClasses: \"default\",\n      methods: [],\n      members: [],\n      annotations: [],\n      styles: [],\n      domId: MERMAID_DOM_ID_PREFIX + name + \"-\" + classCounter\n    });\n    classCounter++;\n  }\n  addInterface(label, classId) {\n    const classInterface = {\n      id: `interface${this.interfaces.length}`,\n      label,\n      classId\n    };\n    this.interfaces.push(classInterface);\n  }\n  /**\n   * Function to lookup domId from id in the graph definition.\n   *\n   * @param id - class ID to lookup\n   * @public\n   */\n  lookUpDomId(_id) {\n    const id = common_default.sanitizeText(_id, getConfig());\n    if (this.classes.has(id)) {\n      return this.classes.get(id).domId;\n    }\n    throw new Error(\"Class not found: \" + id);\n  }\n  clear() {\n    this.relations = [];\n    this.classes = /* @__PURE__ */ new Map();\n    this.notes = [];\n    this.interfaces = [];\n    this.functions = [];\n    this.functions.push(this.setupToolTips.bind(this));\n    this.namespaces = /* @__PURE__ */ new Map();\n    this.namespaceCounter = 0;\n    this.direction = \"TB\";\n    clear();\n  }\n  getClass(id) {\n    return this.classes.get(id);\n  }\n  getClasses() {\n    return this.classes;\n  }\n  getRelations() {\n    return this.relations;\n  }\n  getNotes() {\n    return this.notes;\n  }\n  addRelation(classRelation) {\n    log.debug(\"Adding relation: \" + JSON.stringify(classRelation));\n    const invalidTypes = [\n      this.relationType.LOLLIPOP,\n      this.relationType.AGGREGATION,\n      this.relationType.COMPOSITION,\n      this.relationType.DEPENDENCY,\n      this.relationType.EXTENSION\n    ];\n    if (classRelation.relation.type1 === this.relationType.LOLLIPOP && !invalidTypes.includes(classRelation.relation.type2)) {\n      this.addClass(classRelation.id2);\n      this.addInterface(classRelation.id1, classRelation.id2);\n      classRelation.id1 = `interface${this.interfaces.length - 1}`;\n    } else if (classRelation.relation.type2 === this.relationType.LOLLIPOP && !invalidTypes.includes(classRelation.relation.type1)) {\n      this.addClass(classRelation.id1);\n      this.addInterface(classRelation.id2, classRelation.id1);\n      classRelation.id2 = `interface${this.interfaces.length - 1}`;\n    } else {\n      this.addClass(classRelation.id1);\n      this.addClass(classRelation.id2);\n    }\n    classRelation.id1 = this.splitClassNameAndType(classRelation.id1).className;\n    classRelation.id2 = this.splitClassNameAndType(classRelation.id2).className;\n    classRelation.relationTitle1 = common_default.sanitizeText(\n      classRelation.relationTitle1.trim(),\n      getConfig()\n    );\n    classRelation.relationTitle2 = common_default.sanitizeText(\n      classRelation.relationTitle2.trim(),\n      getConfig()\n    );\n    this.relations.push(classRelation);\n  }\n  /**\n   * Adds an annotation to the specified class Annotations mark special properties of the given type\n   * (like 'interface' or 'service')\n   *\n   * @param className - The class name\n   * @param annotation - The name of the annotation without any brackets\n   * @public\n   */\n  addAnnotation(className, annotation) {\n    const validatedClassName = this.splitClassNameAndType(className).className;\n    this.classes.get(validatedClassName).annotations.push(annotation);\n  }\n  /**\n   * Adds a member to the specified class\n   *\n   * @param className - The class name\n   * @param member - The full name of the member. If the member is enclosed in `<<brackets>>` it is\n   *   treated as an annotation If the member is ending with a closing bracket ) it is treated as a\n   *   method Otherwise the member will be treated as a normal property\n   * @public\n   */\n  addMember(className, member) {\n    this.addClass(className);\n    const validatedClassName = this.splitClassNameAndType(className).className;\n    const theClass = this.classes.get(validatedClassName);\n    if (typeof member === \"string\") {\n      const memberString = member.trim();\n      if (memberString.startsWith(\"<<\") && memberString.endsWith(\">>\")) {\n        theClass.annotations.push(sanitizeText2(memberString.substring(2, memberString.length - 2)));\n      } else if (memberString.indexOf(\")\") > 0) {\n        theClass.methods.push(new ClassMember(memberString, \"method\"));\n      } else if (memberString) {\n        theClass.members.push(new ClassMember(memberString, \"attribute\"));\n      }\n    }\n  }\n  addMembers(className, members) {\n    if (Array.isArray(members)) {\n      members.reverse();\n      members.forEach((member) => this.addMember(className, member));\n    }\n  }\n  addNote(text, className) {\n    const note = {\n      id: `note${this.notes.length}`,\n      class: className,\n      text\n    };\n    this.notes.push(note);\n  }\n  cleanupLabel(label) {\n    if (label.startsWith(\":\")) {\n      label = label.substring(1);\n    }\n    return sanitizeText2(label.trim());\n  }\n  /**\n   * Called by parser when assigning cssClass to a class\n   *\n   * @param ids - Comma separated list of ids\n   * @param className - Class to add\n   */\n  setCssClass(ids, className) {\n    ids.split(\",\").forEach((_id) => {\n      let id = _id;\n      if (/\\d/.exec(_id[0])) {\n        id = MERMAID_DOM_ID_PREFIX + id;\n      }\n      const classNode = this.classes.get(id);\n      if (classNode) {\n        classNode.cssClasses += \" \" + className;\n      }\n    });\n  }\n  defineClass(ids, style) {\n    for (const id of ids) {\n      let styleClass = this.styleClasses.get(id);\n      if (styleClass === void 0) {\n        styleClass = { id, styles: [], textStyles: [] };\n        this.styleClasses.set(id, styleClass);\n      }\n      if (style) {\n        style.forEach((s) => {\n          if (/color/.exec(s)) {\n            const newStyle = s.replace(\"fill\", \"bgFill\");\n            styleClass.textStyles.push(newStyle);\n          }\n          styleClass.styles.push(s);\n        });\n      }\n      this.classes.forEach((value) => {\n        if (value.cssClasses.includes(id)) {\n          value.styles.push(...style.flatMap((s) => s.split(\",\")));\n        }\n      });\n    }\n  }\n  /**\n   * Called by parser when a tooltip is found, e.g. a clickable element.\n   *\n   * @param ids - Comma separated list of ids\n   * @param tooltip - Tooltip to add\n   */\n  setTooltip(ids, tooltip) {\n    ids.split(\",\").forEach((id) => {\n      if (tooltip !== void 0) {\n        this.classes.get(id).tooltip = sanitizeText2(tooltip);\n      }\n    });\n  }\n  getTooltip(id, namespace) {\n    if (namespace && this.namespaces.has(namespace)) {\n      return this.namespaces.get(namespace).classes.get(id).tooltip;\n    }\n    return this.classes.get(id).tooltip;\n  }\n  /**\n   * Called by parser when a link is found. Adds the URL to the vertex data.\n   *\n   * @param ids - Comma separated list of ids\n   * @param linkStr - URL to create a link for\n   * @param target - Target of the link, _blank by default as originally defined in the svgDraw.js file\n   */\n  setLink(ids, linkStr, target) {\n    const config = getConfig();\n    ids.split(\",\").forEach((_id) => {\n      let id = _id;\n      if (/\\d/.exec(_id[0])) {\n        id = MERMAID_DOM_ID_PREFIX + id;\n      }\n      const theClass = this.classes.get(id);\n      if (theClass) {\n        theClass.link = utils_default.formatUrl(linkStr, config);\n        if (config.securityLevel === \"sandbox\") {\n          theClass.linkTarget = \"_top\";\n        } else if (typeof target === \"string\") {\n          theClass.linkTarget = sanitizeText2(target);\n        } else {\n          theClass.linkTarget = \"_blank\";\n        }\n      }\n    });\n    this.setCssClass(ids, \"clickable\");\n  }\n  /**\n   * Called by parser when a click definition is found. Registers an event handler.\n   *\n   * @param ids - Comma separated list of ids\n   * @param functionName - Function to be called on click\n   * @param functionArgs - Function args the function should be called with\n   */\n  setClickEvent(ids, functionName, functionArgs) {\n    ids.split(\",\").forEach((id) => {\n      this.setClickFunc(id, functionName, functionArgs);\n      this.classes.get(id).haveCallback = true;\n    });\n    this.setCssClass(ids, \"clickable\");\n  }\n  setClickFunc(_domId, functionName, functionArgs) {\n    const domId = common_default.sanitizeText(_domId, getConfig());\n    const config = getConfig();\n    if (config.securityLevel !== \"loose\") {\n      return;\n    }\n    if (functionName === void 0) {\n      return;\n    }\n    const id = domId;\n    if (this.classes.has(id)) {\n      const elemId = this.lookUpDomId(id);\n      let argList = [];\n      if (typeof functionArgs === \"string\") {\n        argList = functionArgs.split(/,(?=(?:(?:[^\"]*\"){2})*[^\"]*$)/);\n        for (let i = 0; i < argList.length; i++) {\n          let item = argList[i].trim();\n          if (item.startsWith('\"') && item.endsWith('\"')) {\n            item = item.substr(1, item.length - 2);\n          }\n          argList[i] = item;\n        }\n      }\n      if (argList.length === 0) {\n        argList.push(elemId);\n      }\n      this.functions.push(() => {\n        const elem = document.querySelector(`[id=\"${elemId}\"]`);\n        if (elem !== null) {\n          elem.addEventListener(\n            \"click\",\n            () => {\n              utils_default.runFunc(functionName, ...argList);\n            },\n            false\n          );\n        }\n      });\n    }\n  }\n  bindFunctions(element) {\n    this.functions.forEach((fun) => {\n      fun(element);\n    });\n  }\n  getDirection() {\n    return this.direction;\n  }\n  setDirection(dir) {\n    this.direction = dir;\n  }\n  /**\n   * Function called by parser when a namespace definition has been found.\n   *\n   * @param id - Id of the namespace to add\n   * @public\n   */\n  addNamespace(id) {\n    if (this.namespaces.has(id)) {\n      return;\n    }\n    this.namespaces.set(id, {\n      id,\n      classes: /* @__PURE__ */ new Map(),\n      children: {},\n      domId: MERMAID_DOM_ID_PREFIX + id + \"-\" + this.namespaceCounter\n    });\n    this.namespaceCounter++;\n  }\n  getNamespace(name) {\n    return this.namespaces.get(name);\n  }\n  getNamespaces() {\n    return this.namespaces;\n  }\n  /**\n   * Function called by parser when a namespace definition has been found.\n   *\n   * @param id - Id of the namespace to add\n   * @param classNames - Ids of the class to add\n   * @public\n   */\n  addClassesToNamespace(id, classNames) {\n    if (!this.namespaces.has(id)) {\n      return;\n    }\n    for (const name of classNames) {\n      const { className } = this.splitClassNameAndType(name);\n      this.classes.get(className).parent = id;\n      this.namespaces.get(id).classes.set(className, this.classes.get(className));\n    }\n  }\n  setCssStyle(id, styles) {\n    const thisClass = this.classes.get(id);\n    if (!styles || !thisClass) {\n      return;\n    }\n    for (const s of styles) {\n      if (s.includes(\",\")) {\n        thisClass.styles.push(...s.split(\",\"));\n      } else {\n        thisClass.styles.push(s);\n      }\n    }\n  }\n  /**\n   * Gets the arrow marker for a type index\n   *\n   * @param type - The type to look for\n   * @returns The arrow marker\n   */\n  getArrowMarker(type) {\n    let marker;\n    switch (type) {\n      case 0:\n        marker = \"aggregation\";\n        break;\n      case 1:\n        marker = \"extension\";\n        break;\n      case 2:\n        marker = \"composition\";\n        break;\n      case 3:\n        marker = \"dependency\";\n        break;\n      case 4:\n        marker = \"lollipop\";\n        break;\n      default:\n        marker = \"none\";\n    }\n    return marker;\n  }\n  getData() {\n    const nodes = [];\n    const edges = [];\n    const config = getConfig();\n    for (const namespaceKey of this.namespaces.keys()) {\n      const namespace = this.namespaces.get(namespaceKey);\n      if (namespace) {\n        const node = {\n          id: namespace.id,\n          label: namespace.id,\n          isGroup: true,\n          padding: config.class.padding ?? 16,\n          // parent node must be one of [rect, roundedWithTitle, noteGroup, divider]\n          shape: \"rect\",\n          cssStyles: [\"fill: none\", \"stroke: black\"],\n          look: config.look\n        };\n        nodes.push(node);\n      }\n    }\n    for (const classKey of this.classes.keys()) {\n      const classNode = this.classes.get(classKey);\n      if (classNode) {\n        const node = classNode;\n        node.parentId = classNode.parent;\n        node.look = config.look;\n        nodes.push(node);\n      }\n    }\n    let cnt = 0;\n    for (const note of this.notes) {\n      cnt++;\n      const noteNode = {\n        id: note.id,\n        label: note.text,\n        isGroup: false,\n        shape: \"note\",\n        padding: config.class.padding ?? 6,\n        cssStyles: [\n          \"text-align: left\",\n          \"white-space: nowrap\",\n          `fill: ${config.themeVariables.noteBkgColor}`,\n          `stroke: ${config.themeVariables.noteBorderColor}`\n        ],\n        look: config.look\n      };\n      nodes.push(noteNode);\n      const noteClassId = this.classes.get(note.class)?.id ?? \"\";\n      if (noteClassId) {\n        const edge = {\n          id: `edgeNote${cnt}`,\n          start: note.id,\n          end: noteClassId,\n          type: \"normal\",\n          thickness: \"normal\",\n          classes: \"relation\",\n          arrowTypeStart: \"none\",\n          arrowTypeEnd: \"none\",\n          arrowheadStyle: \"\",\n          labelStyle: [\"\"],\n          style: [\"fill: none\"],\n          pattern: \"dotted\",\n          look: config.look\n        };\n        edges.push(edge);\n      }\n    }\n    for (const _interface of this.interfaces) {\n      const interfaceNode = {\n        id: _interface.id,\n        label: _interface.label,\n        isGroup: false,\n        shape: \"rect\",\n        cssStyles: [\"opacity: 0;\"],\n        look: config.look\n      };\n      nodes.push(interfaceNode);\n    }\n    cnt = 0;\n    for (const classRelation of this.relations) {\n      cnt++;\n      const edge = {\n        id: getEdgeId(classRelation.id1, classRelation.id2, {\n          prefix: \"id\",\n          counter: cnt\n        }),\n        start: classRelation.id1,\n        end: classRelation.id2,\n        type: \"normal\",\n        label: classRelation.title,\n        labelpos: \"c\",\n        thickness: \"normal\",\n        classes: \"relation\",\n        arrowTypeStart: this.getArrowMarker(classRelation.relation.type1),\n        arrowTypeEnd: this.getArrowMarker(classRelation.relation.type2),\n        startLabelRight: classRelation.relationTitle1 === \"none\" ? \"\" : classRelation.relationTitle1,\n        endLabelLeft: classRelation.relationTitle2 === \"none\" ? \"\" : classRelation.relationTitle2,\n        arrowheadStyle: \"\",\n        labelStyle: [\"display: inline-block\"],\n        style: classRelation.style || \"\",\n        pattern: classRelation.relation.lineType == 1 ? \"dashed\" : \"solid\",\n        look: config.look\n      };\n      edges.push(edge);\n    }\n    return { nodes, edges, other: {}, config, direction: this.getDirection() };\n  }\n};\n\n// src/diagrams/class/styles.js\nvar getStyles = /* @__PURE__ */ __name((options) => `g.classGroup text {\n  fill: ${options.nodeBorder || options.classText};\n  stroke: none;\n  font-family: ${options.fontFamily};\n  font-size: 10px;\n\n  .title {\n    font-weight: bolder;\n  }\n\n}\n\n.nodeLabel, .edgeLabel {\n  color: ${options.classText};\n}\n.edgeLabel .label rect {\n  fill: ${options.mainBkg};\n}\n.label text {\n  fill: ${options.classText};\n}\n\n.labelBkg {\n  background: ${options.mainBkg};\n}\n.edgeLabel .label span {\n  background: ${options.mainBkg};\n}\n\n.classTitle {\n  font-weight: bolder;\n}\n.node rect,\n  .node circle,\n  .node ellipse,\n  .node polygon,\n  .node path {\n    fill: ${options.mainBkg};\n    stroke: ${options.nodeBorder};\n    stroke-width: 1px;\n  }\n\n\n.divider {\n  stroke: ${options.nodeBorder};\n  stroke-width: 1;\n}\n\ng.clickable {\n  cursor: pointer;\n}\n\ng.classGroup rect {\n  fill: ${options.mainBkg};\n  stroke: ${options.nodeBorder};\n}\n\ng.classGroup line {\n  stroke: ${options.nodeBorder};\n  stroke-width: 1;\n}\n\n.classLabel .box {\n  stroke: none;\n  stroke-width: 0;\n  fill: ${options.mainBkg};\n  opacity: 0.5;\n}\n\n.classLabel .label {\n  fill: ${options.nodeBorder};\n  font-size: 10px;\n}\n\n.relation {\n  stroke: ${options.lineColor};\n  stroke-width: 1;\n  fill: none;\n}\n\n.dashed-line{\n  stroke-dasharray: 3;\n}\n\n.dotted-line{\n  stroke-dasharray: 1 2;\n}\n\n#compositionStart, .composition {\n  fill: ${options.lineColor} !important;\n  stroke: ${options.lineColor} !important;\n  stroke-width: 1;\n}\n\n#compositionEnd, .composition {\n  fill: ${options.lineColor} !important;\n  stroke: ${options.lineColor} !important;\n  stroke-width: 1;\n}\n\n#dependencyStart, .dependency {\n  fill: ${options.lineColor} !important;\n  stroke: ${options.lineColor} !important;\n  stroke-width: 1;\n}\n\n#dependencyStart, .dependency {\n  fill: ${options.lineColor} !important;\n  stroke: ${options.lineColor} !important;\n  stroke-width: 1;\n}\n\n#extensionStart, .extension {\n  fill: transparent !important;\n  stroke: ${options.lineColor} !important;\n  stroke-width: 1;\n}\n\n#extensionEnd, .extension {\n  fill: transparent !important;\n  stroke: ${options.lineColor} !important;\n  stroke-width: 1;\n}\n\n#aggregationStart, .aggregation {\n  fill: transparent !important;\n  stroke: ${options.lineColor} !important;\n  stroke-width: 1;\n}\n\n#aggregationEnd, .aggregation {\n  fill: transparent !important;\n  stroke: ${options.lineColor} !important;\n  stroke-width: 1;\n}\n\n#lollipopStart, .lollipop {\n  fill: ${options.mainBkg} !important;\n  stroke: ${options.lineColor} !important;\n  stroke-width: 1;\n}\n\n#lollipopEnd, .lollipop {\n  fill: ${options.mainBkg} !important;\n  stroke: ${options.lineColor} !important;\n  stroke-width: 1;\n}\n\n.edgeTerminals {\n  font-size: 11px;\n  line-height: initial;\n}\n\n.classTitleText {\n  text-anchor: middle;\n  font-size: 18px;\n  fill: ${options.textColor};\n}\n`, \"getStyles\");\nvar styles_default = getStyles;\n\n// src/diagrams/class/classRenderer-v3-unified.ts\nvar getDir = /* @__PURE__ */ __name((parsedItem, defaultDir = \"TB\") => {\n  if (!parsedItem.doc) {\n    return defaultDir;\n  }\n  let dir = defaultDir;\n  for (const parsedItemDoc of parsedItem.doc) {\n    if (parsedItemDoc.stmt === \"dir\") {\n      dir = parsedItemDoc.value;\n    }\n  }\n  return dir;\n}, \"getDir\");\nvar getClasses = /* @__PURE__ */ __name(function(text, diagramObj) {\n  return diagramObj.db.getClasses();\n}, \"getClasses\");\nvar draw = /* @__PURE__ */ __name(async function(text, id, _version, diag) {\n  log.info(\"REF0:\");\n  log.info(\"Drawing class diagram (v3)\", id);\n  const { securityLevel, state: conf, layout } = getConfig();\n  const data4Layout = diag.db.getData();\n  const svg = getDiagramElement(id, securityLevel);\n  data4Layout.type = diag.type;\n  data4Layout.layoutAlgorithm = getRegisteredLayoutAlgorithm(layout);\n  data4Layout.nodeSpacing = conf?.nodeSpacing || 50;\n  data4Layout.rankSpacing = conf?.rankSpacing || 50;\n  data4Layout.markers = [\"aggregation\", \"extension\", \"composition\", \"dependency\", \"lollipop\"];\n  data4Layout.diagramId = id;\n  await render(data4Layout, svg);\n  const padding = 8;\n  utils_default.insertTitle(\n    svg,\n    \"classDiagramTitleText\",\n    conf?.titleTopMargin ?? 25,\n    diag.db.getDiagramTitle()\n  );\n  setupViewPortForSVG(svg, padding, \"classDiagram\", conf?.useMaxWidth ?? true);\n}, \"draw\");\nvar classRenderer_v3_unified_default = {\n  getClasses,\n  draw,\n  getDir\n};\n\nexport {\n  classDiagram_default,\n  ClassDB,\n  styles_default,\n  classRenderer_v3_unified_default\n};\n", "import {\n  __name,\n  configureSvgSize,\n  log\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/rendering-util/insertElementsForSize.js\nimport { select } from \"d3\";\nvar getDiagramElement = /* @__PURE__ */ __name((id, securityLevel) => {\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = select(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? select(sandboxElement.nodes()[0].contentDocument.body) : select(\"body\");\n  const svg = root.select(`[id=\"${id}\"]`);\n  return svg;\n}, \"getDiagramElement\");\n\n// src/rendering-util/setupViewPortForSVG.ts\nvar setupViewPortForSVG = /* @__PURE__ */ __name((svg, padding, cssDiagram, useMaxWidth) => {\n  svg.attr(\"class\", cssDiagram);\n  const { width, height, x, y } = calculateDimensionsWithPadding(svg, padding);\n  configureSvgSize(svg, height, width, useMaxWidth);\n  const viewBox = createViewBox(x, y, width, height, padding);\n  svg.attr(\"viewBox\", viewBox);\n  log.debug(`viewBox configured: ${viewBox} with padding: ${padding}`);\n}, \"setupViewPortForSVG\");\nvar calculateDimensionsWithPadding = /* @__PURE__ */ __name((svg, padding) => {\n  const bounds = svg.node()?.getBBox() || { width: 0, height: 0, x: 0, y: 0 };\n  return {\n    width: bounds.width + padding * 2,\n    height: bounds.height + padding * 2,\n    x: bounds.x,\n    y: bounds.y\n  };\n}, \"calculateDimensionsWithPadding\");\nvar createViewBox = /* @__PURE__ */ __name((x, y, width, height, padding) => {\n  return `${x - padding} ${y - padding} ${width} ${height}`;\n}, \"createViewBox\");\n\nexport {\n  getDiagramElement,\n  setupViewPortForSVG\n};\n"], "names": [], "sourceRoot": ""}