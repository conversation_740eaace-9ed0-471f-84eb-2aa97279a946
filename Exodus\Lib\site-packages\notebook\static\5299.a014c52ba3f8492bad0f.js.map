{"version": 3, "file": "5299.a014c52ba3f8492bad0f.js?v=a014c52ba3f8492bad0f", "mappings": ";;;;;;;;;;AAAA;AACA;AACA,oCAAoC,OAAO;AAC3C;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,eAAe,YAAY,KAAK;AAChC,eAAe,YAAY,KAAK;;AAEhC;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA,oCAAoC,kBAAkB;AACtD;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA,IAAI,sFAAsF;AAC1F;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA,MAAM;AACN;AACA,MAAM;AACN,qBAAqB;AACrB;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA,IAAI,2DAA2D;AAC/D;AACA;AACA,IAAI,qBAAqB,GAAG;AAC5B;AACA;AACA,IAAI;AACJ;AACA,IAAI;AACJ;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA,QAAQ,YAAY;AACpB;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,2BAA2B;AAC3B;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA,MAAM,4BAA4B;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,mBAAmB;AACnB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB,yBAAyB;AACzB,6BAA6B;AAC7B;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEO;AACP;AACA;AACA,YAAY;AACZ;AACA,sBAAsB,mCAAmC;AACzD;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,sCAAsC;AACtC,uBAAuB;AACvB,4CAA4C;AAC5C;;AAEA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA,mDAAmD;AACnD,oBAAoB,UAAU;AAC9B;AACA;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/ruby.js"], "sourcesContent": ["function wordObj(words) {\n  var o = {};\n  for (var i = 0, e = words.length; i < e; ++i) o[words[i]] = true;\n  return o;\n}\n\nvar keywordList = [\n  \"alias\", \"and\", \"BEGIN\", \"begin\", \"break\", \"case\", \"class\", \"def\", \"defined?\", \"do\", \"else\",\n  \"elsif\", \"END\", \"end\", \"ensure\", \"false\", \"for\", \"if\", \"in\", \"module\", \"next\", \"not\", \"or\",\n  \"redo\", \"rescue\", \"retry\", \"return\", \"self\", \"super\", \"then\", \"true\", \"undef\", \"unless\",\n  \"until\", \"when\", \"while\", \"yield\", \"nil\", \"raise\", \"throw\", \"catch\", \"fail\", \"loop\", \"callcc\",\n  \"caller\", \"lambda\", \"proc\", \"public\", \"protected\", \"private\", \"require\", \"load\",\n  \"require_relative\", \"extend\", \"autoload\", \"__END__\", \"__FILE__\", \"__LINE__\", \"__dir__\"\n], keywords = wordObj(keywordList);\n\nvar indentWords = wordObj([\"def\", \"class\", \"case\", \"for\", \"while\", \"until\", \"module\",\n                           \"catch\", \"loop\", \"proc\", \"begin\"]);\nvar dedentWords = wordObj([\"end\", \"until\"]);\nvar opening = {\"[\": \"]\", \"{\": \"}\", \"(\": \")\"};\nvar closing = {\"]\": \"[\", \"}\": \"{\", \")\": \"(\"};\n\nvar curPunc;\n\nfunction chain(newtok, stream, state) {\n  state.tokenize.push(newtok);\n  return newtok(stream, state);\n}\n\nfunction tokenBase(stream, state) {\n  if (stream.sol() && stream.match(\"=begin\") && stream.eol()) {\n    state.tokenize.push(readBlockComment);\n    return \"comment\";\n  }\n  if (stream.eatSpace()) return null;\n  var ch = stream.next(), m;\n  if (ch == \"`\" || ch == \"'\" || ch == '\"') {\n    return chain(readQuoted(ch, \"string\", ch == '\"' || ch == \"`\"), stream, state);\n  } else if (ch == \"/\") {\n    if (regexpAhead(stream))\n      return chain(readQuoted(ch, \"string.special\", true), stream, state);\n    else\n      return \"operator\";\n  } else if (ch == \"%\") {\n    var style = \"string\", embed = true;\n    if (stream.eat(\"s\")) style = \"atom\";\n    else if (stream.eat(/[WQ]/)) style = \"string\";\n    else if (stream.eat(/[r]/)) style = \"string.special\";\n    else if (stream.eat(/[wxq]/)) { style = \"string\"; embed = false; }\n    var delim = stream.eat(/[^\\w\\s=]/);\n    if (!delim) return \"operator\";\n    if (opening.propertyIsEnumerable(delim)) delim = opening[delim];\n    return chain(readQuoted(delim, style, embed, true), stream, state);\n  } else if (ch == \"#\") {\n    stream.skipToEnd();\n    return \"comment\";\n  } else if (ch == \"<\" && (m = stream.match(/^<([-~])[\\`\\\"\\']?([a-zA-Z_?]\\w*)[\\`\\\"\\']?(?:;|$)/))) {\n    return chain(readHereDoc(m[2], m[1]), stream, state);\n  } else if (ch == \"0\") {\n    if (stream.eat(\"x\")) stream.eatWhile(/[\\da-fA-F]/);\n    else if (stream.eat(\"b\")) stream.eatWhile(/[01]/);\n    else stream.eatWhile(/[0-7]/);\n    return \"number\";\n  } else if (/\\d/.test(ch)) {\n    stream.match(/^[\\d_]*(?:\\.[\\d_]+)?(?:[eE][+\\-]?[\\d_]+)?/);\n    return \"number\";\n  } else if (ch == \"?\") {\n    while (stream.match(/^\\\\[CM]-/)) {}\n    if (stream.eat(\"\\\\\")) stream.eatWhile(/\\w/);\n    else stream.next();\n    return \"string\";\n  } else if (ch == \":\") {\n    if (stream.eat(\"'\")) return chain(readQuoted(\"'\", \"atom\", false), stream, state);\n    if (stream.eat('\"')) return chain(readQuoted('\"', \"atom\", true), stream, state);\n\n    // :> :>> :< :<< are valid symbols\n    if (stream.eat(/[\\<\\>]/)) {\n      stream.eat(/[\\<\\>]/);\n      return \"atom\";\n    }\n\n    // :+ :- :/ :* :| :& :! are valid symbols\n    if (stream.eat(/[\\+\\-\\*\\/\\&\\|\\:\\!]/)) {\n      return \"atom\";\n    }\n\n    // Symbols can't start by a digit\n    if (stream.eat(/[a-zA-Z$@_\\xa1-\\uffff]/)) {\n      stream.eatWhile(/[\\w$\\xa1-\\uffff]/);\n      // Only one ? ! = is allowed and only as the last character\n      stream.eat(/[\\?\\!\\=]/);\n      return \"atom\";\n    }\n    return \"operator\";\n  } else if (ch == \"@\" && stream.match(/^@?[a-zA-Z_\\xa1-\\uffff]/)) {\n    stream.eat(\"@\");\n    stream.eatWhile(/[\\w\\xa1-\\uffff]/);\n    return \"propertyName\";\n  } else if (ch == \"$\") {\n    if (stream.eat(/[a-zA-Z_]/)) {\n      stream.eatWhile(/[\\w]/);\n    } else if (stream.eat(/\\d/)) {\n      stream.eat(/\\d/);\n    } else {\n      stream.next(); // Must be a special global like $: or $!\n    }\n    return \"variableName.special\";\n  } else if (/[a-zA-Z_\\xa1-\\uffff]/.test(ch)) {\n    stream.eatWhile(/[\\w\\xa1-\\uffff]/);\n    stream.eat(/[\\?\\!]/);\n    if (stream.eat(\":\")) return \"atom\";\n    return \"variable\";\n  } else if (ch == \"|\" && (state.varList || state.lastTok == \"{\" || state.lastTok == \"do\")) {\n    curPunc = \"|\";\n    return null;\n  } else if (/[\\(\\)\\[\\]{}\\\\;]/.test(ch)) {\n    curPunc = ch;\n    return null;\n  } else if (ch == \"-\" && stream.eat(\">\")) {\n    return \"operator\";\n  } else if (/[=+\\-\\/*:\\.^%<>~|]/.test(ch)) {\n    var more = stream.eatWhile(/[=+\\-\\/*:\\.^%<>~|]/);\n    if (ch == \".\" && !more) curPunc = \".\";\n    return \"operator\";\n  } else {\n    return null;\n  }\n}\n\nfunction regexpAhead(stream) {\n  var start = stream.pos, depth = 0, next, found = false, escaped = false\n  while ((next = stream.next()) != null) {\n    if (!escaped) {\n      if (\"[{(\".indexOf(next) > -1) {\n        depth++\n      } else if (\"]})\".indexOf(next) > -1) {\n        depth--\n        if (depth < 0) break\n      } else if (next == \"/\" && depth == 0) {\n        found = true\n        break\n      }\n      escaped = next == \"\\\\\"\n    } else {\n      escaped = false\n    }\n  }\n  stream.backUp(stream.pos - start)\n  return found\n}\n\nfunction tokenBaseUntilBrace(depth) {\n  if (!depth) depth = 1;\n  return function(stream, state) {\n    if (stream.peek() == \"}\") {\n      if (depth == 1) {\n        state.tokenize.pop();\n        return state.tokenize[state.tokenize.length-1](stream, state);\n      } else {\n        state.tokenize[state.tokenize.length - 1] = tokenBaseUntilBrace(depth - 1);\n      }\n    } else if (stream.peek() == \"{\") {\n      state.tokenize[state.tokenize.length - 1] = tokenBaseUntilBrace(depth + 1);\n    }\n    return tokenBase(stream, state);\n  };\n}\nfunction tokenBaseOnce() {\n  var alreadyCalled = false;\n  return function(stream, state) {\n    if (alreadyCalled) {\n      state.tokenize.pop();\n      return state.tokenize[state.tokenize.length-1](stream, state);\n    }\n    alreadyCalled = true;\n    return tokenBase(stream, state);\n  };\n}\nfunction readQuoted(quote, style, embed, unescaped) {\n  return function(stream, state) {\n    var escaped = false, ch;\n\n    if (state.context.type === 'read-quoted-paused') {\n      state.context = state.context.prev;\n      stream.eat(\"}\");\n    }\n\n    while ((ch = stream.next()) != null) {\n      if (ch == quote && (unescaped || !escaped)) {\n        state.tokenize.pop();\n        break;\n      }\n      if (embed && ch == \"#\" && !escaped) {\n        if (stream.eat(\"{\")) {\n          if (quote == \"}\") {\n            state.context = {prev: state.context, type: 'read-quoted-paused'};\n          }\n          state.tokenize.push(tokenBaseUntilBrace());\n          break;\n        } else if (/[@\\$]/.test(stream.peek())) {\n          state.tokenize.push(tokenBaseOnce());\n          break;\n        }\n      }\n      escaped = !escaped && ch == \"\\\\\";\n    }\n    return style;\n  };\n}\nfunction readHereDoc(phrase, mayIndent) {\n  return function(stream, state) {\n    if (mayIndent) stream.eatSpace()\n    if (stream.match(phrase)) state.tokenize.pop();\n    else stream.skipToEnd();\n    return \"string\";\n  };\n}\nfunction readBlockComment(stream, state) {\n  if (stream.sol() && stream.match(\"=end\") && stream.eol())\n    state.tokenize.pop();\n  stream.skipToEnd();\n  return \"comment\";\n}\n\nexport const ruby = {\n  name: \"ruby\",\n  startState: function(indentUnit) {\n    return {tokenize: [tokenBase],\n            indented: 0,\n            context: {type: \"top\", indented: -indentUnit},\n            continuedLine: false,\n            lastTok: null,\n            varList: false};\n  },\n\n  token: function(stream, state) {\n    curPunc = null;\n    if (stream.sol()) state.indented = stream.indentation();\n    var style = state.tokenize[state.tokenize.length-1](stream, state), kwtype;\n    var thisTok = curPunc;\n    if (style == \"variable\") {\n      var word = stream.current();\n      style = state.lastTok == \".\" ? \"property\"\n        : keywords.propertyIsEnumerable(stream.current()) ? \"keyword\"\n        : /^[A-Z]/.test(word) ? \"tag\"\n        : (state.lastTok == \"def\" || state.lastTok == \"class\" || state.varList) ? \"def\"\n        : \"variable\";\n      if (style == \"keyword\") {\n        thisTok = word;\n        if (indentWords.propertyIsEnumerable(word)) kwtype = \"indent\";\n        else if (dedentWords.propertyIsEnumerable(word)) kwtype = \"dedent\";\n        else if ((word == \"if\" || word == \"unless\") && stream.column() == stream.indentation())\n          kwtype = \"indent\";\n        else if (word == \"do\" && state.context.indented < state.indented)\n          kwtype = \"indent\";\n      }\n    }\n    if (curPunc || (style && style != \"comment\")) state.lastTok = thisTok;\n    if (curPunc == \"|\") state.varList = !state.varList;\n\n    if (kwtype == \"indent\" || /[\\(\\[\\{]/.test(curPunc))\n      state.context = {prev: state.context, type: curPunc || style, indented: state.indented};\n    else if ((kwtype == \"dedent\" || /[\\)\\]\\}]/.test(curPunc)) && state.context.prev)\n      state.context = state.context.prev;\n\n    if (stream.eol())\n      state.continuedLine = (curPunc == \"\\\\\" || style == \"operator\");\n    return style;\n  },\n\n  indent: function(state, textAfter, cx) {\n    if (state.tokenize[state.tokenize.length-1] != tokenBase) return null;\n    var firstChar = textAfter && textAfter.charAt(0);\n    var ct = state.context;\n    var closed = ct.type == closing[firstChar] ||\n        ct.type == \"keyword\" && /^(?:end|until|else|elsif|when|rescue)\\b/.test(textAfter);\n    return ct.indented + (closed ? 0 : cx.unit) +\n      (state.continuedLine ? cx.unit : 0);\n  },\n\n  languageData: {\n    indentOnInput: /^\\s*(?:end|rescue|elsif|else|\\})$/,\n    commentTokens: {line: \"#\"},\n    autocomplete: keywordList\n  }\n};\n\n"], "names": [], "sourceRoot": ""}