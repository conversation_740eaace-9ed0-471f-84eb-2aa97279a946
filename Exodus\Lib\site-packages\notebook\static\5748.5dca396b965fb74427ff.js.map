{"version": 3, "file": "5748.5dca396b965fb74427ff.js?v=5dca396b965fb74427ff", "mappings": ";;;;;;;;;;AAE8B;;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qEAAM;;AAIJ;;;;;;;;;;;;AClB4B;;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,qEAAM;AACV;AACA;AACA;AACA;AACA;;AAIE;;;;;;;;;;;;;;;;;ACrB4B;AAGA;AAKA;AAgBA;;AAE9B;AAC2C;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,8BAA8B,gFAAqB;AACnD,iCAAiC,qEAAM;AACvC,iBAAiB,4EAAa;AAC9B;AACA,OAAO,wEAAS;AAChB,GAAG;AACH;AACA,CAAC;AACD,gBAAgB,yEAAe;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8DAA8D,8CAA8C;AAC5G;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA,SAAS,qEAAM,GAAG,WAAW;AAC7B;AACA,qEAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,qEAAM;AACN,mCAAmC,qEAAM;AACzC;AACA,CAAC;AACD,iCAAiC,qEAAM;AACvC,EAAE,8DAAG;AACL;AACA,oCAAoC;AACpC;AACA;AACA,IAAI;AACJ,IAAI,8DAAG;AACP;AACA,CAAC;AACD,iCAAiC,qEAAM;AACvC;AACA,CAAC;AACD,6BAA6B,qEAAM;AACnC;AACA;AACA;AACA;AACA,EAAE,8DAAG;AACL,EAAE,8DAAG;AACL;AACA,OAAO,yEAAc;AACrB,QAAQ,yEAAc;AACtB,4BAA4B,yEAAc;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,8DAAG;AACL;AACA;AACA,EAAE,8DAAG;AACL,CAAC;AACD,6BAA6B,qEAAM;AACnC;AACA;AACA,SAAS,yEAAc;AACvB;AACA;AACA,kIAAkI,KAAK;AACvI;AACA;AACA;AACA,yCAAyC,aAAa;AACtD;AACA,EAAE,8DAAG;AACL,CAAC;AACD,4BAA4B,qEAAM;AAClC;AACA;AACA;AACA;AACA;AACA,gBAAgB,yEAAc;AAC9B;AACA,eAAe,yEAAc;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA,4CAA4C,YAAY;AACxD;AACA;AACA;AACA;AACA,qBAAqB,YAAY;AACjC,sBAAsB,YAAY;AAClC;AACA;AACA;AACA;AACA;AACA;AACA,qDAAqD,yBAAyB;AAC9E;AACA;AACA,qBAAqB,YAAY;AACjC,sBAAsB,YAAY;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,YAAY;AACjC,sBAAsB,YAAY;AAClC,2BAA2B,YAAY;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,YAAY;AACjC,sBAAsB,YAAY;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,YAAY;AACjC,sBAAsB,YAAY;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,aAAa,EAAE,UAAU,EAAE,cAAc,EAAE,sBAAsB;AACtF,sBAAsB,aAAa,EAAE,UAAU,EAAE,cAAc,EAAE,sBAAsB;AACvF;AACA,iBAAiB,aAAa,EAAE,SAAS,UAAU,cAAc,EAAE,sBAAsB;AACzF;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,kBAAkB,GAAG,QAAQ;AACpD,8BAA8B,aAAa,OAAO,yBAAyB;AAC3E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,8DAAG;AACL,EAAE,8DAAG;AACL,CAAC;AACD,iCAAiC,qEAAM;AACvC;AACA;AACA;AACA;AACA,EAAE,8DAAG;AACL;AACA,aAAa,yEAAc;AAC3B,aAAa,yEAAc;AAC3B,4BAA4B,yEAAc;AAC1C,mBAAmB,yEAAc;AACjC;AACA;AACA;AACA;AACA;AACA,0BAA0B,UAAU,EAAE,SAAS;AAC/C,2BAA2B,UAAU,EAAE,SAAS;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,UAAU,EAAE,SAAS;AACjD,6BAA6B,UAAU,EAAE,SAAS;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6DAA6D,yBAAyB;AACtF;AACA;AACA,4BAA4B,UAAU,EAAE,SAAS;AACjD,6BAA6B,UAAU,EAAE,SAAS;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6DAA6D,yBAAyB;AACtF;AACA;AACA,4BAA4B,UAAU,EAAE,SAAS;AACjD,6BAA6B,UAAU,EAAE,SAAS;AAClD;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,uBAAuB,OAAO,yBAAyB;AACvF;AACA;AACA;AACA;AACA;AACA,uBAAuB,gBAAgB,EAAE,oDAAoD,eAAe,OAAO;AACnH;AACA;AACA;AACA;AACA;AACA,IAAI,8DAAG;AACP,IAAI,8DAAG;AACP;AACA,CAAC;AACD,+BAA+B,qEAAM;AACrC,YAAY,yEAAc;AAC1B;AACA;AACA,qFAAqF,QAAQ;AAC7F;AACA;AACA,wBAAwB,QAAQ;AAChC,yBAAyB,QAAQ;AACjC,2BAA2B,QAAQ;AACnC;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA,qEAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,8DAAG;AACL;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qEAAM;AACN,kCAAkC,qEAAM;AACxC,EAAE,8DAAG;AACL;AACA;AACA,CAAC;AACD,6BAA6B,qEAAM;AACnC;AACA,EAAE,oEAAK;AACP,CAAC;AACD,4CAA4C,qEAAM;AAClD;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B,EAAE;AAC/B;AACA,GAAG,yDAAyD,MAAM,QAAQ,MAAM;AAChF;AACA,CAAC;AACD,kCAAkC,qEAAM;AACxC;AACA,CAAC;AACD,iCAAiC,qEAAM;AACvC;AACA,CAAC;AACD,sCAAsC,qEAAM;AAC5C;AACA;AACA,IAAI,8DAAG;AACP,GAAG;AACH;AACA;AACA,CAAC;AACD,uCAAuC,qEAAM;AAC7C;AACA,CAAC;AACD,mCAAmC,qEAAM;AACzC;AACA,CAAC;AACD,8BAA8B,qEAAM;AACpC;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,aAAa;AACb,mBAAmB;AACnB,mBAAmB;AACnB,iBAAiB;AACjB,iBAAiB;AACjB;;AAEA;AACA,+BAA+B,qEAAM;AACrC,EAAE,8EAAgB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,qCAAqC,qEAAM;AAC3C;AACA,4BAA4B,qEAAM;AAClC,4BAA4B,qEAAM;AAClC,2BAA2B,qEAAM;AACjC,8BAA8B,qEAAM;AACpC,mCAAmC,qEAAM;AACzC;AACA;AACA;AACA;AACA,IAAI;AACJ,IAAI,8DAAG,kCAAkC,gBAAgB;AACzD;AACA,CAAC;AACD,kCAAkC,qEAAM;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,kCAAkC,qEAAM;AACxC;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,iCAAiC,qEAAM;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,oCAAoC,qEAAM;AAC1C;AACA;AACA,CAAC;AACD,yCAAyC,qEAAM;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA,yBAAyB,qEAAM;AAC/B,sBAAsB,mEAAK;AAC3B,IAAI,8DAAG;AACP;AACA,GAAG;AACH;AACA;AACA,UAAU,uBAAuB;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mDAAmD,6BAA6B;AAChF,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,mDAAmD,6BAA6B;AAChF;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,KAAK;AACL,GAAG;AACH;;AAEA;AAC4B;AAC5B,qBAAqB,yEAAU;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B,qEAAM;AACnC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,+BAA+B,qEAAM;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,CAAC;AACD,wCAAwC,qEAAM;AAC9C;AACA;AACA;AACA;AACA,qCAAqC,qEAAM;AAC3C;AACA,IAAI;AACJ,qCAAqC,qEAAM;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,CAAC;AACD,0CAA0C,qEAAM;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,CAAC;AACD,uCAAuC,qEAAM;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA,kDAAkD,IAAI;AACtD;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC,MAAM;AACxC;AACA,GAAG;AACH,CAAC;AACD,2CAA2C,qEAAM;AACjD;AACA;AACA,2DAA2D,WAAW;AACtE;AACA;AACA;AACA,oEAAoE,WAAW;AAC/E;AACA;AACA,CAAC;AACD,8CAA8C,qEAAM;AACpD;AACA;AACA,CAAC;AACD,wCAAwC,qEAAM;AAC9C;AACA;AACA,mDAAmD,WAAW;AAC9D;AACA;AACA;AACA,8BAA8B,MAAM;AACpC,WAAW;AACX,CAAC;AACD,sCAAsC,qEAAM;AAC5C;AACA;AACA,mDAAmD,WAAW;AAC9D;AACA;AACA;AACA,8BAA8B,MAAM;AACpC,CAAC;AACD,uCAAuC,qEAAM;AAC7C;AACA;AACA;AACA,gBAAgB,YAAY,kBAAkB,iCAAiC,EAAE,UAAU;AAC3F;AACA;AACA;AACA,gBAAgB,YAAY,QAAQ,iCAAiC,EAAE,UAAU;AACjF;AACA,IAAI;AACJ,8HAA8H,YAAY,EAAE,UAAU;AACtJ,6JAA6J,YAAY,EAAE,UAAU;AACrL,6JAA6J,YAAY,EAAE,UAAU;AACrL,2MAA2M,YAAY,EAAE,UAAU;AACnO,2MAA2M,YAAY,EAAE,UAAU;AACnO,IAAI;AACJ;AACA;AACA;AACA;AACA,mCAAmC,YAAY,QAAQ,gCAAgC;AACvF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,WAAW,EAAE,YAAY,QAAQ,gCAAgC;AACnF;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,qBAAqB,GAAG,qBAAqB,GAAG,qBAAqB,GAAG,qBAAqB,GAAG,qBAAqB,GAAG,qBAAqB,GAAG,qBAAqB,GAAG,qBAAqB;AAC1M,gCAAgC,WAAW,EAAE,YAAY,QAAQ,gCAAgC;AACjG;AACA;AACA,CAAC;AACD,sCAAsC,qEAAM;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,qCAAqC,qEAAM;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,iBAAiB,qCAAqC;AACtD;AACA;AACA;AACA;AACA;AACA,QAAQ,mCAAmC,GAAG;AAC9C,QAAQ,mCAAmC,GAAG;AAC9C,QAAQ,wDAAwD,GAAG;AACnE,QAAQ,wDAAwD,GAAG;AACnE,QAAQ,wDAAwD,GAAG;AACnE,QAAQ,wDAAwD,GAAG,aAAa;AAChF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,iBAAiB,GAAG;AAC9B,UAAU,iBAAiB,GAAG;AAC9B,UAAU,iCAAiC,GAAG;AAC9C,UAAU,uDAAuD,GAAG;AACpE,UAAU,uDAAuD,GAAG;AACpE,UAAU,iCAAiC,GAAG,iBAAiB;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,yCAAyC,qEAAM;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,wCAAwC,qEAAM;AAC9C,kCAAkC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA,MAAM;AACN;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,CAAC;AACD,wCAAwC,qEAAM;AAC9C;AACA;AACA;AACA;AACA,0DAA0D,WAAW;AACrE;AACA,WAAW;AACX,CAAC;AACD,kCAAkC,qEAAM;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC,qEAAM;AACzC;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kDAAkD,IAAI;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC,sDAAsD;AACxF,MAAM;AACN,kCAAkC,sDAAsD;AACxF;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,CAAC;AACD,yCAAyC,qEAAM;AAC/C;AACA;AACA,+CAA+C,qEAAM;AACrD,2CAA2C,qEAAM;AACjD;AACA;AACA,GAAG;AACH,CAAC;AACD,+BAA+B,qEAAM;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,gCAAgC,qEAAM;AACtC;AACA;AACA;AACA,8DAA8D,YAAY,MAAM,WAAW;AAC3F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,MAAM,EAAE,MAAM,IAAI,gBAAgB,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,eAAe,IAAI,OAAO,EAAE,eAAe,EAAE,KAAK,EAAE,gBAAgB,EAAE,MAAM,IAAI,MAAM,EAAE,KAAK;AACjL,QAAQ;AACR;AACA,uBAAuB,MAAM,EAAE,MAAM,IAAI,gBAAgB,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,eAAe,IAAI,OAAO,EAAE,eAAe,EAAE,MAAM,EAAE,gBAAgB,EAAE,MAAM,IAAI,MAAM,EAAE,KAAK;AACjL;AACA,MAAM;AACN;AACA,uBAAuB,MAAM,EAAE,MAAM,IAAI,gBAAgB,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,eAAe,IAAI,OAAO,EAAE,eAAe,EAAE,MAAM,EAAE,gBAAgB,EAAE,MAAM,IAAI,MAAM,EAAE,KAAK;AACjL,QAAQ;AACR;AACA,uBAAuB,MAAM,EAAE,MAAM,IAAI,gBAAgB,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,eAAe,IAAI,OAAO,EAAE,eAAe,EAAE,KAAK,EAAE,gBAAgB,EAAE,MAAM,IAAI,MAAM,EAAE,KAAK;AACjL;AACA,MAAM;AACN;AACA,uBAAuB,MAAM,EAAE,MAAM,IAAI,MAAM,EAAE,gBAAgB,EAAE,KAAK,EAAE,eAAe,EAAE,OAAO,IAAI,eAAe,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,gBAAgB,IAAI,MAAM,EAAE,KAAK;AACjL,QAAQ;AACR;AACA,uBAAuB,MAAM,EAAE,MAAM,IAAI,MAAM,EAAE,gBAAgB,EAAE,MAAM,EAAE,eAAe,EAAE,OAAO,IAAI,eAAe,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,gBAAgB,IAAI,MAAM,EAAE,KAAK;AACjL;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,MAAM,EAAE,MAAM,IAAI,MAAM,EAAE,eAAe,EAAE,KAAK,EAAE,eAAe,EAAE,MAAM,IAAI,MAAM,EAAE,KAAK;AACnH,UAAU;AACV,yBAAyB,MAAM,EAAE,MAAM,IAAI,eAAe,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,eAAe,IAAI,MAAM,EAAE,KAAK;AACpH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,MAAM,EAAE,MAAM,IAAI,MAAM,EAAE,eAAe,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,IAAI,MAAM,EAAE,KAAK;AACpH,UAAU;AACV,yBAAyB,MAAM,EAAE,MAAM,IAAI,eAAe,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,eAAe,IAAI,MAAM,EAAE,KAAK;AACnH;AACA;AACA;AACA,uBAAuB,MAAM,EAAE,MAAM,IAAI,MAAM,EAAE,KAAK;AACtD;AACA,MAAM;AACN;AACA;AACA,yBAAyB,MAAM,EAAE,MAAM,IAAI,MAAM,EAAE,eAAe,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,IAAI,MAAM,EAAE,KAAK;AACpH,UAAU;AACV,yBAAyB,MAAM,EAAE,MAAM,IAAI,eAAe,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,eAAe,IAAI,MAAM,EAAE,KAAK;AACnH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,MAAM,EAAE,MAAM,IAAI,MAAM,EAAE,eAAe,EAAE,KAAK,EAAE,eAAe,EAAE,MAAM,IAAI,MAAM,EAAE,KAAK;AACnH,UAAU;AACV,yBAAyB,MAAM,EAAE,MAAM,IAAI,eAAe,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,eAAe,IAAI,MAAM,EAAE,KAAK;AACnH;AACA;AACA;AACA,uBAAuB,MAAM,EAAE,MAAM,IAAI,MAAM,EAAE,KAAK;AACtD;AACA,MAAM;AACN;AACA;AACA,yBAAyB,MAAM,EAAE,MAAM,IAAI,eAAe,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,eAAe,IAAI,MAAM,EAAE,KAAK;AACpH,UAAU;AACV,yBAAyB,MAAM,EAAE,MAAM,IAAI,MAAM,EAAE,eAAe,EAAE,KAAK,EAAE,eAAe,EAAE,MAAM,IAAI,MAAM,EAAE,KAAK;AACnH;AACA;AACA;AACA;AACA,yBAAyB,MAAM,EAAE,MAAM,IAAI,eAAe,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,eAAe,IAAI,MAAM,EAAE,KAAK;AACnH,UAAU;AACV,yBAAyB,MAAM,EAAE,MAAM,IAAI,MAAM,EAAE,eAAe,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,IAAI,MAAM,EAAE,KAAK;AACpH;AACA;AACA;AACA,uBAAuB,MAAM,EAAE,MAAM,IAAI,MAAM,EAAE,KAAK;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,iCAAiC,qEAAM;AACvC;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,GAAG;AACH,CAAC;AACD,mCAAmC,qEAAM;AACzC;AACA;AACA;AACA;AACA;AACA,uDAAuD,aAAa;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA,MAAM;AACN;AACA;AACA,GAAG;AACH,CAAC;AACD,wCAAwC,qEAAM;AAC9C,wBAAwB,YAAY;AACpC;AACA;AACA,CAAC;AACD,2BAA2B,qEAAM;AACjC;AACA,EAAE,8DAAG;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,oDAAM,SAAS,GAAG;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,wEAAa;AACf;AACA;AACA;AACA;AACA;AACA,EAAE,iFAAiB;AACnB;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,UAAU,uBAAuB;AACjC;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B,qEAAM;AACpC;AACA;AACA;AACA;AACA;AACA,8CAA8C,kBAAkB;AAChE;AACA;AACA,iDAAiD,eAAe;AAChE,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,8CAA8C,kBAAkB;AAChE;AACA;AACA;AACA,iDAAiD,eAAe;AAChE,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,kBAAkB;AAC9C,+BAA+B,6BAA6B;AAC5D,+BAA+B,6BAA6B;AAC5D;AACA;AACA;AACA;AACA,yBAAyB,gCAAgC;AACzD,sBAAsB,2CAA2C;AACjE,sBAAsB,6CAA6C;AACnE,sBAAsB,kCAAkC;AACxD,sBAAsB,6CAA6C;AACnE,8BAA8B,6CAA6C;AAC3E,sBAAsB,6CAA6C;AACnE,8BAA8B,6CAA6C;AAC3E;AACA;AACA,0DAA0D,IAAI;AAC9D;AACA;AACA;AACA,SAAS;AACT,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA,yBAAyB,gCAAgC;AACzD,sBAAsB,2CAA2C;AACjE,sBAAsB,6CAA6C;AACnE,sBAAsB,kCAAkC;AACxD,sBAAsB,6CAA6C;AACnE,8BAA8B,6CAA6C;AAC3E,sBAAsB,6CAA6C;AACnE,8BAA8B,6CAA6C;AAC3E;AACA;AACA,0DAA0D,IAAI;AAC9D;AACA;AACA;AACA,SAAS;AACT,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4DAA4D,IAAI;AAChE;AACA;AACA,SAAS;AACT,OAAO;AACP,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,aAAa;AACnC,sBAAsB,aAAa;AACnC,sBAAsB,yBAAyB;AAC/C,sBAAsB,yBAAyB;AAC/C,sBAAsB,yBAAyB;AAC/C,sBAAsB,yBAAyB;AAC/C,sBAAsB,cAAc;AACpC,sBAAsB,cAAc;AACpC;AACA;AACA,sBAAsB,cAAc;AACpC,sBAAsB,cAAc;AACpC,sBAAsB,yBAAyB;AAC/C,sBAAsB,wBAAwB;AAC9C,sBAAsB,yBAAyB;AAC/C,sBAAsB,wBAAwB;AAC9C,sBAAsB,cAAc;AACpC,sBAAsB,aAAa;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B,kBAAkB;AAChD,iCAAiC,6BAA6B;AAC9D,iCAAiC,6BAA6B;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B,kBAAkB;AAChD,iCAAiC,6BAA6B;AAC9D,iCAAiC,6BAA6B;AAC9D;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL,GAAG;AACH;AACA;AACA,+BAA+B,YAAY;AAC3C,+BAA+B,YAAY;AAC3C,+BAA+B,YAAY;AAC3C;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA,gCAAgC,qEAAM;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA,uBAAuB,KAAK,QAAQ;AACpC,iBAAiB,KAAK,UAAU,qBAAqB,QAAQ;AAC7D,2BAA2B,KAAK,UAAU,wBAAwB,QAAQ;AAC1E,gBAAgB,MAAM,QAAQ;AAC9B,gBAAgB,KAAK,UAAU;AAC/B;AACA;;AAEA;AACA;AACA,cAAc;AACd;AACA;AACA,kBAAkB,aAAa,8BAA8B,QAAQ;AACrE,sBAAsB,aAAa,8BAA8B,QAAQ,gCAAgC;AACzG,eAAe,aAAa,2BAA2B,QAAQ;AAC/D,mBAAmB,QAAQ,6BAA6B,UAAU;AAClE,cAAc,QAAQ;;AAEtB;AACA,cAAc;AACd,YAAY;AACZ;AACA;AACA,cAAc;AACd,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA,cAAc;AACd,YAAY;AACZ;;AAEA,WAAW,iBAAiB,uBAAuB;AACnD;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAGE", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mermaid/dist/chunks/mermaid.core/chunk-4BMEZGHF.mjs", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mermaid/dist/chunks/mermaid.core/chunk-XZIHB7SX.mjs", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mermaid/dist/chunks/mermaid.core/gitGraphDiagram-7IBYFJ6S.mjs"], "sourcesContent": ["import {\n  __name\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/diagrams/common/populateCommonDb.ts\nfunction populateCommonDb(ast, db) {\n  if (ast.accDescr) {\n    db.setAccDescription?.(ast.accDescr);\n  }\n  if (ast.accTitle) {\n    db.setAccTitle?.(ast.accTitle);\n  }\n  if (ast.title) {\n    db.setDiagramTitle?.(ast.title);\n  }\n}\n__name(populateCommonDb, \"populateCommonDb\");\n\nexport {\n  populateCommonDb\n};\n", "import {\n  __name\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/utils/imperativeState.ts\nvar ImperativeState = class {\n  /**\n   * @param init - Function that creates the default state.\n   */\n  constructor(init) {\n    this.init = init;\n    this.records = this.init();\n  }\n  static {\n    __name(this, \"ImperativeState\");\n  }\n  reset() {\n    this.records = this.init();\n  }\n};\n\nexport {\n  ImperativeState\n};\n", "import {\n  populateCommonDb\n} from \"./chunk-4BMEZGHF.mjs\";\nimport {\n  ImperativeState\n} from \"./chunk-XZIHB7SX.mjs\";\nimport {\n  cleanAndMerge,\n  random,\n  utils_default\n} from \"./chunk-O4NI6UNU.mjs\";\nimport {\n  __name,\n  clear,\n  common_default,\n  defaultConfig_default,\n  getAccDescription,\n  getAccTitle,\n  getConfig,\n  getConfig2,\n  getDiagramTitle,\n  log,\n  setAccDescription,\n  setAccTitle,\n  setDiagramTitle,\n  setupGraphViewbox2 as setupGraphViewbox\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/diagrams/git/gitGraphParser.ts\nimport { parse } from \"@mermaid-js/parser\";\n\n// src/diagrams/git/gitGraphTypes.ts\nvar commitType = {\n  NORMAL: 0,\n  REVERSE: 1,\n  HIGHLIGHT: 2,\n  MERGE: 3,\n  CHERRY_PICK: 4\n};\n\n// src/diagrams/git/gitGraphAst.ts\nvar DEFAULT_GITGRAPH_CONFIG = defaultConfig_default.gitGraph;\nvar getConfig3 = /* @__PURE__ */ __name(() => {\n  const config = cleanAndMerge({\n    ...DEFAULT_GITGRAPH_CONFIG,\n    ...getConfig().gitGraph\n  });\n  return config;\n}, \"getConfig\");\nvar state = new ImperativeState(() => {\n  const config = getConfig3();\n  const mainBranchName = config.mainBranchName;\n  const mainBranchOrder = config.mainBranchOrder;\n  return {\n    mainBranchName,\n    commits: /* @__PURE__ */ new Map(),\n    head: null,\n    branchConfig: /* @__PURE__ */ new Map([[mainBranchName, { name: mainBranchName, order: mainBranchOrder }]]),\n    branches: /* @__PURE__ */ new Map([[mainBranchName, null]]),\n    currBranch: mainBranchName,\n    direction: \"LR\",\n    seq: 0,\n    options: {}\n  };\n});\nfunction getID() {\n  return random({ length: 7 });\n}\n__name(getID, \"getID\");\nfunction uniqBy(list, fn) {\n  const recordMap = /* @__PURE__ */ Object.create(null);\n  return list.reduce((out, item) => {\n    const key = fn(item);\n    if (!recordMap[key]) {\n      recordMap[key] = true;\n      out.push(item);\n    }\n    return out;\n  }, []);\n}\n__name(uniqBy, \"uniqBy\");\nvar setDirection = /* @__PURE__ */ __name(function(dir2) {\n  state.records.direction = dir2;\n}, \"setDirection\");\nvar setOptions = /* @__PURE__ */ __name(function(rawOptString) {\n  log.debug(\"options str\", rawOptString);\n  rawOptString = rawOptString?.trim();\n  rawOptString = rawOptString || \"{}\";\n  try {\n    state.records.options = JSON.parse(rawOptString);\n  } catch (e) {\n    log.error(\"error while parsing gitGraph options\", e.message);\n  }\n}, \"setOptions\");\nvar getOptions = /* @__PURE__ */ __name(function() {\n  return state.records.options;\n}, \"getOptions\");\nvar commit = /* @__PURE__ */ __name(function(commitDB) {\n  let msg = commitDB.msg;\n  let id = commitDB.id;\n  const type = commitDB.type;\n  let tags = commitDB.tags;\n  log.info(\"commit\", msg, id, type, tags);\n  log.debug(\"Entering commit:\", msg, id, type, tags);\n  const config = getConfig3();\n  id = common_default.sanitizeText(id, config);\n  msg = common_default.sanitizeText(msg, config);\n  tags = tags?.map((tag) => common_default.sanitizeText(tag, config));\n  const newCommit = {\n    id: id ? id : state.records.seq + \"-\" + getID(),\n    message: msg,\n    seq: state.records.seq++,\n    type: type ?? commitType.NORMAL,\n    tags: tags ?? [],\n    parents: state.records.head == null ? [] : [state.records.head.id],\n    branch: state.records.currBranch\n  };\n  state.records.head = newCommit;\n  log.info(\"main branch\", config.mainBranchName);\n  state.records.commits.set(newCommit.id, newCommit);\n  state.records.branches.set(state.records.currBranch, newCommit.id);\n  log.debug(\"in pushCommit \" + newCommit.id);\n}, \"commit\");\nvar branch = /* @__PURE__ */ __name(function(branchDB) {\n  let name = branchDB.name;\n  const order = branchDB.order;\n  name = common_default.sanitizeText(name, getConfig3());\n  if (state.records.branches.has(name)) {\n    throw new Error(\n      `Trying to create an existing branch. (Help: Either use a new name if you want create a new branch or try using \"checkout ${name}\")`\n    );\n  }\n  state.records.branches.set(name, state.records.head != null ? state.records.head.id : null);\n  state.records.branchConfig.set(name, { name, order });\n  checkout(name);\n  log.debug(\"in createBranch\");\n}, \"branch\");\nvar merge = /* @__PURE__ */ __name((mergeDB) => {\n  let otherBranch = mergeDB.branch;\n  let customId = mergeDB.id;\n  const overrideType = mergeDB.type;\n  const customTags = mergeDB.tags;\n  const config = getConfig3();\n  otherBranch = common_default.sanitizeText(otherBranch, config);\n  if (customId) {\n    customId = common_default.sanitizeText(customId, config);\n  }\n  const currentBranchCheck = state.records.branches.get(state.records.currBranch);\n  const otherBranchCheck = state.records.branches.get(otherBranch);\n  const currentCommit = currentBranchCheck ? state.records.commits.get(currentBranchCheck) : void 0;\n  const otherCommit = otherBranchCheck ? state.records.commits.get(otherBranchCheck) : void 0;\n  if (currentCommit && otherCommit && currentCommit.branch === otherBranch) {\n    throw new Error(`Cannot merge branch '${otherBranch}' into itself.`);\n  }\n  if (state.records.currBranch === otherBranch) {\n    const error = new Error('Incorrect usage of \"merge\". Cannot merge a branch to itself');\n    error.hash = {\n      text: `merge ${otherBranch}`,\n      token: `merge ${otherBranch}`,\n      expected: [\"branch abc\"]\n    };\n    throw error;\n  }\n  if (currentCommit === void 0 || !currentCommit) {\n    const error = new Error(\n      `Incorrect usage of \"merge\". Current branch (${state.records.currBranch})has no commits`\n    );\n    error.hash = {\n      text: `merge ${otherBranch}`,\n      token: `merge ${otherBranch}`,\n      expected: [\"commit\"]\n    };\n    throw error;\n  }\n  if (!state.records.branches.has(otherBranch)) {\n    const error = new Error(\n      'Incorrect usage of \"merge\". Branch to be merged (' + otherBranch + \") does not exist\"\n    );\n    error.hash = {\n      text: `merge ${otherBranch}`,\n      token: `merge ${otherBranch}`,\n      expected: [`branch ${otherBranch}`]\n    };\n    throw error;\n  }\n  if (otherCommit === void 0 || !otherCommit) {\n    const error = new Error(\n      'Incorrect usage of \"merge\". Branch to be merged (' + otherBranch + \") has no commits\"\n    );\n    error.hash = {\n      text: `merge ${otherBranch}`,\n      token: `merge ${otherBranch}`,\n      expected: ['\"commit\"']\n    };\n    throw error;\n  }\n  if (currentCommit === otherCommit) {\n    const error = new Error('Incorrect usage of \"merge\". Both branches have same head');\n    error.hash = {\n      text: `merge ${otherBranch}`,\n      token: `merge ${otherBranch}`,\n      expected: [\"branch abc\"]\n    };\n    throw error;\n  }\n  if (customId && state.records.commits.has(customId)) {\n    const error = new Error(\n      'Incorrect usage of \"merge\". Commit with id:' + customId + \" already exists, use different custom Id\"\n    );\n    error.hash = {\n      text: `merge ${otherBranch} ${customId} ${overrideType} ${customTags?.join(\" \")}`,\n      token: `merge ${otherBranch} ${customId} ${overrideType} ${customTags?.join(\" \")}`,\n      expected: [\n        `merge ${otherBranch} ${customId}_UNIQUE ${overrideType} ${customTags?.join(\" \")}`\n      ]\n    };\n    throw error;\n  }\n  const verifiedBranch = otherBranchCheck ? otherBranchCheck : \"\";\n  const commit2 = {\n    id: customId || `${state.records.seq}-${getID()}`,\n    message: `merged branch ${otherBranch} into ${state.records.currBranch}`,\n    seq: state.records.seq++,\n    parents: state.records.head == null ? [] : [state.records.head.id, verifiedBranch],\n    branch: state.records.currBranch,\n    type: commitType.MERGE,\n    customType: overrideType,\n    customId: customId ? true : false,\n    tags: customTags ?? []\n  };\n  state.records.head = commit2;\n  state.records.commits.set(commit2.id, commit2);\n  state.records.branches.set(state.records.currBranch, commit2.id);\n  log.debug(state.records.branches);\n  log.debug(\"in mergeBranch\");\n}, \"merge\");\nvar cherryPick = /* @__PURE__ */ __name(function(cherryPickDB) {\n  let sourceId = cherryPickDB.id;\n  let targetId = cherryPickDB.targetId;\n  let tags = cherryPickDB.tags;\n  let parentCommitId = cherryPickDB.parent;\n  log.debug(\"Entering cherryPick:\", sourceId, targetId, tags);\n  const config = getConfig3();\n  sourceId = common_default.sanitizeText(sourceId, config);\n  targetId = common_default.sanitizeText(targetId, config);\n  tags = tags?.map((tag) => common_default.sanitizeText(tag, config));\n  parentCommitId = common_default.sanitizeText(parentCommitId, config);\n  if (!sourceId || !state.records.commits.has(sourceId)) {\n    const error = new Error(\n      'Incorrect usage of \"cherryPick\". Source commit id should exist and provided'\n    );\n    error.hash = {\n      text: `cherryPick ${sourceId} ${targetId}`,\n      token: `cherryPick ${sourceId} ${targetId}`,\n      expected: [\"cherry-pick abc\"]\n    };\n    throw error;\n  }\n  const sourceCommit = state.records.commits.get(sourceId);\n  if (sourceCommit === void 0 || !sourceCommit) {\n    throw new Error('Incorrect usage of \"cherryPick\". Source commit id should exist and provided');\n  }\n  if (parentCommitId && !(Array.isArray(sourceCommit.parents) && sourceCommit.parents.includes(parentCommitId))) {\n    const error = new Error(\n      \"Invalid operation: The specified parent commit is not an immediate parent of the cherry-picked commit.\"\n    );\n    throw error;\n  }\n  const sourceCommitBranch = sourceCommit.branch;\n  if (sourceCommit.type === commitType.MERGE && !parentCommitId) {\n    const error = new Error(\n      \"Incorrect usage of cherry-pick: If the source commit is a merge commit, an immediate parent commit must be specified.\"\n    );\n    throw error;\n  }\n  if (!targetId || !state.records.commits.has(targetId)) {\n    if (sourceCommitBranch === state.records.currBranch) {\n      const error = new Error(\n        'Incorrect usage of \"cherryPick\". Source commit is already on current branch'\n      );\n      error.hash = {\n        text: `cherryPick ${sourceId} ${targetId}`,\n        token: `cherryPick ${sourceId} ${targetId}`,\n        expected: [\"cherry-pick abc\"]\n      };\n      throw error;\n    }\n    const currentCommitId = state.records.branches.get(state.records.currBranch);\n    if (currentCommitId === void 0 || !currentCommitId) {\n      const error = new Error(\n        `Incorrect usage of \"cherry-pick\". Current branch (${state.records.currBranch})has no commits`\n      );\n      error.hash = {\n        text: `cherryPick ${sourceId} ${targetId}`,\n        token: `cherryPick ${sourceId} ${targetId}`,\n        expected: [\"cherry-pick abc\"]\n      };\n      throw error;\n    }\n    const currentCommit = state.records.commits.get(currentCommitId);\n    if (currentCommit === void 0 || !currentCommit) {\n      const error = new Error(\n        `Incorrect usage of \"cherry-pick\". Current branch (${state.records.currBranch})has no commits`\n      );\n      error.hash = {\n        text: `cherryPick ${sourceId} ${targetId}`,\n        token: `cherryPick ${sourceId} ${targetId}`,\n        expected: [\"cherry-pick abc\"]\n      };\n      throw error;\n    }\n    const commit2 = {\n      id: state.records.seq + \"-\" + getID(),\n      message: `cherry-picked ${sourceCommit?.message} into ${state.records.currBranch}`,\n      seq: state.records.seq++,\n      parents: state.records.head == null ? [] : [state.records.head.id, sourceCommit.id],\n      branch: state.records.currBranch,\n      type: commitType.CHERRY_PICK,\n      tags: tags ? tags.filter(Boolean) : [\n        `cherry-pick:${sourceCommit.id}${sourceCommit.type === commitType.MERGE ? `|parent:${parentCommitId}` : \"\"}`\n      ]\n    };\n    state.records.head = commit2;\n    state.records.commits.set(commit2.id, commit2);\n    state.records.branches.set(state.records.currBranch, commit2.id);\n    log.debug(state.records.branches);\n    log.debug(\"in cherryPick\");\n  }\n}, \"cherryPick\");\nvar checkout = /* @__PURE__ */ __name(function(branch2) {\n  branch2 = common_default.sanitizeText(branch2, getConfig3());\n  if (!state.records.branches.has(branch2)) {\n    const error = new Error(\n      `Trying to checkout branch which is not yet created. (Help try using \"branch ${branch2}\")`\n    );\n    error.hash = {\n      text: `checkout ${branch2}`,\n      token: `checkout ${branch2}`,\n      expected: [`branch ${branch2}`]\n    };\n    throw error;\n  } else {\n    state.records.currBranch = branch2;\n    const id = state.records.branches.get(state.records.currBranch);\n    if (id === void 0 || !id) {\n      state.records.head = null;\n    } else {\n      state.records.head = state.records.commits.get(id) ?? null;\n    }\n  }\n}, \"checkout\");\nfunction upsert(arr, key, newVal) {\n  const index = arr.indexOf(key);\n  if (index === -1) {\n    arr.push(newVal);\n  } else {\n    arr.splice(index, 1, newVal);\n  }\n}\n__name(upsert, \"upsert\");\nfunction prettyPrintCommitHistory(commitArr) {\n  const commit2 = commitArr.reduce((out, commit3) => {\n    if (out.seq > commit3.seq) {\n      return out;\n    }\n    return commit3;\n  }, commitArr[0]);\n  let line = \"\";\n  commitArr.forEach(function(c) {\n    if (c === commit2) {\n      line += \"\t*\";\n    } else {\n      line += \"\t|\";\n    }\n  });\n  const label = [line, commit2.id, commit2.seq];\n  for (const branch2 in state.records.branches) {\n    if (state.records.branches.get(branch2) === commit2.id) {\n      label.push(branch2);\n    }\n  }\n  log.debug(label.join(\" \"));\n  if (commit2.parents && commit2.parents.length == 2 && commit2.parents[0] && commit2.parents[1]) {\n    const newCommit = state.records.commits.get(commit2.parents[0]);\n    upsert(commitArr, commit2, newCommit);\n    if (commit2.parents[1]) {\n      commitArr.push(state.records.commits.get(commit2.parents[1]));\n    }\n  } else if (commit2.parents.length == 0) {\n    return;\n  } else {\n    if (commit2.parents[0]) {\n      const newCommit = state.records.commits.get(commit2.parents[0]);\n      upsert(commitArr, commit2, newCommit);\n    }\n  }\n  commitArr = uniqBy(commitArr, (c) => c.id);\n  prettyPrintCommitHistory(commitArr);\n}\n__name(prettyPrintCommitHistory, \"prettyPrintCommitHistory\");\nvar prettyPrint = /* @__PURE__ */ __name(function() {\n  log.debug(state.records.commits);\n  const node = getCommitsArray()[0];\n  prettyPrintCommitHistory([node]);\n}, \"prettyPrint\");\nvar clear2 = /* @__PURE__ */ __name(function() {\n  state.reset();\n  clear();\n}, \"clear\");\nvar getBranchesAsObjArray = /* @__PURE__ */ __name(function() {\n  const branchesArray = [...state.records.branchConfig.values()].map((branchConfig, i) => {\n    if (branchConfig.order !== null && branchConfig.order !== void 0) {\n      return branchConfig;\n    }\n    return {\n      ...branchConfig,\n      order: parseFloat(`0.${i}`)\n    };\n  }).sort((a, b) => (a.order ?? 0) - (b.order ?? 0)).map(({ name }) => ({ name }));\n  return branchesArray;\n}, \"getBranchesAsObjArray\");\nvar getBranches = /* @__PURE__ */ __name(function() {\n  return state.records.branches;\n}, \"getBranches\");\nvar getCommits = /* @__PURE__ */ __name(function() {\n  return state.records.commits;\n}, \"getCommits\");\nvar getCommitsArray = /* @__PURE__ */ __name(function() {\n  const commitArr = [...state.records.commits.values()];\n  commitArr.forEach(function(o) {\n    log.debug(o.id);\n  });\n  commitArr.sort((a, b) => a.seq - b.seq);\n  return commitArr;\n}, \"getCommitsArray\");\nvar getCurrentBranch = /* @__PURE__ */ __name(function() {\n  return state.records.currBranch;\n}, \"getCurrentBranch\");\nvar getDirection = /* @__PURE__ */ __name(function() {\n  return state.records.direction;\n}, \"getDirection\");\nvar getHead = /* @__PURE__ */ __name(function() {\n  return state.records.head;\n}, \"getHead\");\nvar db = {\n  commitType,\n  getConfig: getConfig3,\n  setDirection,\n  setOptions,\n  getOptions,\n  commit,\n  branch,\n  merge,\n  cherryPick,\n  checkout,\n  //reset,\n  prettyPrint,\n  clear: clear2,\n  getBranchesAsObjArray,\n  getBranches,\n  getCommits,\n  getCommitsArray,\n  getCurrentBranch,\n  getDirection,\n  getHead,\n  setAccTitle,\n  getAccTitle,\n  getAccDescription,\n  setAccDescription,\n  setDiagramTitle,\n  getDiagramTitle\n};\n\n// src/diagrams/git/gitGraphParser.ts\nvar populate = /* @__PURE__ */ __name((ast, db2) => {\n  populateCommonDb(ast, db2);\n  if (ast.dir) {\n    db2.setDirection(ast.dir);\n  }\n  for (const statement of ast.statements) {\n    parseStatement(statement, db2);\n  }\n}, \"populate\");\nvar parseStatement = /* @__PURE__ */ __name((statement, db2) => {\n  const parsers = {\n    Commit: /* @__PURE__ */ __name((stmt) => db2.commit(parseCommit(stmt)), \"Commit\"),\n    Branch: /* @__PURE__ */ __name((stmt) => db2.branch(parseBranch(stmt)), \"Branch\"),\n    Merge: /* @__PURE__ */ __name((stmt) => db2.merge(parseMerge(stmt)), \"Merge\"),\n    Checkout: /* @__PURE__ */ __name((stmt) => db2.checkout(parseCheckout(stmt)), \"Checkout\"),\n    CherryPicking: /* @__PURE__ */ __name((stmt) => db2.cherryPick(parseCherryPicking(stmt)), \"CherryPicking\")\n  };\n  const parser2 = parsers[statement.$type];\n  if (parser2) {\n    parser2(statement);\n  } else {\n    log.error(`Unknown statement type: ${statement.$type}`);\n  }\n}, \"parseStatement\");\nvar parseCommit = /* @__PURE__ */ __name((commit2) => {\n  const commitDB = {\n    id: commit2.id,\n    msg: commit2.message ?? \"\",\n    type: commit2.type !== void 0 ? commitType[commit2.type] : commitType.NORMAL,\n    tags: commit2.tags ?? void 0\n  };\n  return commitDB;\n}, \"parseCommit\");\nvar parseBranch = /* @__PURE__ */ __name((branch2) => {\n  const branchDB = {\n    name: branch2.name,\n    order: branch2.order ?? 0\n  };\n  return branchDB;\n}, \"parseBranch\");\nvar parseMerge = /* @__PURE__ */ __name((merge2) => {\n  const mergeDB = {\n    branch: merge2.branch,\n    id: merge2.id ?? \"\",\n    type: merge2.type !== void 0 ? commitType[merge2.type] : void 0,\n    tags: merge2.tags ?? void 0\n  };\n  return mergeDB;\n}, \"parseMerge\");\nvar parseCheckout = /* @__PURE__ */ __name((checkout2) => {\n  const branch2 = checkout2.branch;\n  return branch2;\n}, \"parseCheckout\");\nvar parseCherryPicking = /* @__PURE__ */ __name((cherryPicking) => {\n  const cherryPickDB = {\n    id: cherryPicking.id,\n    targetId: \"\",\n    tags: cherryPicking.tags?.length === 0 ? void 0 : cherryPicking.tags,\n    parent: cherryPicking.parent\n  };\n  return cherryPickDB;\n}, \"parseCherryPicking\");\nvar parser = {\n  parse: /* @__PURE__ */ __name(async (input) => {\n    const ast = await parse(\"gitGraph\", input);\n    log.debug(ast);\n    populate(ast, db);\n  }, \"parse\")\n};\nif (void 0) {\n  const { it, expect, describe } = void 0;\n  const mockDB = {\n    commitType,\n    setDirection: vi.fn(),\n    commit: vi.fn(),\n    branch: vi.fn(),\n    merge: vi.fn(),\n    cherryPick: vi.fn(),\n    checkout: vi.fn()\n  };\n  describe(\"GitGraph Parser\", () => {\n    it(\"should parse a commit statement\", () => {\n      const commit2 = {\n        $type: \"Commit\",\n        id: \"1\",\n        message: \"test\",\n        tags: [\"tag1\", \"tag2\"],\n        type: \"NORMAL\"\n      };\n      parseStatement(commit2, mockDB);\n      expect(mockDB.commit).toHaveBeenCalledWith({\n        id: \"1\",\n        msg: \"test\",\n        tags: [\"tag1\", \"tag2\"],\n        type: 0\n      });\n    });\n    it(\"should parse a branch statement\", () => {\n      const branch2 = {\n        $type: \"Branch\",\n        name: \"newBranch\",\n        order: 1\n      };\n      parseStatement(branch2, mockDB);\n      expect(mockDB.branch).toHaveBeenCalledWith({ name: \"newBranch\", order: 1 });\n    });\n    it(\"should parse a checkout statement\", () => {\n      const checkout2 = {\n        $type: \"Checkout\",\n        branch: \"newBranch\"\n      };\n      parseStatement(checkout2, mockDB);\n      expect(mockDB.checkout).toHaveBeenCalledWith(\"newBranch\");\n    });\n    it(\"should parse a merge statement\", () => {\n      const merge2 = {\n        $type: \"Merge\",\n        branch: \"newBranch\",\n        id: \"1\",\n        tags: [\"tag1\", \"tag2\"],\n        type: \"NORMAL\"\n      };\n      parseStatement(merge2, mockDB);\n      expect(mockDB.merge).toHaveBeenCalledWith({\n        branch: \"newBranch\",\n        id: \"1\",\n        tags: [\"tag1\", \"tag2\"],\n        type: 0\n      });\n    });\n    it(\"should parse a cherry picking statement\", () => {\n      const cherryPick2 = {\n        $type: \"CherryPicking\",\n        id: \"1\",\n        tags: [\"tag1\", \"tag2\"],\n        parent: \"2\"\n      };\n      parseStatement(cherryPick2, mockDB);\n      expect(mockDB.cherryPick).toHaveBeenCalledWith({\n        id: \"1\",\n        targetId: \"\",\n        parent: \"2\",\n        tags: [\"tag1\", \"tag2\"]\n      });\n    });\n    it(\"should parse a langium generated gitGraph ast\", () => {\n      const dummy = {\n        $type: \"GitGraph\",\n        statements: []\n      };\n      const gitGraphAst = {\n        $type: \"GitGraph\",\n        statements: [\n          {\n            $container: dummy,\n            $type: \"Commit\",\n            id: \"1\",\n            message: \"test\",\n            tags: [\"tag1\", \"tag2\"],\n            type: \"NORMAL\"\n          },\n          {\n            $container: dummy,\n            $type: \"Branch\",\n            name: \"newBranch\",\n            order: 1\n          },\n          {\n            $container: dummy,\n            $type: \"Merge\",\n            branch: \"newBranch\",\n            id: \"1\",\n            tags: [\"tag1\", \"tag2\"],\n            type: \"NORMAL\"\n          },\n          {\n            $container: dummy,\n            $type: \"Checkout\",\n            branch: \"newBranch\"\n          },\n          {\n            $container: dummy,\n            $type: \"CherryPicking\",\n            id: \"1\",\n            tags: [\"tag1\", \"tag2\"],\n            parent: \"2\"\n          }\n        ]\n      };\n      populate(gitGraphAst, mockDB);\n      expect(mockDB.commit).toHaveBeenCalledWith({\n        id: \"1\",\n        msg: \"test\",\n        tags: [\"tag1\", \"tag2\"],\n        type: 0\n      });\n      expect(mockDB.branch).toHaveBeenCalledWith({ name: \"newBranch\", order: 1 });\n      expect(mockDB.merge).toHaveBeenCalledWith({\n        branch: \"newBranch\",\n        id: \"1\",\n        tags: [\"tag1\", \"tag2\"],\n        type: 0\n      });\n      expect(mockDB.checkout).toHaveBeenCalledWith(\"newBranch\");\n    });\n  });\n}\n\n// src/diagrams/git/gitGraphRenderer.ts\nimport { select } from \"d3\";\nvar DEFAULT_CONFIG = getConfig2();\nvar DEFAULT_GITGRAPH_CONFIG2 = DEFAULT_CONFIG?.gitGraph;\nvar LAYOUT_OFFSET = 10;\nvar COMMIT_STEP = 40;\nvar PX = 4;\nvar PY = 2;\nvar THEME_COLOR_LIMIT = 8;\nvar branchPos = /* @__PURE__ */ new Map();\nvar commitPos = /* @__PURE__ */ new Map();\nvar defaultPos = 30;\nvar allCommitsDict = /* @__PURE__ */ new Map();\nvar lanes = [];\nvar maxPos = 0;\nvar dir = \"LR\";\nvar clear3 = /* @__PURE__ */ __name(() => {\n  branchPos.clear();\n  commitPos.clear();\n  allCommitsDict.clear();\n  maxPos = 0;\n  lanes = [];\n  dir = \"LR\";\n}, \"clear\");\nvar drawText = /* @__PURE__ */ __name((txt) => {\n  const svgLabel = document.createElementNS(\"http://www.w3.org/2000/svg\", \"text\");\n  const rows = typeof txt === \"string\" ? txt.split(/\\\\n|\\n|<br\\s*\\/?>/gi) : txt;\n  rows.forEach((row) => {\n    const tspan = document.createElementNS(\"http://www.w3.org/2000/svg\", \"tspan\");\n    tspan.setAttributeNS(\"http://www.w3.org/XML/1998/namespace\", \"xml:space\", \"preserve\");\n    tspan.setAttribute(\"dy\", \"1em\");\n    tspan.setAttribute(\"x\", \"0\");\n    tspan.setAttribute(\"class\", \"row\");\n    tspan.textContent = row.trim();\n    svgLabel.appendChild(tspan);\n  });\n  return svgLabel;\n}, \"drawText\");\nvar findClosestParent = /* @__PURE__ */ __name((parents) => {\n  let closestParent;\n  let comparisonFunc;\n  let targetPosition;\n  if (dir === \"BT\") {\n    comparisonFunc = /* @__PURE__ */ __name((a, b) => a <= b, \"comparisonFunc\");\n    targetPosition = Infinity;\n  } else {\n    comparisonFunc = /* @__PURE__ */ __name((a, b) => a >= b, \"comparisonFunc\");\n    targetPosition = 0;\n  }\n  parents.forEach((parent) => {\n    const parentPosition = dir === \"TB\" || dir == \"BT\" ? commitPos.get(parent)?.y : commitPos.get(parent)?.x;\n    if (parentPosition !== void 0 && comparisonFunc(parentPosition, targetPosition)) {\n      closestParent = parent;\n      targetPosition = parentPosition;\n    }\n  });\n  return closestParent;\n}, \"findClosestParent\");\nvar findClosestParentBT = /* @__PURE__ */ __name((parents) => {\n  let closestParent = \"\";\n  let maxPosition = Infinity;\n  parents.forEach((parent) => {\n    const parentPosition = commitPos.get(parent).y;\n    if (parentPosition <= maxPosition) {\n      closestParent = parent;\n      maxPosition = parentPosition;\n    }\n  });\n  return closestParent || void 0;\n}, \"findClosestParentBT\");\nvar setParallelBTPos = /* @__PURE__ */ __name((sortedKeys, commits, defaultPos2) => {\n  let curPos = defaultPos2;\n  let maxPosition = defaultPos2;\n  const roots = [];\n  sortedKeys.forEach((key) => {\n    const commit2 = commits.get(key);\n    if (!commit2) {\n      throw new Error(`Commit not found for key ${key}`);\n    }\n    if (commit2.parents.length) {\n      curPos = calculateCommitPosition(commit2);\n      maxPosition = Math.max(curPos, maxPosition);\n    } else {\n      roots.push(commit2);\n    }\n    setCommitPosition(commit2, curPos);\n  });\n  curPos = maxPosition;\n  roots.forEach((commit2) => {\n    setRootPosition(commit2, curPos, defaultPos2);\n  });\n  sortedKeys.forEach((key) => {\n    const commit2 = commits.get(key);\n    if (commit2?.parents.length) {\n      const closestParent = findClosestParentBT(commit2.parents);\n      curPos = commitPos.get(closestParent).y - COMMIT_STEP;\n      if (curPos <= maxPosition) {\n        maxPosition = curPos;\n      }\n      const x = branchPos.get(commit2.branch).pos;\n      const y = curPos - LAYOUT_OFFSET;\n      commitPos.set(commit2.id, { x, y });\n    }\n  });\n}, \"setParallelBTPos\");\nvar findClosestParentPos = /* @__PURE__ */ __name((commit2) => {\n  const closestParent = findClosestParent(commit2.parents.filter((p) => p !== null));\n  if (!closestParent) {\n    throw new Error(`Closest parent not found for commit ${commit2.id}`);\n  }\n  const closestParentPos = commitPos.get(closestParent)?.y;\n  if (closestParentPos === void 0) {\n    throw new Error(`Closest parent position not found for commit ${commit2.id}`);\n  }\n  return closestParentPos;\n}, \"findClosestParentPos\");\nvar calculateCommitPosition = /* @__PURE__ */ __name((commit2) => {\n  const closestParentPos = findClosestParentPos(commit2);\n  return closestParentPos + COMMIT_STEP;\n}, \"calculateCommitPosition\");\nvar setCommitPosition = /* @__PURE__ */ __name((commit2, curPos) => {\n  const branch2 = branchPos.get(commit2.branch);\n  if (!branch2) {\n    throw new Error(`Branch not found for commit ${commit2.id}`);\n  }\n  const x = branch2.pos;\n  const y = curPos + LAYOUT_OFFSET;\n  commitPos.set(commit2.id, { x, y });\n  return { x, y };\n}, \"setCommitPosition\");\nvar setRootPosition = /* @__PURE__ */ __name((commit2, curPos, defaultPos2) => {\n  const branch2 = branchPos.get(commit2.branch);\n  if (!branch2) {\n    throw new Error(`Branch not found for commit ${commit2.id}`);\n  }\n  const y = curPos + defaultPos2;\n  const x = branch2.pos;\n  commitPos.set(commit2.id, { x, y });\n}, \"setRootPosition\");\nvar drawCommitBullet = /* @__PURE__ */ __name((gBullets, commit2, commitPosition, typeClass, branchIndex, commitSymbolType) => {\n  if (commitSymbolType === commitType.HIGHLIGHT) {\n    gBullets.append(\"rect\").attr(\"x\", commitPosition.x - 10).attr(\"y\", commitPosition.y - 10).attr(\"width\", 20).attr(\"height\", 20).attr(\n      \"class\",\n      `commit ${commit2.id} commit-highlight${branchIndex % THEME_COLOR_LIMIT} ${typeClass}-outer`\n    );\n    gBullets.append(\"rect\").attr(\"x\", commitPosition.x - 6).attr(\"y\", commitPosition.y - 6).attr(\"width\", 12).attr(\"height\", 12).attr(\n      \"class\",\n      `commit ${commit2.id} commit${branchIndex % THEME_COLOR_LIMIT} ${typeClass}-inner`\n    );\n  } else if (commitSymbolType === commitType.CHERRY_PICK) {\n    gBullets.append(\"circle\").attr(\"cx\", commitPosition.x).attr(\"cy\", commitPosition.y).attr(\"r\", 10).attr(\"class\", `commit ${commit2.id} ${typeClass}`);\n    gBullets.append(\"circle\").attr(\"cx\", commitPosition.x - 3).attr(\"cy\", commitPosition.y + 2).attr(\"r\", 2.75).attr(\"fill\", \"#fff\").attr(\"class\", `commit ${commit2.id} ${typeClass}`);\n    gBullets.append(\"circle\").attr(\"cx\", commitPosition.x + 3).attr(\"cy\", commitPosition.y + 2).attr(\"r\", 2.75).attr(\"fill\", \"#fff\").attr(\"class\", `commit ${commit2.id} ${typeClass}`);\n    gBullets.append(\"line\").attr(\"x1\", commitPosition.x + 3).attr(\"y1\", commitPosition.y + 1).attr(\"x2\", commitPosition.x).attr(\"y2\", commitPosition.y - 5).attr(\"stroke\", \"#fff\").attr(\"class\", `commit ${commit2.id} ${typeClass}`);\n    gBullets.append(\"line\").attr(\"x1\", commitPosition.x - 3).attr(\"y1\", commitPosition.y + 1).attr(\"x2\", commitPosition.x).attr(\"y2\", commitPosition.y - 5).attr(\"stroke\", \"#fff\").attr(\"class\", `commit ${commit2.id} ${typeClass}`);\n  } else {\n    const circle = gBullets.append(\"circle\");\n    circle.attr(\"cx\", commitPosition.x);\n    circle.attr(\"cy\", commitPosition.y);\n    circle.attr(\"r\", commit2.type === commitType.MERGE ? 9 : 10);\n    circle.attr(\"class\", `commit ${commit2.id} commit${branchIndex % THEME_COLOR_LIMIT}`);\n    if (commitSymbolType === commitType.MERGE) {\n      const circle2 = gBullets.append(\"circle\");\n      circle2.attr(\"cx\", commitPosition.x);\n      circle2.attr(\"cy\", commitPosition.y);\n      circle2.attr(\"r\", 6);\n      circle2.attr(\n        \"class\",\n        `commit ${typeClass} ${commit2.id} commit${branchIndex % THEME_COLOR_LIMIT}`\n      );\n    }\n    if (commitSymbolType === commitType.REVERSE) {\n      const cross = gBullets.append(\"path\");\n      cross.attr(\n        \"d\",\n        `M ${commitPosition.x - 5},${commitPosition.y - 5}L${commitPosition.x + 5},${commitPosition.y + 5}M${commitPosition.x - 5},${commitPosition.y + 5}L${commitPosition.x + 5},${commitPosition.y - 5}`\n      ).attr(\"class\", `commit ${typeClass} ${commit2.id} commit${branchIndex % THEME_COLOR_LIMIT}`);\n    }\n  }\n}, \"drawCommitBullet\");\nvar drawCommitLabel = /* @__PURE__ */ __name((gLabels, commit2, commitPosition, pos) => {\n  if (commit2.type !== commitType.CHERRY_PICK && (commit2.customId && commit2.type === commitType.MERGE || commit2.type !== commitType.MERGE) && DEFAULT_GITGRAPH_CONFIG2?.showCommitLabel) {\n    const wrapper = gLabels.append(\"g\");\n    const labelBkg = wrapper.insert(\"rect\").attr(\"class\", \"commit-label-bkg\");\n    const text = wrapper.append(\"text\").attr(\"x\", pos).attr(\"y\", commitPosition.y + 25).attr(\"class\", \"commit-label\").text(commit2.id);\n    const bbox = text.node()?.getBBox();\n    if (bbox) {\n      labelBkg.attr(\"x\", commitPosition.posWithOffset - bbox.width / 2 - PY).attr(\"y\", commitPosition.y + 13.5).attr(\"width\", bbox.width + 2 * PY).attr(\"height\", bbox.height + 2 * PY);\n      if (dir === \"TB\" || dir === \"BT\") {\n        labelBkg.attr(\"x\", commitPosition.x - (bbox.width + 4 * PX + 5)).attr(\"y\", commitPosition.y - 12);\n        text.attr(\"x\", commitPosition.x - (bbox.width + 4 * PX)).attr(\"y\", commitPosition.y + bbox.height - 12);\n      } else {\n        text.attr(\"x\", commitPosition.posWithOffset - bbox.width / 2);\n      }\n      if (DEFAULT_GITGRAPH_CONFIG2.rotateCommitLabel) {\n        if (dir === \"TB\" || dir === \"BT\") {\n          text.attr(\n            \"transform\",\n            \"rotate(-45, \" + commitPosition.x + \", \" + commitPosition.y + \")\"\n          );\n          labelBkg.attr(\n            \"transform\",\n            \"rotate(-45, \" + commitPosition.x + \", \" + commitPosition.y + \")\"\n          );\n        } else {\n          const r_x = -7.5 - (bbox.width + 10) / 25 * 9.5;\n          const r_y = 10 + bbox.width / 25 * 8.5;\n          wrapper.attr(\n            \"transform\",\n            \"translate(\" + r_x + \", \" + r_y + \") rotate(-45, \" + pos + \", \" + commitPosition.y + \")\"\n          );\n        }\n      }\n    }\n  }\n}, \"drawCommitLabel\");\nvar drawCommitTags = /* @__PURE__ */ __name((gLabels, commit2, commitPosition, pos) => {\n  if (commit2.tags.length > 0) {\n    let yOffset = 0;\n    let maxTagBboxWidth = 0;\n    let maxTagBboxHeight = 0;\n    const tagElements = [];\n    for (const tagValue of commit2.tags.reverse()) {\n      const rect = gLabels.insert(\"polygon\");\n      const hole = gLabels.append(\"circle\");\n      const tag = gLabels.append(\"text\").attr(\"y\", commitPosition.y - 16 - yOffset).attr(\"class\", \"tag-label\").text(tagValue);\n      const tagBbox = tag.node()?.getBBox();\n      if (!tagBbox) {\n        throw new Error(\"Tag bbox not found\");\n      }\n      maxTagBboxWidth = Math.max(maxTagBboxWidth, tagBbox.width);\n      maxTagBboxHeight = Math.max(maxTagBboxHeight, tagBbox.height);\n      tag.attr(\"x\", commitPosition.posWithOffset - tagBbox.width / 2);\n      tagElements.push({\n        tag,\n        hole,\n        rect,\n        yOffset\n      });\n      yOffset += 20;\n    }\n    for (const { tag, hole, rect, yOffset: yOffset2 } of tagElements) {\n      const h2 = maxTagBboxHeight / 2;\n      const ly = commitPosition.y - 19.2 - yOffset2;\n      rect.attr(\"class\", \"tag-label-bkg\").attr(\n        \"points\",\n        `\n      ${pos - maxTagBboxWidth / 2 - PX / 2},${ly + PY}  \n      ${pos - maxTagBboxWidth / 2 - PX / 2},${ly - PY}\n      ${commitPosition.posWithOffset - maxTagBboxWidth / 2 - PX},${ly - h2 - PY}\n      ${commitPosition.posWithOffset + maxTagBboxWidth / 2 + PX},${ly - h2 - PY}\n      ${commitPosition.posWithOffset + maxTagBboxWidth / 2 + PX},${ly + h2 + PY}\n      ${commitPosition.posWithOffset - maxTagBboxWidth / 2 - PX},${ly + h2 + PY}`\n      );\n      hole.attr(\"cy\", ly).attr(\"cx\", pos - maxTagBboxWidth / 2 + PX / 2).attr(\"r\", 1.5).attr(\"class\", \"tag-hole\");\n      if (dir === \"TB\" || dir === \"BT\") {\n        const yOrigin = pos + yOffset2;\n        rect.attr(\"class\", \"tag-label-bkg\").attr(\n          \"points\",\n          `\n        ${commitPosition.x},${yOrigin + 2}\n        ${commitPosition.x},${yOrigin - 2}\n        ${commitPosition.x + LAYOUT_OFFSET},${yOrigin - h2 - 2}\n        ${commitPosition.x + LAYOUT_OFFSET + maxTagBboxWidth + 4},${yOrigin - h2 - 2}\n        ${commitPosition.x + LAYOUT_OFFSET + maxTagBboxWidth + 4},${yOrigin + h2 + 2}\n        ${commitPosition.x + LAYOUT_OFFSET},${yOrigin + h2 + 2}`\n        ).attr(\"transform\", \"translate(12,12) rotate(45, \" + commitPosition.x + \",\" + pos + \")\");\n        hole.attr(\"cx\", commitPosition.x + PX / 2).attr(\"cy\", yOrigin).attr(\"transform\", \"translate(12,12) rotate(45, \" + commitPosition.x + \",\" + pos + \")\");\n        tag.attr(\"x\", commitPosition.x + 5).attr(\"y\", yOrigin + 3).attr(\"transform\", \"translate(14,14) rotate(45, \" + commitPosition.x + \",\" + pos + \")\");\n      }\n    }\n  }\n}, \"drawCommitTags\");\nvar getCommitClassType = /* @__PURE__ */ __name((commit2) => {\n  const commitSymbolType = commit2.customType ?? commit2.type;\n  switch (commitSymbolType) {\n    case commitType.NORMAL:\n      return \"commit-normal\";\n    case commitType.REVERSE:\n      return \"commit-reverse\";\n    case commitType.HIGHLIGHT:\n      return \"commit-highlight\";\n    case commitType.MERGE:\n      return \"commit-merge\";\n    case commitType.CHERRY_PICK:\n      return \"commit-cherry-pick\";\n    default:\n      return \"commit-normal\";\n  }\n}, \"getCommitClassType\");\nvar calculatePosition = /* @__PURE__ */ __name((commit2, dir2, pos, commitPos2) => {\n  const defaultCommitPosition = { x: 0, y: 0 };\n  if (commit2.parents.length > 0) {\n    const closestParent = findClosestParent(commit2.parents);\n    if (closestParent) {\n      const parentPosition = commitPos2.get(closestParent) ?? defaultCommitPosition;\n      if (dir2 === \"TB\") {\n        return parentPosition.y + COMMIT_STEP;\n      } else if (dir2 === \"BT\") {\n        const currentPosition = commitPos2.get(commit2.id) ?? defaultCommitPosition;\n        return currentPosition.y - COMMIT_STEP;\n      } else {\n        return parentPosition.x + COMMIT_STEP;\n      }\n    }\n  } else {\n    if (dir2 === \"TB\") {\n      return defaultPos;\n    } else if (dir2 === \"BT\") {\n      const currentPosition = commitPos2.get(commit2.id) ?? defaultCommitPosition;\n      return currentPosition.y - COMMIT_STEP;\n    } else {\n      return 0;\n    }\n  }\n  return 0;\n}, \"calculatePosition\");\nvar getCommitPosition = /* @__PURE__ */ __name((commit2, pos, isParallelCommits) => {\n  const posWithOffset = dir === \"BT\" && isParallelCommits ? pos : pos + LAYOUT_OFFSET;\n  const y = dir === \"TB\" || dir === \"BT\" ? posWithOffset : branchPos.get(commit2.branch)?.pos;\n  const x = dir === \"TB\" || dir === \"BT\" ? branchPos.get(commit2.branch)?.pos : posWithOffset;\n  if (x === void 0 || y === void 0) {\n    throw new Error(`Position were undefined for commit ${commit2.id}`);\n  }\n  return { x, y, posWithOffset };\n}, \"getCommitPosition\");\nvar drawCommits = /* @__PURE__ */ __name((svg, commits, modifyGraph) => {\n  if (!DEFAULT_GITGRAPH_CONFIG2) {\n    throw new Error(\"GitGraph config not found\");\n  }\n  const gBullets = svg.append(\"g\").attr(\"class\", \"commit-bullets\");\n  const gLabels = svg.append(\"g\").attr(\"class\", \"commit-labels\");\n  let pos = dir === \"TB\" || dir === \"BT\" ? defaultPos : 0;\n  const keys = [...commits.keys()];\n  const isParallelCommits = DEFAULT_GITGRAPH_CONFIG2?.parallelCommits ?? false;\n  const sortKeys = /* @__PURE__ */ __name((a, b) => {\n    const seqA = commits.get(a)?.seq;\n    const seqB = commits.get(b)?.seq;\n    return seqA !== void 0 && seqB !== void 0 ? seqA - seqB : 0;\n  }, \"sortKeys\");\n  let sortedKeys = keys.sort(sortKeys);\n  if (dir === \"BT\") {\n    if (isParallelCommits) {\n      setParallelBTPos(sortedKeys, commits, pos);\n    }\n    sortedKeys = sortedKeys.reverse();\n  }\n  sortedKeys.forEach((key) => {\n    const commit2 = commits.get(key);\n    if (!commit2) {\n      throw new Error(`Commit not found for key ${key}`);\n    }\n    if (isParallelCommits) {\n      pos = calculatePosition(commit2, dir, pos, commitPos);\n    }\n    const commitPosition = getCommitPosition(commit2, pos, isParallelCommits);\n    if (modifyGraph) {\n      const typeClass = getCommitClassType(commit2);\n      const commitSymbolType = commit2.customType ?? commit2.type;\n      const branchIndex = branchPos.get(commit2.branch)?.index ?? 0;\n      drawCommitBullet(gBullets, commit2, commitPosition, typeClass, branchIndex, commitSymbolType);\n      drawCommitLabel(gLabels, commit2, commitPosition, pos);\n      drawCommitTags(gLabels, commit2, commitPosition, pos);\n    }\n    if (dir === \"TB\" || dir === \"BT\") {\n      commitPos.set(commit2.id, { x: commitPosition.x, y: commitPosition.posWithOffset });\n    } else {\n      commitPos.set(commit2.id, { x: commitPosition.posWithOffset, y: commitPosition.y });\n    }\n    pos = dir === \"BT\" && isParallelCommits ? pos + COMMIT_STEP : pos + COMMIT_STEP + LAYOUT_OFFSET;\n    if (pos > maxPos) {\n      maxPos = pos;\n    }\n  });\n}, \"drawCommits\");\nvar shouldRerouteArrow = /* @__PURE__ */ __name((commitA, commitB, p1, p2, allCommits) => {\n  const commitBIsFurthest = dir === \"TB\" || dir === \"BT\" ? p1.x < p2.x : p1.y < p2.y;\n  const branchToGetCurve = commitBIsFurthest ? commitB.branch : commitA.branch;\n  const isOnBranchToGetCurve = /* @__PURE__ */ __name((x) => x.branch === branchToGetCurve, \"isOnBranchToGetCurve\");\n  const isBetweenCommits = /* @__PURE__ */ __name((x) => x.seq > commitA.seq && x.seq < commitB.seq, \"isBetweenCommits\");\n  return [...allCommits.values()].some((commitX) => {\n    return isBetweenCommits(commitX) && isOnBranchToGetCurve(commitX);\n  });\n}, \"shouldRerouteArrow\");\nvar findLane = /* @__PURE__ */ __name((y1, y2, depth = 0) => {\n  const candidate = y1 + Math.abs(y1 - y2) / 2;\n  if (depth > 5) {\n    return candidate;\n  }\n  const ok = lanes.every((lane) => Math.abs(lane - candidate) >= 10);\n  if (ok) {\n    lanes.push(candidate);\n    return candidate;\n  }\n  const diff = Math.abs(y1 - y2);\n  return findLane(y1, y2 - diff / 5, depth + 1);\n}, \"findLane\");\nvar drawArrow = /* @__PURE__ */ __name((svg, commitA, commitB, allCommits) => {\n  const p1 = commitPos.get(commitA.id);\n  const p2 = commitPos.get(commitB.id);\n  if (p1 === void 0 || p2 === void 0) {\n    throw new Error(`Commit positions not found for commits ${commitA.id} and ${commitB.id}`);\n  }\n  const arrowNeedsRerouting = shouldRerouteArrow(commitA, commitB, p1, p2, allCommits);\n  let arc = \"\";\n  let arc2 = \"\";\n  let radius = 0;\n  let offset = 0;\n  let colorClassNum = branchPos.get(commitB.branch)?.index;\n  if (commitB.type === commitType.MERGE && commitA.id !== commitB.parents[0]) {\n    colorClassNum = branchPos.get(commitA.branch)?.index;\n  }\n  let lineDef;\n  if (arrowNeedsRerouting) {\n    arc = \"A 10 10, 0, 0, 0,\";\n    arc2 = \"A 10 10, 0, 0, 1,\";\n    radius = 10;\n    offset = 10;\n    const lineY = p1.y < p2.y ? findLane(p1.y, p2.y) : findLane(p2.y, p1.y);\n    const lineX = p1.x < p2.x ? findLane(p1.x, p2.x) : findLane(p2.x, p1.x);\n    if (dir === \"TB\") {\n      if (p1.x < p2.x) {\n        lineDef = `M ${p1.x} ${p1.y} L ${lineX - radius} ${p1.y} ${arc2} ${lineX} ${p1.y + offset} L ${lineX} ${p2.y - radius} ${arc} ${lineX + offset} ${p2.y} L ${p2.x} ${p2.y}`;\n      } else {\n        colorClassNum = branchPos.get(commitA.branch)?.index;\n        lineDef = `M ${p1.x} ${p1.y} L ${lineX + radius} ${p1.y} ${arc} ${lineX} ${p1.y + offset} L ${lineX} ${p2.y - radius} ${arc2} ${lineX - offset} ${p2.y} L ${p2.x} ${p2.y}`;\n      }\n    } else if (dir === \"BT\") {\n      if (p1.x < p2.x) {\n        lineDef = `M ${p1.x} ${p1.y} L ${lineX - radius} ${p1.y} ${arc} ${lineX} ${p1.y - offset} L ${lineX} ${p2.y + radius} ${arc2} ${lineX + offset} ${p2.y} L ${p2.x} ${p2.y}`;\n      } else {\n        colorClassNum = branchPos.get(commitA.branch)?.index;\n        lineDef = `M ${p1.x} ${p1.y} L ${lineX + radius} ${p1.y} ${arc2} ${lineX} ${p1.y - offset} L ${lineX} ${p2.y + radius} ${arc} ${lineX - offset} ${p2.y} L ${p2.x} ${p2.y}`;\n      }\n    } else {\n      if (p1.y < p2.y) {\n        lineDef = `M ${p1.x} ${p1.y} L ${p1.x} ${lineY - radius} ${arc} ${p1.x + offset} ${lineY} L ${p2.x - radius} ${lineY} ${arc2} ${p2.x} ${lineY + offset} L ${p2.x} ${p2.y}`;\n      } else {\n        colorClassNum = branchPos.get(commitA.branch)?.index;\n        lineDef = `M ${p1.x} ${p1.y} L ${p1.x} ${lineY + radius} ${arc2} ${p1.x + offset} ${lineY} L ${p2.x - radius} ${lineY} ${arc} ${p2.x} ${lineY - offset} L ${p2.x} ${p2.y}`;\n      }\n    }\n  } else {\n    arc = \"A 20 20, 0, 0, 0,\";\n    arc2 = \"A 20 20, 0, 0, 1,\";\n    radius = 20;\n    offset = 20;\n    if (dir === \"TB\") {\n      if (p1.x < p2.x) {\n        if (commitB.type === commitType.MERGE && commitA.id !== commitB.parents[0]) {\n          lineDef = `M ${p1.x} ${p1.y} L ${p1.x} ${p2.y - radius} ${arc} ${p1.x + offset} ${p2.y} L ${p2.x} ${p2.y}`;\n        } else {\n          lineDef = `M ${p1.x} ${p1.y} L ${p2.x - radius} ${p1.y} ${arc2} ${p2.x} ${p1.y + offset} L ${p2.x} ${p2.y}`;\n        }\n      }\n      if (p1.x > p2.x) {\n        arc = \"A 20 20, 0, 0, 0,\";\n        arc2 = \"A 20 20, 0, 0, 1,\";\n        radius = 20;\n        offset = 20;\n        if (commitB.type === commitType.MERGE && commitA.id !== commitB.parents[0]) {\n          lineDef = `M ${p1.x} ${p1.y} L ${p1.x} ${p2.y - radius} ${arc2} ${p1.x - offset} ${p2.y} L ${p2.x} ${p2.y}`;\n        } else {\n          lineDef = `M ${p1.x} ${p1.y} L ${p2.x + radius} ${p1.y} ${arc} ${p2.x} ${p1.y + offset} L ${p2.x} ${p2.y}`;\n        }\n      }\n      if (p1.x === p2.x) {\n        lineDef = `M ${p1.x} ${p1.y} L ${p2.x} ${p2.y}`;\n      }\n    } else if (dir === \"BT\") {\n      if (p1.x < p2.x) {\n        if (commitB.type === commitType.MERGE && commitA.id !== commitB.parents[0]) {\n          lineDef = `M ${p1.x} ${p1.y} L ${p1.x} ${p2.y + radius} ${arc2} ${p1.x + offset} ${p2.y} L ${p2.x} ${p2.y}`;\n        } else {\n          lineDef = `M ${p1.x} ${p1.y} L ${p2.x - radius} ${p1.y} ${arc} ${p2.x} ${p1.y - offset} L ${p2.x} ${p2.y}`;\n        }\n      }\n      if (p1.x > p2.x) {\n        arc = \"A 20 20, 0, 0, 0,\";\n        arc2 = \"A 20 20, 0, 0, 1,\";\n        radius = 20;\n        offset = 20;\n        if (commitB.type === commitType.MERGE && commitA.id !== commitB.parents[0]) {\n          lineDef = `M ${p1.x} ${p1.y} L ${p1.x} ${p2.y + radius} ${arc} ${p1.x - offset} ${p2.y} L ${p2.x} ${p2.y}`;\n        } else {\n          lineDef = `M ${p1.x} ${p1.y} L ${p2.x - radius} ${p1.y} ${arc} ${p2.x} ${p1.y - offset} L ${p2.x} ${p2.y}`;\n        }\n      }\n      if (p1.x === p2.x) {\n        lineDef = `M ${p1.x} ${p1.y} L ${p2.x} ${p2.y}`;\n      }\n    } else {\n      if (p1.y < p2.y) {\n        if (commitB.type === commitType.MERGE && commitA.id !== commitB.parents[0]) {\n          lineDef = `M ${p1.x} ${p1.y} L ${p2.x - radius} ${p1.y} ${arc2} ${p2.x} ${p1.y + offset} L ${p2.x} ${p2.y}`;\n        } else {\n          lineDef = `M ${p1.x} ${p1.y} L ${p1.x} ${p2.y - radius} ${arc} ${p1.x + offset} ${p2.y} L ${p2.x} ${p2.y}`;\n        }\n      }\n      if (p1.y > p2.y) {\n        if (commitB.type === commitType.MERGE && commitA.id !== commitB.parents[0]) {\n          lineDef = `M ${p1.x} ${p1.y} L ${p2.x - radius} ${p1.y} ${arc} ${p2.x} ${p1.y - offset} L ${p2.x} ${p2.y}`;\n        } else {\n          lineDef = `M ${p1.x} ${p1.y} L ${p1.x} ${p2.y + radius} ${arc2} ${p1.x + offset} ${p2.y} L ${p2.x} ${p2.y}`;\n        }\n      }\n      if (p1.y === p2.y) {\n        lineDef = `M ${p1.x} ${p1.y} L ${p2.x} ${p2.y}`;\n      }\n    }\n  }\n  if (lineDef === void 0) {\n    throw new Error(\"Line definition not found\");\n  }\n  svg.append(\"path\").attr(\"d\", lineDef).attr(\"class\", \"arrow arrow\" + colorClassNum % THEME_COLOR_LIMIT);\n}, \"drawArrow\");\nvar drawArrows = /* @__PURE__ */ __name((svg, commits) => {\n  const gArrows = svg.append(\"g\").attr(\"class\", \"commit-arrows\");\n  [...commits.keys()].forEach((key) => {\n    const commit2 = commits.get(key);\n    if (commit2.parents && commit2.parents.length > 0) {\n      commit2.parents.forEach((parent) => {\n        drawArrow(gArrows, commits.get(parent), commit2, commits);\n      });\n    }\n  });\n}, \"drawArrows\");\nvar drawBranches = /* @__PURE__ */ __name((svg, branches) => {\n  const g = svg.append(\"g\");\n  branches.forEach((branch2, index) => {\n    const adjustIndexForTheme = index % THEME_COLOR_LIMIT;\n    const pos = branchPos.get(branch2.name)?.pos;\n    if (pos === void 0) {\n      throw new Error(`Position not found for branch ${branch2.name}`);\n    }\n    const line = g.append(\"line\");\n    line.attr(\"x1\", 0);\n    line.attr(\"y1\", pos);\n    line.attr(\"x2\", maxPos);\n    line.attr(\"y2\", pos);\n    line.attr(\"class\", \"branch branch\" + adjustIndexForTheme);\n    if (dir === \"TB\") {\n      line.attr(\"y1\", defaultPos);\n      line.attr(\"x1\", pos);\n      line.attr(\"y2\", maxPos);\n      line.attr(\"x2\", pos);\n    } else if (dir === \"BT\") {\n      line.attr(\"y1\", maxPos);\n      line.attr(\"x1\", pos);\n      line.attr(\"y2\", defaultPos);\n      line.attr(\"x2\", pos);\n    }\n    lanes.push(pos);\n    const name = branch2.name;\n    const labelElement = drawText(name);\n    const bkg = g.insert(\"rect\");\n    const branchLabel = g.insert(\"g\").attr(\"class\", \"branchLabel\");\n    const label = branchLabel.insert(\"g\").attr(\"class\", \"label branch-label\" + adjustIndexForTheme);\n    label.node().appendChild(labelElement);\n    const bbox = labelElement.getBBox();\n    bkg.attr(\"class\", \"branchLabelBkg label\" + adjustIndexForTheme).attr(\"rx\", 4).attr(\"ry\", 4).attr(\"x\", -bbox.width - 4 - (DEFAULT_GITGRAPH_CONFIG2?.rotateCommitLabel === true ? 30 : 0)).attr(\"y\", -bbox.height / 2 + 8).attr(\"width\", bbox.width + 18).attr(\"height\", bbox.height + 4);\n    label.attr(\n      \"transform\",\n      \"translate(\" + (-bbox.width - 14 - (DEFAULT_GITGRAPH_CONFIG2?.rotateCommitLabel === true ? 30 : 0)) + \", \" + (pos - bbox.height / 2 - 1) + \")\"\n    );\n    if (dir === \"TB\") {\n      bkg.attr(\"x\", pos - bbox.width / 2 - 10).attr(\"y\", 0);\n      label.attr(\"transform\", \"translate(\" + (pos - bbox.width / 2 - 5) + \", 0)\");\n    } else if (dir === \"BT\") {\n      bkg.attr(\"x\", pos - bbox.width / 2 - 10).attr(\"y\", maxPos);\n      label.attr(\"transform\", \"translate(\" + (pos - bbox.width / 2 - 5) + \", \" + maxPos + \")\");\n    } else {\n      bkg.attr(\"transform\", \"translate(-19, \" + (pos - bbox.height / 2) + \")\");\n    }\n  });\n}, \"drawBranches\");\nvar setBranchPosition = /* @__PURE__ */ __name(function(name, pos, index, bbox, rotateCommitLabel) {\n  branchPos.set(name, { pos, index });\n  pos += 50 + (rotateCommitLabel ? 40 : 0) + (dir === \"TB\" || dir === \"BT\" ? bbox.width / 2 : 0);\n  return pos;\n}, \"setBranchPosition\");\nvar draw = /* @__PURE__ */ __name(function(txt, id, ver, diagObj) {\n  clear3();\n  log.debug(\"in gitgraph renderer\", txt + \"\\n\", \"id:\", id, ver);\n  if (!DEFAULT_GITGRAPH_CONFIG2) {\n    throw new Error(\"GitGraph config not found\");\n  }\n  const rotateCommitLabel = DEFAULT_GITGRAPH_CONFIG2.rotateCommitLabel ?? false;\n  const db2 = diagObj.db;\n  allCommitsDict = db2.getCommits();\n  const branches = db2.getBranchesAsObjArray();\n  dir = db2.getDirection();\n  const diagram2 = select(`[id=\"${id}\"]`);\n  let pos = 0;\n  branches.forEach((branch2, index) => {\n    const labelElement = drawText(branch2.name);\n    const g = diagram2.append(\"g\");\n    const branchLabel = g.insert(\"g\").attr(\"class\", \"branchLabel\");\n    const label = branchLabel.insert(\"g\").attr(\"class\", \"label branch-label\");\n    label.node()?.appendChild(labelElement);\n    const bbox = labelElement.getBBox();\n    pos = setBranchPosition(branch2.name, pos, index, bbox, rotateCommitLabel);\n    label.remove();\n    branchLabel.remove();\n    g.remove();\n  });\n  drawCommits(diagram2, allCommitsDict, false);\n  if (DEFAULT_GITGRAPH_CONFIG2.showBranches) {\n    drawBranches(diagram2, branches);\n  }\n  drawArrows(diagram2, allCommitsDict);\n  drawCommits(diagram2, allCommitsDict, true);\n  utils_default.insertTitle(\n    diagram2,\n    \"gitTitleText\",\n    DEFAULT_GITGRAPH_CONFIG2.titleTopMargin ?? 0,\n    db2.getDiagramTitle()\n  );\n  setupGraphViewbox(\n    void 0,\n    diagram2,\n    DEFAULT_GITGRAPH_CONFIG2.diagramPadding,\n    DEFAULT_GITGRAPH_CONFIG2.useMaxWidth\n  );\n}, \"draw\");\nvar gitGraphRenderer_default = {\n  draw\n};\nif (void 0) {\n  const { it, expect, describe } = void 0;\n  describe(\"drawText\", () => {\n    it(\"should drawText\", () => {\n      const svgLabel = drawText(\"main\");\n      expect(svgLabel).toBeDefined();\n      expect(svgLabel.children[0].innerHTML).toBe(\"main\");\n    });\n  });\n  describe(\"branchPosition\", () => {\n    const bbox = {\n      x: 0,\n      y: 0,\n      width: 10,\n      height: 10,\n      top: 0,\n      right: 0,\n      bottom: 0,\n      left: 0,\n      toJSON: /* @__PURE__ */ __name(() => \"\", \"toJSON\")\n    };\n    it(\"should setBranchPositions LR with two branches\", () => {\n      dir = \"LR\";\n      const pos = setBranchPosition(\"main\", 0, 0, bbox, true);\n      expect(pos).toBe(90);\n      expect(branchPos.get(\"main\")).toEqual({ pos: 0, index: 0 });\n      const posNext = setBranchPosition(\"develop\", pos, 1, bbox, true);\n      expect(posNext).toBe(180);\n      expect(branchPos.get(\"develop\")).toEqual({ pos, index: 1 });\n    });\n    it(\"should setBranchPositions TB with two branches\", () => {\n      dir = \"TB\";\n      bbox.width = 34.9921875;\n      const pos = setBranchPosition(\"main\", 0, 0, bbox, true);\n      expect(pos).toBe(107.49609375);\n      expect(branchPos.get(\"main\")).toEqual({ pos: 0, index: 0 });\n      bbox.width = 56.421875;\n      const posNext = setBranchPosition(\"develop\", pos, 1, bbox, true);\n      expect(posNext).toBe(225.70703125);\n      expect(branchPos.get(\"develop\")).toEqual({ pos, index: 1 });\n    });\n  });\n  describe(\"commitPosition\", () => {\n    const commits = /* @__PURE__ */ new Map([\n      [\n        \"commitZero\",\n        {\n          id: \"ZERO\",\n          message: \"\",\n          seq: 0,\n          type: commitType.NORMAL,\n          tags: [],\n          parents: [],\n          branch: \"main\"\n        }\n      ],\n      [\n        \"commitA\",\n        {\n          id: \"A\",\n          message: \"\",\n          seq: 1,\n          type: commitType.NORMAL,\n          tags: [],\n          parents: [\"ZERO\"],\n          branch: \"feature\"\n        }\n      ],\n      [\n        \"commitB\",\n        {\n          id: \"B\",\n          message: \"\",\n          seq: 2,\n          type: commitType.NORMAL,\n          tags: [],\n          parents: [\"A\"],\n          branch: \"feature\"\n        }\n      ],\n      [\n        \"commitM\",\n        {\n          id: \"M\",\n          message: \"merged branch feature into main\",\n          seq: 3,\n          type: commitType.MERGE,\n          tags: [],\n          parents: [\"ZERO\", \"B\"],\n          branch: \"main\",\n          customId: true\n        }\n      ],\n      [\n        \"commitC\",\n        {\n          id: \"C\",\n          message: \"\",\n          seq: 4,\n          type: commitType.NORMAL,\n          tags: [],\n          parents: [\"ZERO\"],\n          branch: \"release\"\n        }\n      ],\n      [\n        \"commit5_8928ea0\",\n        {\n          id: \"5-8928ea0\",\n          message: \"cherry-picked [object Object] into release\",\n          seq: 5,\n          type: commitType.CHERRY_PICK,\n          tags: [],\n          parents: [\"C\", \"M\"],\n          branch: \"release\"\n        }\n      ],\n      [\n        \"commitD\",\n        {\n          id: \"D\",\n          message: \"\",\n          seq: 6,\n          type: commitType.NORMAL,\n          tags: [],\n          parents: [\"5-8928ea0\"],\n          branch: \"release\"\n        }\n      ],\n      [\n        \"commit7_ed848ba\",\n        {\n          id: \"7-ed848ba\",\n          message: \"cherry-picked [object Object] into release\",\n          seq: 7,\n          type: commitType.CHERRY_PICK,\n          tags: [],\n          parents: [\"D\", \"M\"],\n          branch: \"release\"\n        }\n      ]\n    ]);\n    let pos = 0;\n    branchPos.set(\"main\", { pos: 0, index: 0 });\n    branchPos.set(\"feature\", { pos: 107.49609375, index: 1 });\n    branchPos.set(\"release\", { pos: 224.03515625, index: 2 });\n    describe(\"TB\", () => {\n      pos = 30;\n      dir = \"TB\";\n      const expectedCommitPositionTB = /* @__PURE__ */ new Map([\n        [\"commitZero\", { x: 0, y: 40, posWithOffset: 40 }],\n        [\"commitA\", { x: 107.49609375, y: 90, posWithOffset: 90 }],\n        [\"commitB\", { x: 107.49609375, y: 140, posWithOffset: 140 }],\n        [\"commitM\", { x: 0, y: 190, posWithOffset: 190 }],\n        [\"commitC\", { x: 224.03515625, y: 240, posWithOffset: 240 }],\n        [\"commit5_8928ea0\", { x: 224.03515625, y: 290, posWithOffset: 290 }],\n        [\"commitD\", { x: 224.03515625, y: 340, posWithOffset: 340 }],\n        [\"commit7_ed848ba\", { x: 224.03515625, y: 390, posWithOffset: 390 }]\n      ]);\n      commits.forEach((commit2, key) => {\n        it(`should give the correct position for commit ${key}`, () => {\n          const position = getCommitPosition(commit2, pos, false);\n          expect(position).toEqual(expectedCommitPositionTB.get(key));\n          pos += 50;\n        });\n      });\n    });\n    describe(\"LR\", () => {\n      let pos2 = 30;\n      dir = \"LR\";\n      const expectedCommitPositionLR = /* @__PURE__ */ new Map([\n        [\"commitZero\", { x: 0, y: 40, posWithOffset: 40 }],\n        [\"commitA\", { x: 107.49609375, y: 90, posWithOffset: 90 }],\n        [\"commitB\", { x: 107.49609375, y: 140, posWithOffset: 140 }],\n        [\"commitM\", { x: 0, y: 190, posWithOffset: 190 }],\n        [\"commitC\", { x: 224.03515625, y: 240, posWithOffset: 240 }],\n        [\"commit5_8928ea0\", { x: 224.03515625, y: 290, posWithOffset: 290 }],\n        [\"commitD\", { x: 224.03515625, y: 340, posWithOffset: 340 }],\n        [\"commit7_ed848ba\", { x: 224.03515625, y: 390, posWithOffset: 390 }]\n      ]);\n      commits.forEach((commit2, key) => {\n        it(`should give the correct position for commit ${key}`, () => {\n          const position = getCommitPosition(commit2, pos2, false);\n          expect(position).toEqual(expectedCommitPositionLR.get(key));\n          pos2 += 50;\n        });\n      });\n    });\n    describe(\"getCommitClassType\", () => {\n      const expectedCommitClassType = /* @__PURE__ */ new Map([\n        [\"commitZero\", \"commit-normal\"],\n        [\"commitA\", \"commit-normal\"],\n        [\"commitB\", \"commit-normal\"],\n        [\"commitM\", \"commit-merge\"],\n        [\"commitC\", \"commit-normal\"],\n        [\"commit5_8928ea0\", \"commit-cherry-pick\"],\n        [\"commitD\", \"commit-normal\"],\n        [\"commit7_ed848ba\", \"commit-cherry-pick\"]\n      ]);\n      commits.forEach((commit2, key) => {\n        it(`should give the correct class type for commit ${key}`, () => {\n          const classType = getCommitClassType(commit2);\n          expect(classType).toBe(expectedCommitClassType.get(key));\n        });\n      });\n    });\n  });\n  describe(\"building BT parallel commit diagram\", () => {\n    const commits = /* @__PURE__ */ new Map([\n      [\n        \"1-abcdefg\",\n        {\n          id: \"1-abcdefg\",\n          message: \"\",\n          seq: 0,\n          type: 0,\n          tags: [],\n          parents: [],\n          branch: \"main\"\n        }\n      ],\n      [\n        \"2-abcdefg\",\n        {\n          id: \"2-abcdefg\",\n          message: \"\",\n          seq: 1,\n          type: 0,\n          tags: [],\n          parents: [\"1-abcdefg\"],\n          branch: \"main\"\n        }\n      ],\n      [\n        \"3-abcdefg\",\n        {\n          id: \"3-abcdefg\",\n          message: \"\",\n          seq: 2,\n          type: 0,\n          tags: [],\n          parents: [\"2-abcdefg\"],\n          branch: \"develop\"\n        }\n      ],\n      [\n        \"4-abcdefg\",\n        {\n          id: \"4-abcdefg\",\n          message: \"\",\n          seq: 3,\n          type: 0,\n          tags: [],\n          parents: [\"3-abcdefg\"],\n          branch: \"develop\"\n        }\n      ],\n      [\n        \"5-abcdefg\",\n        {\n          id: \"5-abcdefg\",\n          message: \"\",\n          seq: 4,\n          type: 0,\n          tags: [],\n          parents: [\"2-abcdefg\"],\n          branch: \"feature\"\n        }\n      ],\n      [\n        \"6-abcdefg\",\n        {\n          id: \"6-abcdefg\",\n          message: \"\",\n          seq: 5,\n          type: 0,\n          tags: [],\n          parents: [\"5-abcdefg\"],\n          branch: \"feature\"\n        }\n      ],\n      [\n        \"7-abcdefg\",\n        {\n          id: \"7-abcdefg\",\n          message: \"\",\n          seq: 6,\n          type: 0,\n          tags: [],\n          parents: [\"2-abcdefg\"],\n          branch: \"main\"\n        }\n      ],\n      [\n        \"8-abcdefg\",\n        {\n          id: \"8-abcdefg\",\n          message: \"\",\n          seq: 7,\n          type: 0,\n          tags: [],\n          parents: [\"7-abcdefg\"],\n          branch: \"main\"\n        }\n      ]\n    ]);\n    const expectedCommitPosition = /* @__PURE__ */ new Map([\n      [\"1-abcdefg\", { x: 0, y: 40 }],\n      [\"2-abcdefg\", { x: 0, y: 90 }],\n      [\"3-abcdefg\", { x: 107.49609375, y: 140 }],\n      [\"4-abcdefg\", { x: 107.49609375, y: 190 }],\n      [\"5-abcdefg\", { x: 225.70703125, y: 140 }],\n      [\"6-abcdefg\", { x: 225.70703125, y: 190 }],\n      [\"7-abcdefg\", { x: 0, y: 140 }],\n      [\"8-abcdefg\", { x: 0, y: 190 }]\n    ]);\n    const expectedCommitPositionAfterParallel = /* @__PURE__ */ new Map([\n      [\"1-abcdefg\", { x: 0, y: 210 }],\n      [\"2-abcdefg\", { x: 0, y: 160 }],\n      [\"3-abcdefg\", { x: 107.49609375, y: 110 }],\n      [\"4-abcdefg\", { x: 107.49609375, y: 60 }],\n      [\"5-abcdefg\", { x: 225.70703125, y: 110 }],\n      [\"6-abcdefg\", { x: 225.70703125, y: 60 }],\n      [\"7-abcdefg\", { x: 0, y: 110 }],\n      [\"8-abcdefg\", { x: 0, y: 60 }]\n    ]);\n    const expectedCommitCurrentPosition = /* @__PURE__ */ new Map([\n      [\"1-abcdefg\", 30],\n      [\"2-abcdefg\", 80],\n      [\"3-abcdefg\", 130],\n      [\"4-abcdefg\", 180],\n      [\"5-abcdefg\", 130],\n      [\"6-abcdefg\", 180],\n      [\"7-abcdefg\", 130],\n      [\"8-abcdefg\", 180]\n    ]);\n    const sortedKeys = [...expectedCommitPosition.keys()];\n    it(\"should get the correct commit position and current position\", () => {\n      dir = \"BT\";\n      let curPos = 30;\n      commitPos.clear();\n      branchPos.clear();\n      branchPos.set(\"main\", { pos: 0, index: 0 });\n      branchPos.set(\"develop\", { pos: 107.49609375, index: 1 });\n      branchPos.set(\"feature\", { pos: 225.70703125, index: 2 });\n      DEFAULT_GITGRAPH_CONFIG2.parallelCommits = true;\n      commits.forEach((commit2, key) => {\n        if (commit2.parents.length > 0) {\n          curPos = calculateCommitPosition(commit2);\n        }\n        const position = setCommitPosition(commit2, curPos);\n        expect(position).toEqual(expectedCommitPosition.get(key));\n        expect(curPos).toEqual(expectedCommitCurrentPosition.get(key));\n      });\n    });\n    it(\"should get the correct commit position after parallel commits\", () => {\n      commitPos.clear();\n      branchPos.clear();\n      dir = \"BT\";\n      const curPos = 30;\n      commitPos.clear();\n      branchPos.clear();\n      branchPos.set(\"main\", { pos: 0, index: 0 });\n      branchPos.set(\"develop\", { pos: 107.49609375, index: 1 });\n      branchPos.set(\"feature\", { pos: 225.70703125, index: 2 });\n      setParallelBTPos(sortedKeys, commits, curPos);\n      sortedKeys.forEach((commit2) => {\n        const position = commitPos.get(commit2);\n        expect(position).toEqual(expectedCommitPositionAfterParallel.get(commit2));\n      });\n    });\n  });\n  DEFAULT_GITGRAPH_CONFIG2.parallelCommits = false;\n  it(\"add\", () => {\n    commitPos.set(\"parent1\", { x: 1, y: 1 });\n    commitPos.set(\"parent2\", { x: 2, y: 2 });\n    commitPos.set(\"parent3\", { x: 3, y: 3 });\n    dir = \"LR\";\n    const parents = [\"parent1\", \"parent2\", \"parent3\"];\n    const closestParent = findClosestParent(parents);\n    expect(closestParent).toBe(\"parent3\");\n    commitPos.clear();\n  });\n}\n\n// src/diagrams/git/styles.js\nvar getStyles = /* @__PURE__ */ __name((options) => `\n  .commit-id,\n  .commit-msg,\n  .branch-label {\n    fill: lightgrey;\n    color: lightgrey;\n    font-family: 'trebuchet ms', verdana, arial, sans-serif;\n    font-family: var(--mermaid-font-family);\n  }\n  ${[0, 1, 2, 3, 4, 5, 6, 7].map(\n  (i) => `\n        .branch-label${i} { fill: ${options[\"gitBranchLabel\" + i]}; }\n        .commit${i} { stroke: ${options[\"git\" + i]}; fill: ${options[\"git\" + i]}; }\n        .commit-highlight${i} { stroke: ${options[\"gitInv\" + i]}; fill: ${options[\"gitInv\" + i]}; }\n        .label${i}  { fill: ${options[\"git\" + i]}; }\n        .arrow${i} { stroke: ${options[\"git\" + i]}; }\n        `\n).join(\"\\n\")}\n\n  .branch {\n    stroke-width: 1;\n    stroke: ${options.lineColor};\n    stroke-dasharray: 2;\n  }\n  .commit-label { font-size: ${options.commitLabelFontSize}; fill: ${options.commitLabelColor};}\n  .commit-label-bkg { font-size: ${options.commitLabelFontSize}; fill: ${options.commitLabelBackground}; opacity: 0.5; }\n  .tag-label { font-size: ${options.tagLabelFontSize}; fill: ${options.tagLabelColor};}\n  .tag-label-bkg { fill: ${options.tagLabelBackground}; stroke: ${options.tagLabelBorder}; }\n  .tag-hole { fill: ${options.textColor}; }\n\n  .commit-merge {\n    stroke: ${options.primaryColor};\n    fill: ${options.primaryColor};\n  }\n  .commit-reverse {\n    stroke: ${options.primaryColor};\n    fill: ${options.primaryColor};\n    stroke-width: 3;\n  }\n  .commit-highlight-outer {\n  }\n  .commit-highlight-inner {\n    stroke: ${options.primaryColor};\n    fill: ${options.primaryColor};\n  }\n\n  .arrow { stroke-width: 8; stroke-linecap: round; fill: none}\n  .gitTitleText {\n    text-anchor: middle;\n    font-size: 18px;\n    fill: ${options.textColor};\n  }\n`, \"getStyles\");\nvar styles_default = getStyles;\n\n// src/diagrams/git/gitGraphDiagram.ts\nvar diagram = {\n  parser,\n  db,\n  renderer: gitGraphRenderer_default,\n  styles: styles_default\n};\nexport {\n  diagram\n};\n"], "names": [], "sourceRoot": ""}