{"version": 3, "file": "632.c59cde46a58f6dac3b70.js?v=c59cde46a58f6dac3b70", "mappings": ";;;;;;;;;;AAAA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,sBAAsB;AACtB;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA,gDAAgD;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEO;AACP;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA,oBAAoB,mBAAmB;AACvC;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/oz.js"], "sourcesContent": ["function wordRegexp(words) {\n  return new RegExp(\"^((\" + words.join(\")|(\") + \"))\\\\b\");\n}\n\nvar singleOperators = /[\\^@!\\|<>#~\\.\\*\\-\\+\\\\/,=]/;\nvar doubleOperators = /(<-)|(:=)|(=<)|(>=)|(<=)|(<:)|(>:)|(=:)|(\\\\=)|(\\\\=:)|(!!)|(==)|(::)/;\nvar tripleOperators = /(:::)|(\\.\\.\\.)|(=<:)|(>=:)/;\n\nvar middle = [\"in\", \"then\", \"else\", \"of\", \"elseof\", \"elsecase\", \"elseif\", \"catch\",\n              \"finally\", \"with\", \"require\", \"prepare\", \"import\", \"export\", \"define\", \"do\"];\nvar end = [\"end\"];\n\nvar atoms = wordRegexp([\"true\", \"false\", \"nil\", \"unit\"]);\nvar commonKeywords = wordRegexp([\"andthen\", \"at\", \"attr\", \"declare\", \"feat\", \"from\", \"lex\",\n                                 \"mod\", \"div\", \"mode\", \"orelse\", \"parser\", \"prod\", \"prop\", \"scanner\", \"self\", \"syn\", \"token\"]);\nvar openingKeywords = wordRegexp([\"local\", \"proc\", \"fun\", \"case\", \"class\", \"if\", \"cond\", \"or\", \"dis\",\n                                  \"choice\", \"not\", \"thread\", \"try\", \"raise\", \"lock\", \"for\", \"suchthat\", \"meth\", \"functor\"]);\nvar middleKeywords = wordRegexp(middle);\nvar endKeywords = wordRegexp(end);\n\n// Tokenizers\nfunction tokenBase(stream, state) {\n  if (stream.eatSpace()) {\n    return null;\n  }\n\n  // Brackets\n  if(stream.match(/[{}]/)) {\n    return \"bracket\";\n  }\n\n  // Special [] keyword\n  if (stream.match('[]')) {\n    return \"keyword\"\n  }\n\n  // Operators\n  if (stream.match(tripleOperators) || stream.match(doubleOperators)) {\n    return \"operator\";\n  }\n\n  // Atoms\n  if(stream.match(atoms)) {\n    return 'atom';\n  }\n\n  // Opening keywords\n  var matched = stream.match(openingKeywords);\n  if (matched) {\n    if (!state.doInCurrentLine)\n      state.currentIndent++;\n    else\n      state.doInCurrentLine = false;\n\n    // Special matching for signatures\n    if(matched[0] == \"proc\" || matched[0] == \"fun\")\n      state.tokenize = tokenFunProc;\n    else if(matched[0] == \"class\")\n      state.tokenize = tokenClass;\n    else if(matched[0] == \"meth\")\n      state.tokenize = tokenMeth;\n\n    return 'keyword';\n  }\n\n  // Middle and other keywords\n  if (stream.match(middleKeywords) || stream.match(commonKeywords)) {\n    return \"keyword\"\n  }\n\n  // End keywords\n  if (stream.match(endKeywords)) {\n    state.currentIndent--;\n    return 'keyword';\n  }\n\n  // Eat the next char for next comparisons\n  var ch = stream.next();\n\n  // Strings\n  if (ch == '\"' || ch == \"'\") {\n    state.tokenize = tokenString(ch);\n    return state.tokenize(stream, state);\n  }\n\n  // Numbers\n  if (/[~\\d]/.test(ch)) {\n    if (ch == \"~\") {\n      if(! /^[0-9]/.test(stream.peek()))\n        return null;\n      else if (( stream.next() == \"0\" && stream.match(/^[xX][0-9a-fA-F]+/)) || stream.match(/^[0-9]*(\\.[0-9]+)?([eE][~+]?[0-9]+)?/))\n        return \"number\";\n    }\n\n    if ((ch == \"0\" && stream.match(/^[xX][0-9a-fA-F]+/)) || stream.match(/^[0-9]*(\\.[0-9]+)?([eE][~+]?[0-9]+)?/))\n      return \"number\";\n\n    return null;\n  }\n\n  // Comments\n  if (ch == \"%\") {\n    stream.skipToEnd();\n    return 'comment';\n  }\n  else if (ch == \"/\") {\n    if (stream.eat(\"*\")) {\n      state.tokenize = tokenComment;\n      return tokenComment(stream, state);\n    }\n  }\n\n  // Single operators\n  if(singleOperators.test(ch)) {\n    return \"operator\";\n  }\n\n  // If nothing match, we skip the entire alphanumerical block\n  stream.eatWhile(/\\w/);\n\n  return \"variable\";\n}\n\nfunction tokenClass(stream, state) {\n  if (stream.eatSpace()) {\n    return null;\n  }\n  stream.match(/([A-Z][A-Za-z0-9_]*)|(`.+`)/);\n  state.tokenize = tokenBase;\n  return \"type\"\n}\n\nfunction tokenMeth(stream, state) {\n  if (stream.eatSpace()) {\n    return null;\n  }\n  stream.match(/([a-zA-Z][A-Za-z0-9_]*)|(`.+`)/);\n  state.tokenize = tokenBase;\n  return \"def\"\n}\n\nfunction tokenFunProc(stream, state) {\n  if (stream.eatSpace()) {\n    return null;\n  }\n\n  if(!state.hasPassedFirstStage && stream.eat(\"{\")) {\n    state.hasPassedFirstStage = true;\n    return \"bracket\";\n  }\n  else if(state.hasPassedFirstStage) {\n    stream.match(/([A-Z][A-Za-z0-9_]*)|(`.+`)|\\$/);\n    state.hasPassedFirstStage = false;\n    state.tokenize = tokenBase;\n    return \"def\"\n  }\n  else {\n    state.tokenize = tokenBase;\n    return null;\n  }\n}\n\nfunction tokenComment(stream, state) {\n  var maybeEnd = false, ch;\n  while (ch = stream.next()) {\n    if (ch == \"/\" && maybeEnd) {\n      state.tokenize = tokenBase;\n      break;\n    }\n    maybeEnd = (ch == \"*\");\n  }\n  return \"comment\";\n}\n\nfunction tokenString(quote) {\n  return function (stream, state) {\n    var escaped = false, next, end = false;\n    while ((next = stream.next()) != null) {\n      if (next == quote && !escaped) {\n        end = true;\n        break;\n      }\n      escaped = !escaped && next == \"\\\\\";\n    }\n    if (end || !escaped)\n      state.tokenize = tokenBase;\n    return \"string\";\n  };\n}\n\nfunction buildElectricInputRegEx() {\n  // Reindentation should occur on [] or on a match of any of\n  // the block closing keywords, at the end of a line.\n  var allClosings = middle.concat(end);\n  return new RegExp(\"[\\\\[\\\\]]|(\" + allClosings.join(\"|\") + \")$\");\n}\n\nexport const oz = {\n  name: \"oz\",\n\n  startState: function () {\n    return {\n      tokenize: tokenBase,\n      currentIndent: 0,\n      doInCurrentLine: false,\n      hasPassedFirstStage: false\n    };\n  },\n\n  token: function (stream, state) {\n    if (stream.sol())\n      state.doInCurrentLine = 0;\n\n    return state.tokenize(stream, state);\n  },\n\n  indent: function (state, textAfter, cx) {\n    var trueText = textAfter.replace(/^\\s+|\\s+$/g, '');\n\n    if (trueText.match(endKeywords) || trueText.match(middleKeywords) || trueText.match(/(\\[])/))\n      return cx.unit * (state.currentIndent - 1);\n\n    if (state.currentIndent < 0)\n      return 0;\n\n    return state.currentIndent * cx.unit\n  },\n\n  languageData: {\n    indentOnInut: buildElectricInputRegEx(),\n    commentTokens: {line: \"%\", block: {open: \"/*\", close: \"*/\"}}\n  }\n};\n"], "names": [], "sourceRoot": ""}