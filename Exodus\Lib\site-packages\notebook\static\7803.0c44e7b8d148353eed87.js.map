{"version": 3, "file": "7803.0c44e7b8d148353eed87.js?v=0c44e7b8d148353eed87", "mappings": ";;;;;;;;;;AAAO;AACP;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA,qDAAqD;AACrD;AACA;AACA;AACA,MAAM;AACN;AACA,0BAA0B;AAC1B;AACA,MAAM;AACN;AACA;AACA,MAAM;AACN,2BAA2B;AAC3B;AACA;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/properties.js"], "sourcesContent": ["export const properties = {\n  name: \"properties\",\n\n  token: function(stream, state) {\n    var sol = stream.sol() || state.afterSection;\n    var eol = stream.eol();\n\n    state.afterSection = false;\n\n    if (sol) {\n      if (state.nextMultiline) {\n        state.inMultiline = true;\n        state.nextMultiline = false;\n      } else {\n        state.position = \"def\";\n      }\n    }\n\n    if (eol && ! state.nextMultiline) {\n      state.inMultiline = false;\n      state.position = \"def\";\n    }\n\n    if (sol) {\n      while(stream.eatSpace()) {}\n    }\n\n    var ch = stream.next();\n\n    if (sol && (ch === \"#\" || ch === \"!\" || ch === \";\")) {\n      state.position = \"comment\";\n      stream.skipToEnd();\n      return \"comment\";\n    } else if (sol && ch === \"[\") {\n      state.afterSection = true;\n      stream.skipTo(\"]\"); stream.eat(\"]\");\n      return \"header\";\n    } else if (ch === \"=\" || ch === \":\") {\n      state.position = \"quote\";\n      return null;\n    } else if (ch === \"\\\\\" && state.position === \"quote\") {\n      if (stream.eol()) {  // end of line?\n        // Multiline value\n        state.nextMultiline = true;\n      }\n    }\n\n    return state.position;\n  },\n\n  startState: function() {\n    return {\n      position : \"def\",       // Current position, \"def\", \"quote\" or \"comment\"\n      nextMultiline : false,  // Is the next line multiline value\n      inMultiline : false,    // Is the current line a multiline value\n      afterSection : false    // Did we just open a section\n    };\n  }\n\n};\n"], "names": [], "sourceRoot": ""}