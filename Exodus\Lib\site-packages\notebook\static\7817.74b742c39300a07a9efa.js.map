{"version": 3, "file": "7817.74b742c39300a07a9efa.js?v=74b742c39300a07a9efa", "mappings": ";;;;;;;;;;AAAA;AACA;AACA;AACA,cAAc,QAAQ;AACtB,GAAG;AACH;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,gCAAgC,QAAQ;AACxC;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,4BAA4B;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,2BAA2B;AACtD;AACA;AACA;AACA;AACA;AACA,2BAA2B,2BAA2B;AACtD;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,OAAO;AACb,WAAW;AACX,4BAA4B;AAC5B;AACA;AACA;AACA;AACA,4BAA4B;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,uCAAuC,mBAAmB;AAC1D;AACA;AACA;;AAEA;AACA;AACA,uCAAuC,mBAAmB;AAC1D;AACA;AACA;AACA;AACA,uCAAuC,mBAAmB;AAC1D;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/forth.js"], "sourcesContent": ["function toWordList(words) {\n  var ret = [];\n  words.split(' ').forEach(function(e){\n    ret.push({name: e});\n  });\n  return ret;\n}\n\nvar coreWordList = toWordList(\n  'INVERT AND OR XOR\\\n 2* 2/ LSHIFT RSHIFT\\\n 0= = 0< < > U< MIN MAX\\\n 2DROP 2DUP 2OVER 2SWAP ?DUP DEPTH DROP DUP OVER ROT SWAP\\\n >R R> R@\\\n + - 1+ 1- ABS NEGATE\\\n S>D * M* UM*\\\n FM/MOD SM/REM UM/MOD */ */MOD / /MOD MOD\\\n HERE , @ ! CELL+ CELLS C, C@ C! CHARS 2@ 2!\\\n ALIGN ALIGNED +! ALLOT\\\n CHAR [CHAR] [ ] BL\\\n FIND EXECUTE IMMEDIATE COUNT LITERAL STATE\\\n ; DOES> >BODY\\\n EVALUATE\\\n SOURCE >IN\\\n <# # #S #> HOLD SIGN BASE >NUMBER HEX DECIMAL\\\n FILL MOVE\\\n . CR EMIT SPACE SPACES TYPE U. .R U.R\\\n ACCEPT\\\n TRUE FALSE\\\n <> U> 0<> 0>\\\n NIP TUCK ROLL PICK\\\n 2>R 2R@ 2R>\\\n WITHIN UNUSED MARKER\\\n I J\\\n TO\\\n COMPILE, [COMPILE]\\\n SAVE-INPUT RESTORE-INPUT\\\n PAD ERASE\\\n 2LITERAL DNEGATE\\\n D- D+ D0< D0= D2* D2/ D< D= DMAX DMIN D>S DABS\\\n M+ M*/ D. D.R 2ROT DU<\\\n CATCH THROW\\\n FREE RESIZE ALLOCATE\\\n CS-PICK CS-ROLL\\\n GET-CURRENT SET-CURRENT FORTH-WORDLIST GET-ORDER SET-ORDER\\\n PREVIOUS SEARCH-WORDLIST WORDLIST FIND ALSO ONLY FORTH DEFINITIONS ORDER\\\n -TRAILING /STRING SEARCH COMPARE CMOVE CMOVE> BLANK SLITERAL');\n\nvar immediateWordList = toWordList('IF ELSE THEN BEGIN WHILE REPEAT UNTIL RECURSE [IF] [ELSE] [THEN] ?DO DO LOOP +LOOP UNLOOP LEAVE EXIT AGAIN CASE OF ENDOF ENDCASE');\n\nfunction searchWordList (wordList, word) {\n  var i;\n  for (i = wordList.length - 1; i >= 0; i--) {\n    if (wordList[i].name === word.toUpperCase()) {\n      return wordList[i];\n    }\n  }\n  return undefined;\n}\nexport const forth = {\n  name: \"forth\",\n  startState: function() {\n    return {\n      state: '',\n      base: 10,\n      coreWordList: coreWordList,\n      immediateWordList: immediateWordList,\n      wordList: []\n    };\n  },\n  token: function (stream, stt) {\n    var mat;\n    if (stream.eatSpace()) {\n      return null;\n    }\n    if (stt.state === '') { // interpretation\n      if (stream.match(/^(\\]|:NONAME)(\\s|$)/i)) {\n        stt.state = ' compilation';\n        return 'builtin';\n      }\n      mat = stream.match(/^(\\:)\\s+(\\S+)(\\s|$)+/);\n      if (mat) {\n        stt.wordList.push({name: mat[2].toUpperCase()});\n        stt.state = ' compilation';\n        return 'def';\n      }\n      mat = stream.match(/^(VARIABLE|2VARIABLE|CONSTANT|2CONSTANT|CREATE|POSTPONE|VALUE|WORD)\\s+(\\S+)(\\s|$)+/i);\n      if (mat) {\n        stt.wordList.push({name: mat[2].toUpperCase()});\n        return 'def';\n      }\n      mat = stream.match(/^(\\'|\\[\\'\\])\\s+(\\S+)(\\s|$)+/);\n      if (mat) {\n        return 'builtin'\n      }\n    } else { // compilation\n      // ; [\n      if (stream.match(/^(\\;|\\[)(\\s)/)) {\n        stt.state = '';\n        stream.backUp(1);\n        return 'builtin';\n      }\n      if (stream.match(/^(\\;|\\[)($)/)) {\n        stt.state = '';\n        return 'builtin';\n      }\n      if (stream.match(/^(POSTPONE)\\s+\\S+(\\s|$)+/)) {\n        return 'builtin';\n      }\n    }\n\n    // dynamic wordlist\n    mat = stream.match(/^(\\S+)(\\s+|$)/);\n    if (mat) {\n      if (searchWordList(stt.wordList, mat[1]) !== undefined) {\n        return 'variable';\n      }\n\n      // comments\n      if (mat[1] === '\\\\') {\n        stream.skipToEnd();\n        return 'comment';\n      }\n\n      // core words\n      if (searchWordList(stt.coreWordList, mat[1]) !== undefined) {\n        return 'builtin';\n      }\n      if (searchWordList(stt.immediateWordList, mat[1]) !== undefined) {\n        return 'keyword';\n      }\n\n      if (mat[1] === '(') {\n        stream.eatWhile(function (s) { return s !== ')'; });\n        stream.eat(')');\n        return 'comment';\n      }\n\n      // // strings\n      if (mat[1] === '.(') {\n        stream.eatWhile(function (s) { return s !== ')'; });\n        stream.eat(')');\n        return 'string';\n      }\n      if (mat[1] === 'S\"' || mat[1] === '.\"' || mat[1] === 'C\"') {\n        stream.eatWhile(function (s) { return s !== '\"'; });\n        stream.eat('\"');\n        return 'string';\n      }\n\n      // numbers\n      if (mat[1] - 0xfffffffff) {\n        return 'number';\n      }\n      // if (mat[1].match(/^[-+]?[0-9]+\\.[0-9]*/)) {\n      //     return 'number';\n      // }\n\n      return 'atom';\n    }\n  }\n};\n"], "names": [], "sourceRoot": ""}