{"version": 3, "file": "8678.9d28c7fae2ee6c13d2eb.js?v=9d28c7fae2ee6c13d2eb", "mappings": ";;;;;;;;;;;;;;;;;;;;AAK8B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;;AAE9B;AACA;AACA,UAAU,+EAAoB;AAC9B;AACA,eAAe,kEAAO;AACtB,GAAG;AACH,YAAY,2FAAgC;AAC5C,UAAU,yEAAc;AACxB,wBAAwB,sEAAM;AAC9B;AACA;AACA;AACA;AACA,GAAG;AACH;AAGE", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mermaid/dist/chunks/mermaid.core/classDiagram-v2-COTLJTTW.mjs"], "sourcesContent": ["import {\n  ClassDB,\n  classDiagram_default,\n  classRenderer_v3_unified_default,\n  styles_default\n} from \"./chunk-A2AXSNBT.mjs\";\nimport \"./chunk-RZ5BOZE2.mjs\";\nimport \"./chunk-TYCBKAJE.mjs\";\nimport \"./chunk-IIMUDSI4.mjs\";\nimport \"./chunk-VV3M67IP.mjs\";\nimport \"./chunk-HRU6DDCH.mjs\";\nimport \"./chunk-K557N5IZ.mjs\";\nimport \"./chunk-H2D2JQ3I.mjs\";\nimport \"./chunk-C3MQ5ANM.mjs\";\nimport \"./chunk-O4NI6UNU.mjs\";\nimport {\n  __name\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/diagrams/class/classDiagram-v2.ts\nvar diagram = {\n  parser: classDiagram_default,\n  get db() {\n    return new ClassDB();\n  },\n  renderer: classRenderer_v3_unified_default,\n  styles: styles_default,\n  init: /* @__PURE__ */ __name((cnf) => {\n    if (!cnf.class) {\n      cnf.class = {};\n    }\n    cnf.class.arrowMarkerAbsolute = cnf.arrowMarkerAbsolute;\n  }, \"init\")\n};\nexport {\n  diagram\n};\n"], "names": [], "sourceRoot": ""}