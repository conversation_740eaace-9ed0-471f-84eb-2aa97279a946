{"version": 3, "file": "9233.916f96402862a0190f46.js?v=916f96402862a0190f46", "mappings": ";;;;;;;;;;AAAA;AACA;;AAEO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,0BAA0B;AAC1B,MAAM,0BAA0B;AAChC;AACA;AACA;AACA;AACA;AACA,gCAAgC;AAChC;AACA,iCAAiC;AACjC;AACA,sCAAsC;AACtC;AACA;AACA,0BAA0B,GAAG;AAC7B,kBAAkB;AAClB;AACA,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,4CAA4C,sBAAsB;AAClE;AACA,yDAAyD;AACzD;AACA,4EAA4E;AAC5E,2EAA2E,QAAQ;AACnF;AACA,wCAAwC;AACxC;;AAEA;AACA,oDAAoD,6BAA6B;AACjF;AACA;AACA;AACA;AACA,+CAA+C,wBAAwB;;AAEvE;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,oBAAoB;AACpB;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/yaml.js"], "sourcesContent": ["var cons = ['true', 'false', 'on', 'off', 'yes', 'no'];\nvar keywordRegex = new RegExp(\"\\\\b((\"+cons.join(\")|(\")+\"))$\", 'i');\n\nexport const yaml = {\n  name: \"yaml\",\n  token: function(stream, state) {\n    var ch = stream.peek();\n    var esc = state.escaped;\n    state.escaped = false;\n    /* comments */\n    if (ch == \"#\" && (stream.pos == 0 || /\\s/.test(stream.string.charAt(stream.pos - 1)))) {\n      stream.skipToEnd();\n      return \"comment\";\n    }\n\n    if (stream.match(/^('([^']|\\\\.)*'?|\"([^\"]|\\\\.)*\"?)/))\n      return \"string\";\n\n    if (state.literal && stream.indentation() > state.keyCol) {\n      stream.skipToEnd(); return \"string\";\n    } else if (state.literal) { state.literal = false; }\n    if (stream.sol()) {\n      state.keyCol = 0;\n      state.pair = false;\n      state.pairStart = false;\n      /* document start */\n      if(stream.match('---')) { return \"def\"; }\n      /* document end */\n      if (stream.match('...')) { return \"def\"; }\n      /* array list item */\n      if (stream.match(/^\\s*-\\s+/)) { return 'meta'; }\n    }\n    /* inline pairs/lists */\n    if (stream.match(/^(\\{|\\}|\\[|\\])/)) {\n      if (ch == '{')\n        state.inlinePairs++;\n      else if (ch == '}')\n        state.inlinePairs--;\n      else if (ch == '[')\n        state.inlineList++;\n      else\n        state.inlineList--;\n      return 'meta';\n    }\n\n    /* list separator */\n    if (state.inlineList > 0 && !esc && ch == ',') {\n      stream.next();\n      return 'meta';\n    }\n    /* pairs separator */\n    if (state.inlinePairs > 0 && !esc && ch == ',') {\n      state.keyCol = 0;\n      state.pair = false;\n      state.pairStart = false;\n      stream.next();\n      return 'meta';\n    }\n\n    /* start of value of a pair */\n    if (state.pairStart) {\n      /* block literals */\n      if (stream.match(/^\\s*(\\||\\>)\\s*/)) { state.literal = true; return 'meta'; };\n      /* references */\n      if (stream.match(/^\\s*(\\&|\\*)[a-z0-9\\._-]+\\b/i)) { return 'variable'; }\n      /* numbers */\n      if (state.inlinePairs == 0 && stream.match(/^\\s*-?[0-9\\.\\,]+\\s?$/)) { return 'number'; }\n      if (state.inlinePairs > 0 && stream.match(/^\\s*-?[0-9\\.\\,]+\\s?(?=(,|}))/)) { return 'number'; }\n      /* keywords */\n      if (stream.match(keywordRegex)) { return 'keyword'; }\n    }\n\n    /* pairs (associative arrays) -> key */\n    if (!state.pair && stream.match(/^\\s*(?:[,\\[\\]{}&*!|>'\"%@`][^\\s'\":]|[^,\\[\\]{}#&*!|>'\"%@`])[^#]*?(?=\\s*:($|\\s))/)) {\n      state.pair = true;\n      state.keyCol = stream.indentation();\n      return \"atom\";\n    }\n    if (state.pair && stream.match(/^:\\s*/)) { state.pairStart = true; return 'meta'; }\n\n    /* nothing found, continue */\n    state.pairStart = false;\n    state.escaped = (ch == '\\\\');\n    stream.next();\n    return null;\n  },\n  startState: function() {\n    return {\n      pair: false,\n      pairStart: false,\n      keyCol: 0,\n      inlinePairs: 0,\n      inlineList: 0,\n      literal: false,\n      escaped: false\n    };\n  },\n  languageData: {\n    commentTokens: {line: \"#\"}\n  }\n};\n"], "names": [], "sourceRoot": ""}