"use strict";(self["webpackChunk_jupyterlab_application_top"]=self["webpackChunk_jupyterlab_application_top"]||[]).push([[4462],{84462:(e,t,r)=>{r.r(t);r.d(t,{stylus:()=>se});var i=["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","bgsound","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","marquee","menu","menuitem","meta","meter","nav","nobr","noframes","noscript","object","ol","optgroup","option","output","p","param","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","tr","track","u","ul","var","video"];var a=["domain","regexp","url-prefix","url"];var n=["all","aural","braille","handheld","print","projection","screen","tty","tv","embossed"];var o=["width","min-width","max-width","height","min-height","max-height","device-width","min-device-width","max-device-width","device-height","min-device-height","max-device-height","aspect-ratio","min-aspect-ratio","max-aspect-ratio","device-aspect-ratio","min-device-aspect-ratio","max-device-aspect-ratio","color","min-color","max-color","color-index","min-color-index","max-color-index","monochrome","min-monochrome","max-monochrome","resolution","min-resolution","max-resolution","scan","grid","dynamic-range","video-dynamic-range"];var l=["align-content","align-items","align-self","alignment-adjust","alignment-baseline","anchor-point","animation","animation-delay","animation-direction","animation-duration","animation-fill-mode","animation-iteration-count","animation-name","animation-play-state","animation-timing-function","appearance","azimuth","backface-visibility","background","background-attachment","background-clip","background-color","background-image","background-origin","background-position","background-repeat","background-size","baseline-shift","binding","bleed","bookmark-label","bookmark-level","bookmark-state","bookmark-target","border","border-bottom","border-bottom-color","border-bottom-left-radius","border-bottom-right-radius","border-bottom-style","border-bottom-width","border-collapse","border-color","border-image","border-image-outset","border-image-repeat","border-image-slice","border-image-source","border-image-width","border-left","border-left-color","border-left-style","border-left-width","border-radius","border-right","border-right-color","border-right-style","border-right-width","border-spacing","border-style","border-top","border-top-color","border-top-left-radius","border-top-right-radius","border-top-style","border-top-width","border-width","bottom","box-decoration-break","box-shadow","box-sizing","break-after","break-before","break-inside","caption-side","clear","clip","color","color-profile","column-count","column-fill","column-gap","column-rule","column-rule-color","column-rule-style","column-rule-width","column-span","column-width","columns","content","counter-increment","counter-reset","crop","cue","cue-after","cue-before","cursor","direction","display","dominant-baseline","drop-initial-after-adjust","drop-initial-after-align","drop-initial-before-adjust","drop-initial-before-align","drop-initial-size","drop-initial-value","elevation","empty-cells","fit","fit-position","flex","flex-basis","flex-direction","flex-flow","flex-grow","flex-shrink","flex-wrap","float","float-offset","flow-from","flow-into","font","font-feature-settings","font-family","font-kerning","font-language-override","font-size","font-size-adjust","font-stretch","font-style","font-synthesis","font-variant","font-variant-alternates","font-variant-caps","font-variant-east-asian","font-variant-ligatures","font-variant-numeric","font-variant-position","font-weight","grid","grid-area","grid-auto-columns","grid-auto-flow","grid-auto-position","grid-auto-rows","grid-column","grid-column-end","grid-column-start","grid-row","grid-row-end","grid-row-start","grid-template","grid-template-areas","grid-template-columns","grid-template-rows","hanging-punctuation","height","hyphens","icon","image-orientation","image-rendering","image-resolution","inline-box-align","justify-content","left","letter-spacing","line-break","line-height","line-stacking","line-stacking-ruby","line-stacking-shift","line-stacking-strategy","list-style","list-style-image","list-style-position","list-style-type","margin","margin-bottom","margin-left","margin-right","margin-top","marker-offset","marks","marquee-direction","marquee-loop","marquee-play-count","marquee-speed","marquee-style","max-height","max-width","min-height","min-width","move-to","nav-down","nav-index","nav-left","nav-right","nav-up","object-fit","object-position","opacity","order","orphans","outline","outline-color","outline-offset","outline-style","outline-width","overflow","overflow-style","overflow-wrap","overflow-x","overflow-y","padding","padding-bottom","padding-left","padding-right","padding-top","page","page-break-after","page-break-before","page-break-inside","page-policy","pause","pause-after","pause-before","perspective","perspective-origin","pitch","pitch-range","play-during","position","presentation-level","punctuation-trim","quotes","region-break-after","region-break-before","region-break-inside","region-fragment","rendering-intent","resize","rest","rest-after","rest-before","richness","right","rotation","rotation-point","ruby-align","ruby-overhang","ruby-position","ruby-span","shape-image-threshold","shape-inside","shape-margin","shape-outside","size","speak","speak-as","speak-header","speak-numeral","speak-punctuation","speech-rate","stress","string-set","tab-size","table-layout","target","target-name","target-new","target-position","text-align","text-align-last","text-decoration","text-decoration-color","text-decoration-line","text-decoration-skip","text-decoration-style","text-emphasis","text-emphasis-color","text-emphasis-position","text-emphasis-style","text-height","text-indent","text-justify","text-outline","text-overflow","text-shadow","text-size-adjust","text-space-collapse","text-transform","text-underline-position","text-wrap","top","transform","transform-origin","transform-style","transition","transition-delay","transition-duration","transition-property","transition-timing-function","unicode-bidi","vertical-align","visibility","voice-balance","voice-duration","voice-family","voice-pitch","voice-range","voice-rate","voice-stress","voice-volume","volume","white-space","widows","width","will-change","word-break","word-spacing","word-wrap","z-index","clip-path","clip-rule","mask","enable-background","filter","flood-color","flood-opacity","lighting-color","stop-color","stop-opacity","pointer-events","color-interpolation","color-interpolation-filters","color-rendering","fill","fill-opacity","fill-rule","image-rendering","marker","marker-end","marker-mid","marker-start","shape-rendering","stroke","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke-width","text-rendering","baseline-shift","dominant-baseline","glyph-orientation-horizontal","glyph-orientation-vertical","text-anchor","writing-mode","font-smoothing","osx-font-smoothing"];var s=["scrollbar-arrow-color","scrollbar-base-color","scrollbar-dark-shadow-color","scrollbar-face-color","scrollbar-highlight-color","scrollbar-shadow-color","scrollbar-3d-light-color","scrollbar-track-color","shape-inside","searchfield-cancel-button","searchfield-decoration","searchfield-results-button","searchfield-results-decoration","zoom"];var c=["font-family","src","unicode-range","font-variant","font-feature-settings","font-stretch","font-weight","font-style"];var u=["aliceblue","antiquewhite","aqua","aquamarine","azure","beige","bisque","black","blanchedalmond","blue","blueviolet","brown","burlywood","cadetblue","chartreuse","chocolate","coral","cornflowerblue","cornsilk","crimson","cyan","darkblue","darkcyan","darkgoldenrod","darkgray","darkgreen","darkkhaki","darkmagenta","darkolivegreen","darkorange","darkorchid","darkred","darksalmon","darkseagreen","darkslateblue","darkslategray","darkturquoise","darkviolet","deeppink","deepskyblue","dimgray","dodgerblue","firebrick","floralwhite","forestgreen","fuchsia","gainsboro","ghostwhite","gold","goldenrod","gray","grey","green","greenyellow","honeydew","hotpink","indianred","indigo","ivory","khaki","lavender","lavenderblush","lawngreen","lemonchiffon","lightblue","lightcoral","lightcyan","lightgoldenrodyellow","lightgray","lightgreen","lightpink","lightsalmon","lightseagreen","lightskyblue","lightslategray","lightsteelblue","lightyellow","lime","limegreen","linen","magenta","maroon","mediumaquamarine","mediumblue","mediumorchid","mediumpurple","mediumseagreen","mediumslateblue","mediumspringgreen","mediumturquoise","mediumvioletred","midnightblue","mintcream","mistyrose","moccasin","navajowhite","navy","oldlace","olive","olivedrab","orange","orangered","orchid","palegoldenrod","palegreen","paleturquoise","palevioletred","papayawhip","peachpuff","peru","pink","plum","powderblue","purple","rebeccapurple","red","rosybrown","royalblue","saddlebrown","salmon","sandybrown","seagreen","seashell","sienna","silver","skyblue","slateblue","slategray","snow","springgreen","steelblue","tan","teal","thistle","tomato","turquoise","violet","wheat","white","whitesmoke","yellow","yellowgreen"];var d=["above","absolute","activeborder","additive","activecaption","afar","after-white-space","ahead","alias","all","all-scroll","alphabetic","alternate","always","amharic","amharic-abegede","antialiased","appworkspace","arabic-indic","armenian","asterisks","attr","auto","avoid","avoid-column","avoid-page","avoid-region","background","backwards","baseline","below","bidi-override","binary","bengali","blink","block","block-axis","bold","bolder","border","border-box","both","bottom","break","break-all","break-word","bullets","button","buttonface","buttonhighlight","buttonshadow","buttontext","calc","cambodian","capitalize","caps-lock-indicator","caption","captiontext","caret","cell","center","checkbox","circle","cjk-decimal","cjk-earthly-branch","cjk-heavenly-stem","cjk-ideographic","clear","clip","close-quote","col-resize","collapse","column","compact","condensed","conic-gradient","contain","content","contents","content-box","context-menu","continuous","copy","counter","counters","cover","crop","cross","crosshair","currentcolor","cursive","cyclic","dashed","decimal","decimal-leading-zero","default","default-button","destination-atop","destination-in","destination-out","destination-over","devanagari","disc","discard","disclosure-closed","disclosure-open","document","dot-dash","dot-dot-dash","dotted","double","down","e-resize","ease","ease-in","ease-in-out","ease-out","element","ellipse","ellipsis","embed","end","ethiopic","ethiopic-abegede","ethiopic-abegede-am-et","ethiopic-abegede-gez","ethiopic-abegede-ti-er","ethiopic-abegede-ti-et","ethiopic-halehame-aa-er","ethiopic-halehame-aa-et","ethiopic-halehame-am-et","ethiopic-halehame-gez","ethiopic-halehame-om-et","ethiopic-halehame-sid-et","ethiopic-halehame-so-et","ethiopic-halehame-ti-er","ethiopic-halehame-ti-et","ethiopic-halehame-tig","ethiopic-numeric","ew-resize","expanded","extends","extra-condensed","extra-expanded","fantasy","fast","fill","fixed","flat","flex","footnotes","forwards","from","geometricPrecision","georgian","graytext","groove","gujarati","gurmukhi","hand","hangul","hangul-consonant","hebrew","help","hidden","hide","high","higher","highlight","highlighttext","hiragana","hiragana-iroha","horizontal","hsl","hsla","icon","ignore","inactiveborder","inactivecaption","inactivecaptiontext","infinite","infobackground","infotext","inherit","initial","inline","inline-axis","inline-block","inline-flex","inline-table","inset","inside","intrinsic","invert","italic","japanese-formal","japanese-informal","justify","kannada","katakana","katakana-iroha","keep-all","khmer","korean-hangul-formal","korean-hanja-formal","korean-hanja-informal","landscape","lao","large","larger","left","level","lighter","line-through","linear","linear-gradient","lines","list-item","listbox","listitem","local","logical","loud","lower","lower-alpha","lower-armenian","lower-greek","lower-hexadecimal","lower-latin","lower-norwegian","lower-roman","lowercase","ltr","malayalam","match","matrix","matrix3d","media-play-button","media-slider","media-sliderthumb","media-volume-slider","media-volume-sliderthumb","medium","menu","menulist","menulist-button","menutext","message-box","middle","min-intrinsic","mix","mongolian","monospace","move","multiple","myanmar","n-resize","narrower","ne-resize","nesw-resize","no-close-quote","no-drop","no-open-quote","no-repeat","none","normal","not-allowed","nowrap","ns-resize","numbers","numeric","nw-resize","nwse-resize","oblique","octal","open-quote","optimizeLegibility","optimizeSpeed","oriya","oromo","outset","outside","outside-shape","overlay","overline","padding","padding-box","painted","page","paused","persian","perspective","plus-darker","plus-lighter","pointer","polygon","portrait","pre","pre-line","pre-wrap","preserve-3d","progress","push-button","radial-gradient","radio","read-only","read-write","read-write-plaintext-only","rectangle","region","relative","repeat","repeating-linear-gradient","repeating-radial-gradient","repeating-conic-gradient","repeat-x","repeat-y","reset","reverse","rgb","rgba","ridge","right","rotate","rotate3d","rotateX","rotateY","rotateZ","round","row-resize","rtl","run-in","running","s-resize","sans-serif","scale","scale3d","scaleX","scaleY","scaleZ","scroll","scrollbar","scroll-position","se-resize","searchfield","searchfield-cancel-button","searchfield-decoration","searchfield-results-button","searchfield-results-decoration","semi-condensed","semi-expanded","separate","serif","show","sidama","simp-chinese-formal","simp-chinese-informal","single","skew","skewX","skewY","skip-white-space","slide","slider-horizontal","slider-vertical","sliderthumb-horizontal","sliderthumb-vertical","slow","small","small-caps","small-caption","smaller","solid","somali","source-atop","source-in","source-out","source-over","space","spell-out","square","square-button","standard","start","static","status-bar","stretch","stroke","sub","subpixel-antialiased","super","sw-resize","symbolic","symbols","table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row","table-row-group","tamil","telugu","text","text-bottom","text-top","textarea","textfield","thai","thick","thin","threeddarkshadow","threedface","threedhighlight","threedlightshadow","threedshadow","tibetan","tigre","tigrinya-er","tigrinya-er-abegede","tigrinya-et","tigrinya-et-abegede","to","top","trad-chinese-formal","trad-chinese-informal","translate","translate3d","translateX","translateY","translateZ","transparent","ultra-condensed","ultra-expanded","underline","up","upper-alpha","upper-armenian","upper-greek","upper-hexadecimal","upper-latin","upper-norwegian","upper-roman","uppercase","urdu","url","var","vertical","vertical-text","visible","visibleFill","visiblePainted","visibleStroke","visual","w-resize","wait","wave","wider","window","windowframe","windowtext","words","x-large","x-small","xor","xx-large","xx-small","bicubic","optimizespeed","grayscale","row","row-reverse","wrap","wrap-reverse","column-reverse","flex-start","flex-end","space-between","space-around","unset"];var m=["in","and","or","not","is not","is a","is","isnt","defined","if unless"],p=["for","if","else","unless","from","to"],f=["null","true","false","href","title","type","not-allowed","readonly","disabled"],h=["@font-face","@keyframes","@media","@viewport","@page","@host","@supports","@block","@css"];var b=i.concat(a,n,o,l,s,u,d,c,m,p,f,h);function g(e){e=e.sort((function(e,t){return t>e}));return new RegExp("^(("+e.join(")|(")+"))\\b")}function k(e){var t={};for(var r=0;r<e.length;++r)t[e[r]]=true;return t}function w(e){return e.replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&")}var v=k(i),y=/^(a|b|i|s|col|em)$/i,x=k(l),z=k(s),q=k(d),j=k(u),$=k(a),_=g(a),B=k(o),C=k(n),N=k(c),L=/^\s*([.]{2,3}|&&|\|\||\*\*|[?!=:]?=|[-+*\/%<>]=?|\?:|\~)/,P=g(m),U=k(p),E=new RegExp(/^\-(moz|ms|o|webkit)-/i),O=k(f),W="",A={},R,S,X,Y;function Z(e,t){W=e.string.match(/(^[\w-]+\s*=\s*$)|(^\s*[\w-]+\s*=\s*[\w-])|(^\s*(\.|#|@|\$|\&|\[|\d|\+|::?|\{|\>|~|\/)?\s*[\w-]*([a-z0-9-]|\*|\/\*)(\(|,)?)/);t.context.line.firstWord=W?W[0].replace(/^\s*/,""):"";t.context.line.indent=e.indentation();R=e.peek();if(e.match("//")){e.skipToEnd();return["comment","comment"]}if(e.match("/*")){t.tokenize=T;return T(e,t)}if(R=='"'||R=="'"){e.next();t.tokenize=D(R);return t.tokenize(e,t)}if(R=="@"){e.next();e.eatWhile(/[\w\\-]/);return["def",e.current()]}if(R=="#"){e.next();if(e.match(/^[0-9a-f]{3}([0-9a-f]([0-9a-f]{2}){0,2})?\b(?!-)/i)){return["atom","atom"]}if(e.match(/^[a-z][\w-]*/i)){return["builtin","hash"]}}if(e.match(E)){return["meta","vendor-prefixes"]}if(e.match(/^-?[0-9]?\.?[0-9]/)){e.eatWhile(/[a-z%]/i);return["number","unit"]}if(R=="!"){e.next();return[e.match(/^(important|optional)/i)?"keyword":"operator","important"]}if(R=="."&&e.match(/^\.[a-z][\w-]*/i)){return["qualifier","qualifier"]}if(e.match(_)){if(e.peek()=="(")t.tokenize=F;return["property","word"]}if(e.match(/^[a-z][\w-]*\(/i)){e.backUp(1);return["keyword","mixin"]}if(e.match(/^(\+|-)[a-z][\w-]*\(/i)){e.backUp(1);return["keyword","block-mixin"]}if(e.string.match(/^\s*&/)&&e.match(/^[-_]+[a-z][\w-]*/)){return["qualifier","qualifier"]}if(e.match(/^(\/|&)(-|_|:|\.|#|[a-z])/)){e.backUp(1);return["variableName.special","reference"]}if(e.match(/^&{1}\s*$/)){return["variableName.special","reference"]}if(e.match(P)){return["operator","operator"]}if(e.match(/^\$?[-_]*[a-z0-9]+[\w-]*/i)){if(e.match(/^(\.|\[)[\w-\'\"\]]+/i,false)){if(!M(e.current())){e.match(".");return["variable","variable-name"]}}return["variable","word"]}if(e.match(L)){return["operator",e.current()]}if(/[:;,{}\[\]\(\)]/.test(R)){e.next();return[null,R]}e.next();return[null,null]}function T(e,t){var r=false,i;while((i=e.next())!=null){if(r&&i=="/"){t.tokenize=null;break}r=i=="*"}return["comment","comment"]}function D(e){return function(t,r){var i=false,a;while((a=t.next())!=null){if(a==e&&!i){if(e==")")t.backUp(1);break}i=!i&&a=="\\"}if(a==e||!i&&e!=")")r.tokenize=null;return["string","string"]}}function F(e,t){e.next();if(!e.match(/\s*[\"\')]/,false))t.tokenize=D(")");else t.tokenize=null;return[null,"("]}function I(e,t,r,i){this.type=e;this.indent=t;this.prev=r;this.line=i||{firstWord:"",indent:0}}function G(e,t,r,i){i=i>=0?i:t.indentUnit;e.context=new I(r,t.indentation()+i,e.context);return r}function H(e,t,r){var i=e.context.indent-t.indentUnit;r=r||false;e.context=e.context.prev;if(r)e.context.indent=i;return e.context.type}function J(e,t,r){return A[r.context.type](e,t,r)}function K(e,t,r,i){for(var a=i||1;a>0;a--)r.context=r.context.prev;return J(e,t,r)}function M(e){return e.toLowerCase()in v}function Q(e){e=e.toLowerCase();return e in x||e in N}function V(e){return e.toLowerCase()in U}function ee(e){return e.toLowerCase().match(E)}function te(e){var t=e.toLowerCase();var r="variable";if(M(e))r="tag";else if(V(e))r="block-keyword";else if(Q(e))r="property";else if(t in q||t in O)r="atom";else if(t=="return"||t in j)r="keyword";else if(e.match(/^[A-Z]/))r="string";return r}function re(e,t){return oe(t)&&(e=="{"||e=="]"||e=="hash"||e=="qualifier")||e=="block-mixin"}function ie(e,t){return e=="{"&&t.match(/^\s*\$?[\w-]+/i,false)}function ae(e,t){return e==":"&&t.match(/^[a-z-]+/,false)}function ne(e){return e.sol()||e.string.match(new RegExp("^\\s*"+w(e.current())))}function oe(e){return e.eol()||e.match(/^\s*$/,false)}function le(e){var t=/^\s*[-_]*[a-z0-9]+[\w-]*/i;var r=typeof e=="string"?e.match(t):e.string.match(t);return r?r[0].replace(/^\s*/,""):""}A.block=function(e,t,r){if(e=="comment"&&ne(t)||e==","&&oe(t)||e=="mixin"){return G(r,t,"block",0)}if(ie(e,t)){return G(r,t,"interpolation")}if(oe(t)&&e=="]"){if(!/^\s*(\.|#|:|\[|\*|&)/.test(t.string)&&!M(le(t))){return G(r,t,"block",0)}}if(re(e,t)){return G(r,t,"block")}if(e=="}"&&oe(t)){return G(r,t,"block",0)}if(e=="variable-name"){if(t.string.match(/^\s?\$[\w-\.\[\]\'\"]+$/)||V(le(t))){return G(r,t,"variableName")}else{return G(r,t,"variableName",0)}}if(e=="="){if(!oe(t)&&!V(le(t))){return G(r,t,"block",0)}return G(r,t,"block")}if(e=="*"){if(oe(t)||t.match(/\s*(,|\.|#|\[|:|{)/,false)){Y="tag";return G(r,t,"block")}}if(ae(e,t)){return G(r,t,"pseudo")}if(/@(font-face|media|supports|(-moz-)?document)/.test(e)){return G(r,t,oe(t)?"block":"atBlock")}if(/@(-(moz|ms|o|webkit)-)?keyframes$/.test(e)){return G(r,t,"keyframes")}if(/@extends?/.test(e)){return G(r,t,"extend",0)}if(e&&e.charAt(0)=="@"){if(t.indentation()>0&&Q(t.current().slice(1))){Y="variable";return"block"}if(/(@import|@require|@charset)/.test(e)){return G(r,t,"block",0)}return G(r,t,"block")}if(e=="reference"&&oe(t)){return G(r,t,"block")}if(e=="("){return G(r,t,"parens")}if(e=="vendor-prefixes"){return G(r,t,"vendorPrefixes")}if(e=="word"){var i=t.current();Y=te(i);if(Y=="property"){if(ne(t)){return G(r,t,"block",0)}else{Y="atom";return"block"}}if(Y=="tag"){if(/embed|menu|pre|progress|sub|table/.test(i)){if(Q(le(t))){Y="atom";return"block"}}if(t.string.match(new RegExp("\\[\\s*"+i+"|"+i+"\\s*\\]"))){Y="atom";return"block"}if(y.test(i)){if(ne(t)&&t.string.match(/=/)||!ne(t)&&!t.string.match(/^(\s*\.|#|\&|\[|\/|>|\*)/)&&!M(le(t))){Y="variable";if(V(le(t)))return"block";return G(r,t,"block",0)}}if(oe(t))return G(r,t,"block")}if(Y=="block-keyword"){Y="keyword";if(t.current(/(if|unless)/)&&!ne(t)){return"block"}return G(r,t,"block")}if(i=="return")return G(r,t,"block",0);if(Y=="variable"&&t.string.match(/^\s?\$[\w-\.\[\]\'\"]+$/)){return G(r,t,"block")}}return r.context.type};A.parens=function(e,t,r){if(e=="(")return G(r,t,"parens");if(e==")"){if(r.context.prev.type=="parens"){return H(r,t)}if(t.string.match(/^[a-z][\w-]*\(/i)&&oe(t)||V(le(t))||/(\.|#|:|\[|\*|&|>|~|\+|\/)/.test(le(t))||!t.string.match(/^-?[a-z][\w-\.\[\]\'\"]*\s*=/)&&M(le(t))){return G(r,t,"block")}if(t.string.match(/^[\$-]?[a-z][\w-\.\[\]\'\"]*\s*=/)||t.string.match(/^\s*(\(|\)|[0-9])/)||t.string.match(/^\s+[a-z][\w-]*\(/i)||t.string.match(/^\s+[\$-]?[a-z]/i)){return G(r,t,"block",0)}if(oe(t))return G(r,t,"block");else return G(r,t,"block",0)}if(e&&e.charAt(0)=="@"&&Q(t.current().slice(1))){Y="variable"}if(e=="word"){var i=t.current();Y=te(i);if(Y=="tag"&&y.test(i)){Y="variable"}if(Y=="property"||i=="to")Y="atom"}if(e=="variable-name"){return G(r,t,"variableName")}if(ae(e,t)){return G(r,t,"pseudo")}return r.context.type};A.vendorPrefixes=function(e,t,r){if(e=="word"){Y="property";return G(r,t,"block",0)}return H(r,t)};A.pseudo=function(e,t,r){if(!Q(le(t.string))){t.match(/^[a-z-]+/);Y="variableName.special";if(oe(t))return G(r,t,"block");return H(r,t)}return K(e,t,r)};A.atBlock=function(e,t,r){if(e=="(")return G(r,t,"atBlock_parens");if(re(e,t)){return G(r,t,"block")}if(ie(e,t)){return G(r,t,"interpolation")}if(e=="word"){var i=t.current().toLowerCase();if(/^(only|not|and|or)$/.test(i))Y="keyword";else if($.hasOwnProperty(i))Y="tag";else if(C.hasOwnProperty(i))Y="attribute";else if(B.hasOwnProperty(i))Y="property";else if(z.hasOwnProperty(i))Y="string.special";else Y=te(t.current());if(Y=="tag"&&oe(t)){return G(r,t,"block")}}if(e=="operator"&&/^(not|and|or)$/.test(t.current())){Y="keyword"}return r.context.type};A.atBlock_parens=function(e,t,r){if(e=="{"||e=="}")return r.context.type;if(e==")"){if(oe(t))return G(r,t,"block");else return G(r,t,"atBlock")}if(e=="word"){var i=t.current().toLowerCase();Y=te(i);if(/^(max|min)/.test(i))Y="property";if(Y=="tag"){y.test(i)?Y="variable":Y="atom"}return r.context.type}return A.atBlock(e,t,r)};A.keyframes=function(e,t,r){if(t.indentation()=="0"&&(e=="}"&&ne(t)||e=="]"||e=="hash"||e=="qualifier"||M(t.current()))){return K(e,t,r)}if(e=="{")return G(r,t,"keyframes");if(e=="}"){if(ne(t))return H(r,t,true);else return G(r,t,"keyframes")}if(e=="unit"&&/^[0-9]+\%$/.test(t.current())){return G(r,t,"keyframes")}if(e=="word"){Y=te(t.current());if(Y=="block-keyword"){Y="keyword";return G(r,t,"keyframes")}}if(/@(font-face|media|supports|(-moz-)?document)/.test(e)){return G(r,t,oe(t)?"block":"atBlock")}if(e=="mixin"){return G(r,t,"block",0)}return r.context.type};A.interpolation=function(e,t,r){if(e=="{")H(r,t)&&G(r,t,"block");if(e=="}"){if(t.string.match(/^\s*(\.|#|:|\[|\*|&|>|~|\+|\/)/i)||t.string.match(/^\s*[a-z]/i)&&M(le(t))){return G(r,t,"block")}if(!t.string.match(/^(\{|\s*\&)/)||t.match(/\s*[\w-]/,false)){return G(r,t,"block",0)}return G(r,t,"block")}if(e=="variable-name"){return G(r,t,"variableName",0)}if(e=="word"){Y=te(t.current());if(Y=="tag")Y="atom"}return r.context.type};A.extend=function(e,t,r){if(e=="["||e=="=")return"extend";if(e=="]")return H(r,t);if(e=="word"){Y=te(t.current());return"extend"}return H(r,t)};A.variableName=function(e,t,r){if(e=="string"||e=="["||e=="]"||t.current().match(/^(\.|\$)/)){if(t.current().match(/^\.[\w-]+/i))Y="variable";return"variableName"}return K(e,t,r)};const se={name:"stylus",startState:function(){return{tokenize:null,state:"block",context:new I("block",0,null)}},token:function(e,t){if(!t.tokenize&&e.eatSpace())return null;S=(t.tokenize||Z)(e,t);if(S&&typeof S=="object"){X=S[1];S=S[0]}Y=S;t.state=A[t.state](X,e,t);return Y},indent:function(e,t,r){var i=e.context,a=t&&t.charAt(0),n=i.indent,o=le(t),l=i.line.indent,s=e.context.prev?e.context.prev.line.firstWord:"",c=e.context.prev?e.context.prev.line.indent:l;if(i.prev&&(a=="}"&&(i.type=="block"||i.type=="atBlock"||i.type=="keyframes")||a==")"&&(i.type=="parens"||i.type=="atBlock_parens")||a=="{"&&i.type=="at")){n=i.indent-r.unit}else if(!/(\})/.test(a)){if(/@|\$|\d/.test(a)||/^\{/.test(t)||/^\s*\/(\/|\*)/.test(t)||/^\s*\/\*/.test(s)||/^\s*[\w-\.\[\]\'\"]+\s*(\?|:|\+)?=/i.test(t)||/^(\+|-)?[a-z][\w-]*\(/i.test(t)||/^return/.test(t)||V(o)){n=l}else if(/(\.|#|:|\[|\*|&|>|~|\+|\/)/.test(a)||M(o)){if(/\,\s*$/.test(s)){n=c}else if(/(\.|#|:|\[|\*|&|>|~|\+|\/)/.test(s)||M(s)){n=l<=c?c:c+r.unit}else{n=l}}else if(!/,\s*$/.test(t)&&(ee(o)||Q(o))){if(V(s)){n=l<=c?c:c+r.unit}else if(/^\{/.test(s)){n=l<=c?l:c+r.unit}else if(ee(s)||Q(s)){n=l>=c?c:l}else if(/^(\.|#|:|\[|\*|&|@|\+|\-|>|~|\/)/.test(s)||/=\s*$/.test(s)||M(s)||/^\$[\w-\.\[\]\'\"]/.test(s)){n=c+r.unit}else{n=l}}}return n},languageData:{indentOnInput:/^\s*\}$/,commentTokens:{line:"//",block:{open:"/*",close:"*/"}},autocomplete:b}}}}]);