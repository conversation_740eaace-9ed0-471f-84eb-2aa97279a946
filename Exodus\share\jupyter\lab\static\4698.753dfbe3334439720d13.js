(self["webpackChunk_jupyterlab_application_top"]=self["webpackChunk_jupyterlab_application_top"]||[]).push([[4698],{67901:(t,e,r)=>{var n=1/0;var o="[object Symbol]";var u=/[&<>"'`]/g,a=RegExp(u.source);var c={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","`":"&#96;"};var p=typeof r.g=="object"&&r.g&&r.g.Object===Object&&r.g;var f=typeof self=="object"&&self&&self.Object===Object&&self;var l=p||f||Function("return this")();function i(t){return function(e){return t==null?undefined:t[e]}}var b=i(c);var s=Object.prototype;var v=s.toString;var y=l.Symbol;var j=y?y.prototype:undefined,g=j?j.toString:undefined;function d(t){if(typeof t=="string"){return t}if(O(t)){return g?g.call(t):""}var e=t+"";return e=="0"&&1/t==-n?"-0":e}function _(t){return!!t&&typeof t=="object"}function O(t){return typeof t=="symbol"||_(t)&&v.call(t)==o}function h(t){return t==null?"":d(t)}function k(t){t=h(t);return t&&a.test(t)?t.replace(u,b):t}t.exports=k}}]);