(self["webpackChunk_jupyterlab_application_top"]=self["webpackChunk_jupyterlab_application_top"]||[]).push([[7371],{26527:function(t,e,r){(function e(i,n){if(true)t.exports=n(r(41709));else{}})(this,(function(t){return(()=>{"use strict";var e={658:t=>{t.exports=Object.assign!=null?Object.assign.bind(Object):function(t){for(var e=arguments.length,r=Array(e>1?e-1:0),i=1;i<e;i++){r[i-1]=arguments[i]}r.forEach((function(e){Object.keys(e).forEach((function(r){return t[r]=e[r]}))}));return t}},548:(t,e,r)=>{var i=function(){function t(t,e){var r=[];var i=true;var n=false;var o=undefined;try{for(var a=t[Symbol.iterator](),s;!(i=(s=a.next()).done);i=true){r.push(s.value);if(e&&r.length===e)break}}catch(h){n=true;o=h}finally{try{if(!i&&a["return"])a["return"]()}finally{if(n)throw o}}return r}return function(e,r){if(Array.isArray(e)){return e}else if(Symbol.iterator in Object(e)){return t(e,r)}else{throw new TypeError("Invalid attempt to destructure non-iterable instance")}}}();var n=r(140).layoutBase.LinkedList;var o={};o.getTopMostNodes=function(t){var e={};for(var r=0;r<t.length;r++){e[t[r].id()]=true}var i=t.filter((function(t,r){if(typeof t==="number"){t=r}var i=t.parent()[0];while(i!=null){if(e[i.id()]){return false}i=i.parent()[0]}return true}));return i};o.connectComponents=function(t,e,r,i){var o=new n;var a=new Set;var s=[];var h=void 0;var l=void 0;var c=void 0;var d=false;var f=1;var g=[];var u=[];var p=function i(){var n=t.collection();u.push(n);var p=r[0];var v=t.collection();v.merge(p).merge(p.descendants().intersection(e));s.push(p);v.forEach((function(t){o.push(t);a.add(t);n.merge(t)}));var y=function i(){p=o.shift();var l=t.collection();p.neighborhood().nodes().forEach((function(t){if(e.intersection(p.edgesWith(t)).length>0){l.merge(t)}}));for(var c=0;c<l.length;c++){var d=l[c];h=r.intersection(d.union(d.ancestors()));if(h!=null&&!a.has(h[0])){var f=h.union(h.descendants());f.forEach((function(t){o.push(t);a.add(t);n.merge(t);if(r.has(t)){s.push(t)}}))}}};while(o.length!=0){y()}n.forEach((function(t){e.intersection(t.connectedEdges()).forEach((function(t){if(n.has(t.source())&&n.has(t.target())){n.merge(t)}}))}));if(s.length==r.length){d=true}if(!d||d&&f>1){l=s[0];c=l.connectedEdges().length;s.forEach((function(t){if(t.connectedEdges().length<c){c=t.connectedEdges().length;l=t}}));g.push(l.id());var m=t.collection();m.merge(s[0]);s.forEach((function(t){m.merge(t)}));s=[];r=r.difference(m);f++}};do{p()}while(!d);if(i){if(g.length>0){i.set("dummy"+(i.size+1),g)}}return u};o.relocateComponent=function(t,e,r){if(!r.fixedNodeConstraint){var n=Number.POSITIVE_INFINITY;var o=Number.NEGATIVE_INFINITY;var a=Number.POSITIVE_INFINITY;var s=Number.NEGATIVE_INFINITY;if(r.quality=="draft"){var h=true;var l=false;var c=undefined;try{for(var d=e.nodeIndexes[Symbol.iterator](),f;!(h=(f=d.next()).done);h=true){var g=f.value;var u=i(g,2);var p=u[0];var v=u[1];var y=r.cy.getElementById(p);if(y){var m=y.boundingBox();var E=e.xCoords[v]-m.w/2;var N=e.xCoords[v]+m.w/2;var T=e.yCoords[v]-m.h/2;var A=e.yCoords[v]+m.h/2;if(E<n)n=E;if(N>o)o=N;if(T<a)a=T;if(A>s)s=A}}}catch(C){l=true;c=C}finally{try{if(!h&&d.return){d.return()}}finally{if(l){throw c}}}var w=t.x-(o+n)/2;var L=t.y-(s+a)/2;e.xCoords=e.xCoords.map((function(t){return t+w}));e.yCoords=e.yCoords.map((function(t){return t+L}))}else{Object.keys(e).forEach((function(t){var r=e[t];var i=r.getRect().x;var h=r.getRect().x+r.getRect().width;var l=r.getRect().y;var c=r.getRect().y+r.getRect().height;if(i<n)n=i;if(h>o)o=h;if(l<a)a=l;if(c>s)s=c}));var I=t.x-(o+n)/2;var _=t.y-(s+a)/2;Object.keys(e).forEach((function(t){var r=e[t];r.setCenter(r.getCenterX()+I,r.getCenterY()+_)}))}}};o.calcBoundingBox=function(t,e,r,i){var n=Number.MAX_SAFE_INTEGER;var o=Number.MIN_SAFE_INTEGER;var a=Number.MAX_SAFE_INTEGER;var s=Number.MIN_SAFE_INTEGER;var h=void 0;var l=void 0;var c=void 0;var d=void 0;var f=t.descendants().not(":parent");var g=f.length;for(var u=0;u<g;u++){var p=f[u];h=e[i.get(p.id())]-p.width()/2;l=e[i.get(p.id())]+p.width()/2;c=r[i.get(p.id())]-p.height()/2;d=r[i.get(p.id())]+p.height()/2;if(n>h){n=h}if(o<l){o=l}if(a>c){a=c}if(s<d){s=d}}var v={};v.topLeftX=n;v.topLeftY=a;v.width=o-n;v.height=s-a;return v};o.calcParentsWithoutChildren=function(t,e){var r=t.collection();e.nodes(":parent").forEach((function(t){var e=false;t.children().forEach((function(t){if(t.css("display")!="none"){e=true}}));if(!e){r.merge(t)}}));return r};t.exports=o},816:(t,e,r)=>{var i=r(548);var n=r(140).CoSELayout;var o=r(140).CoSENode;var a=r(140).layoutBase.PointD;var s=r(140).layoutBase.DimensionD;var h=r(140).layoutBase.LayoutConstants;var l=r(140).layoutBase.FDLayoutConstants;var c=r(140).CoSEConstants;var d=function t(e,r){var d=e.cy;var f=e.eles;var g=f.nodes();var u=f.edges();var p=void 0;var v=void 0;var y=void 0;var m={};if(e.randomize){p=r["nodeIndexes"];v=r["xCoords"];y=r["yCoords"]}var E=function t(e){return typeof e==="function"};var N=function t(e,r){if(E(e)){return e(r)}else{return e}};var T=i.calcParentsWithoutChildren(d,f);var A=function t(e,r,n,h){var l=r.length;for(var c=0;c<l;c++){var d=r[c];var f=null;if(d.intersection(T).length==0){f=d.children()}var g=void 0;var u=d.layoutDimensions({nodeDimensionsIncludeLabels:h.nodeDimensionsIncludeLabels});if(d.outerWidth()!=null&&d.outerHeight()!=null){if(h.randomize){if(!d.isParent()){g=e.add(new o(n.graphManager,new a(v[p.get(d.id())]-u.w/2,y[p.get(d.id())]-u.h/2),new s(parseFloat(u.w),parseFloat(u.h))))}else{var E=i.calcBoundingBox(d,v,y,p);if(d.intersection(T).length==0){g=e.add(new o(n.graphManager,new a(E.topLeftX,E.topLeftY),new s(E.width,E.height)))}else{g=e.add(new o(n.graphManager,new a(E.topLeftX,E.topLeftY),new s(parseFloat(u.w),parseFloat(u.h))))}}}else{g=e.add(new o(n.graphManager,new a(d.position("x")-u.w/2,d.position("y")-u.h/2),new s(parseFloat(u.w),parseFloat(u.h))))}}else{g=e.add(new o(this.graphManager))}g.id=d.data("id");g.nodeRepulsion=N(h.nodeRepulsion,d);g.paddingLeft=parseInt(d.css("padding"));g.paddingTop=parseInt(d.css("padding"));g.paddingRight=parseInt(d.css("padding"));g.paddingBottom=parseInt(d.css("padding"));if(h.nodeDimensionsIncludeLabels){g.labelWidth=d.boundingBox({includeLabels:true,includeNodes:false,includeOverlays:false}).w;g.labelHeight=d.boundingBox({includeLabels:true,includeNodes:false,includeOverlays:false}).h;g.labelPosVertical=d.css("text-valign");g.labelPosHorizontal=d.css("text-halign")}m[d.data("id")]=g;if(isNaN(g.rect.x)){g.rect.x=0}if(isNaN(g.rect.y)){g.rect.y=0}if(f!=null&&f.length>0){var A=void 0;A=n.getGraphManager().add(n.newGraph(),g);t(A,f,n,h)}}};var w=function t(r,i,n){var o=0;var a=0;for(var s=0;s<n.length;s++){var h=n[s];var d=m[h.data("source")];var f=m[h.data("target")];if(d&&f&&d!==f&&d.getEdgesBetween(f).length==0){var g=i.add(r.newEdge(),d,f);g.id=h.id();g.idealLength=N(e.idealEdgeLength,h);g.edgeElasticity=N(e.edgeElasticity,h);o+=g.idealLength;a++}}if(e.idealEdgeLength!=null){if(a>0)c.DEFAULT_EDGE_LENGTH=l.DEFAULT_EDGE_LENGTH=o/a;else if(!E(e.idealEdgeLength))c.DEFAULT_EDGE_LENGTH=l.DEFAULT_EDGE_LENGTH=e.idealEdgeLength;else c.DEFAULT_EDGE_LENGTH=l.DEFAULT_EDGE_LENGTH=50;c.MIN_REPULSION_DIST=l.MIN_REPULSION_DIST=l.DEFAULT_EDGE_LENGTH/10;c.DEFAULT_RADIAL_SEPARATION=l.DEFAULT_EDGE_LENGTH}};var L=function t(e,r){if(r.fixedNodeConstraint){e.constraints["fixedNodeConstraint"]=r.fixedNodeConstraint}if(r.alignmentConstraint){e.constraints["alignmentConstraint"]=r.alignmentConstraint}if(r.relativePlacementConstraint){e.constraints["relativePlacementConstraint"]=r.relativePlacementConstraint}};if(e.nestingFactor!=null)c.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=l.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=e.nestingFactor;if(e.gravity!=null)c.DEFAULT_GRAVITY_STRENGTH=l.DEFAULT_GRAVITY_STRENGTH=e.gravity;if(e.numIter!=null)c.MAX_ITERATIONS=l.MAX_ITERATIONS=e.numIter;if(e.gravityRange!=null)c.DEFAULT_GRAVITY_RANGE_FACTOR=l.DEFAULT_GRAVITY_RANGE_FACTOR=e.gravityRange;if(e.gravityCompound!=null)c.DEFAULT_COMPOUND_GRAVITY_STRENGTH=l.DEFAULT_COMPOUND_GRAVITY_STRENGTH=e.gravityCompound;if(e.gravityRangeCompound!=null)c.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=l.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=e.gravityRangeCompound;if(e.initialEnergyOnIncremental!=null)c.DEFAULT_COOLING_FACTOR_INCREMENTAL=l.DEFAULT_COOLING_FACTOR_INCREMENTAL=e.initialEnergyOnIncremental;if(e.tilingCompareBy!=null)c.TILING_COMPARE_BY=e.tilingCompareBy;if(e.quality=="proof")h.QUALITY=2;else h.QUALITY=0;c.NODE_DIMENSIONS_INCLUDE_LABELS=l.NODE_DIMENSIONS_INCLUDE_LABELS=h.NODE_DIMENSIONS_INCLUDE_LABELS=e.nodeDimensionsIncludeLabels;c.DEFAULT_INCREMENTAL=l.DEFAULT_INCREMENTAL=h.DEFAULT_INCREMENTAL=!e.randomize;c.ANIMATE=l.ANIMATE=h.ANIMATE=e.animate;c.TILE=e.tile;c.TILING_PADDING_VERTICAL=typeof e.tilingPaddingVertical==="function"?e.tilingPaddingVertical.call():e.tilingPaddingVertical;c.TILING_PADDING_HORIZONTAL=typeof e.tilingPaddingHorizontal==="function"?e.tilingPaddingHorizontal.call():e.tilingPaddingHorizontal;c.DEFAULT_INCREMENTAL=l.DEFAULT_INCREMENTAL=h.DEFAULT_INCREMENTAL=true;c.PURE_INCREMENTAL=!e.randomize;h.DEFAULT_UNIFORM_LEAF_NODE_SIZES=e.uniformNodeDimensions;if(e.step=="transformed"){c.TRANSFORM_ON_CONSTRAINT_HANDLING=true;c.ENFORCE_CONSTRAINTS=false;c.APPLY_LAYOUT=false}if(e.step=="enforced"){c.TRANSFORM_ON_CONSTRAINT_HANDLING=false;c.ENFORCE_CONSTRAINTS=true;c.APPLY_LAYOUT=false}if(e.step=="cose"){c.TRANSFORM_ON_CONSTRAINT_HANDLING=false;c.ENFORCE_CONSTRAINTS=false;c.APPLY_LAYOUT=true}if(e.step=="all"){if(e.randomize)c.TRANSFORM_ON_CONSTRAINT_HANDLING=true;else c.TRANSFORM_ON_CONSTRAINT_HANDLING=false;c.ENFORCE_CONSTRAINTS=true;c.APPLY_LAYOUT=true}if(e.fixedNodeConstraint||e.alignmentConstraint||e.relativePlacementConstraint){c.TREE_REDUCTION_ON_INCREMENTAL=false}else{c.TREE_REDUCTION_ON_INCREMENTAL=true}var t=new n;var I=t.newGraphManager();A(I.addRoot(),i.getTopMostNodes(g),t,e);w(t,I,u);L(t,e);t.runLayout();return m};t.exports={coseLayout:d}},212:(t,e,r)=>{var i=function(){function t(t,e){for(var r=0;r<e.length;r++){var i=e[r];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(t,i.key,i)}}return function(e,r,i){if(r)t(e.prototype,r);if(i)t(e,i);return e}}();function n(t,e){if(!(t instanceof e)){throw new TypeError("Cannot call a class as a function")}}var o=r(658);var a=r(548);var s=r(657),h=s.spectralLayout;var l=r(816),c=l.coseLayout;var d=Object.freeze({quality:"default",randomize:true,animate:true,animationDuration:1e3,animationEasing:undefined,fit:true,padding:30,nodeDimensionsIncludeLabels:false,uniformNodeDimensions:false,packComponents:true,step:"all",samplingType:true,sampleSize:25,nodeSeparation:75,piTol:1e-7,nodeRepulsion:function t(e){return 4500},idealEdgeLength:function t(e){return 50},edgeElasticity:function t(e){return.45},nestingFactor:.1,gravity:.25,numIter:2500,tile:true,tilingCompareBy:undefined,tilingPaddingVertical:10,tilingPaddingHorizontal:10,gravityRangeCompound:1.5,gravityCompound:1,gravityRange:3.8,initialEnergyOnIncremental:.3,fixedNodeConstraint:undefined,alignmentConstraint:undefined,relativePlacementConstraint:undefined,ready:function t(){},stop:function t(){}});var f=function(){function t(e){n(this,t);this.options=o({},d,e)}i(t,[{key:"run",value:function t(){var e=this;var r=this.options;var i=r.cy;var n=r.eles;var o=[];var s=void 0;var l=void 0;var d=[];var f=void 0;var g=[];if(r.fixedNodeConstraint&&(!Array.isArray(r.fixedNodeConstraint)||r.fixedNodeConstraint.length==0)){r.fixedNodeConstraint=undefined}if(r.alignmentConstraint){if(r.alignmentConstraint.vertical&&(!Array.isArray(r.alignmentConstraint.vertical)||r.alignmentConstraint.vertical.length==0)){r.alignmentConstraint.vertical=undefined}if(r.alignmentConstraint.horizontal&&(!Array.isArray(r.alignmentConstraint.horizontal)||r.alignmentConstraint.horizontal.length==0)){r.alignmentConstraint.horizontal=undefined}}if(r.relativePlacementConstraint&&(!Array.isArray(r.relativePlacementConstraint)||r.relativePlacementConstraint.length==0)){r.relativePlacementConstraint=undefined}var u=r.fixedNodeConstraint||r.alignmentConstraint||r.relativePlacementConstraint;if(u){r.tile=false;r.packComponents=false}var p=void 0;var v=false;if(i.layoutUtilities&&r.packComponents){p=i.layoutUtilities("get");if(!p)p=i.layoutUtilities();v=true}if(n.nodes().length>0){if(!v){var y=r.eles.boundingBox();g.push({x:y.x1+y.w/2,y:y.y1+y.h/2});if(r.randomize){var m=h(r);o.push(m)}if(r.quality=="default"||r.quality=="proof"){d.push(c(r,o[0]));a.relocateComponent(g[0],d[0],r)}else{a.relocateComponent(g[0],o[0],r)}}else{var E=a.getTopMostNodes(r.eles.nodes());f=a.connectComponents(i,r.eles,E);f.forEach((function(t){var e=t.boundingBox();g.push({x:e.x1+e.w/2,y:e.y1+e.h/2})}));if(r.randomize){f.forEach((function(t){r.eles=t;o.push(h(r))}))}if(r.quality=="default"||r.quality=="proof"){var N=i.collection();if(r.tile){var T=new Map;var A=[];var w=[];var L=0;var I={nodeIndexes:T,xCoords:A,yCoords:w};var _=[];f.forEach((function(t,e){if(t.edges().length==0){t.nodes().forEach((function(e,r){N.merge(t.nodes()[r]);if(!e.isParent()){I.nodeIndexes.set(t.nodes()[r].id(),L++);I.xCoords.push(t.nodes()[0].position().x);I.yCoords.push(t.nodes()[0].position().y)}}));_.push(e)}}));if(N.length>1){var C=N.boundingBox();g.push({x:C.x1+C.w/2,y:C.y1+C.h/2});f.push(N);o.push(I);for(var M=_.length-1;M>=0;M--){f.splice(_[M],1);o.splice(_[M],1);g.splice(_[M],1)}}}f.forEach((function(t,e){r.eles=t;d.push(c(r,o[e]));a.relocateComponent(g[e],d[e],r)}))}else{f.forEach((function(t,e){a.relocateComponent(g[e],o[e],r)}))}var x=new Set;if(f.length>1){var O=[];var D=n.filter((function(t){return t.css("display")=="none"}));f.forEach((function(t,e){var i=void 0;if(r.quality=="draft"){i=o[e].nodeIndexes}if(t.nodes().not(D).length>0){var n={};n.edges=[];n.nodes=[];var s=void 0;t.nodes().not(D).forEach((function(t){if(r.quality=="draft"){if(!t.isParent()){s=i.get(t.id());n.nodes.push({x:o[e].xCoords[s]-t.boundingbox().w/2,y:o[e].yCoords[s]-t.boundingbox().h/2,width:t.boundingbox().w,height:t.boundingbox().h})}else{var h=a.calcBoundingBox(t,o[e].xCoords,o[e].yCoords,i);n.nodes.push({x:h.topLeftX,y:h.topLeftY,width:h.width,height:h.height})}}else{if(d[e][t.id()]){n.nodes.push({x:d[e][t.id()].getLeft(),y:d[e][t.id()].getTop(),width:d[e][t.id()].getWidth(),height:d[e][t.id()].getHeight()})}}}));t.edges().forEach((function(t){var s=t.source();var h=t.target();if(s.css("display")!="none"&&h.css("display")!="none"){if(r.quality=="draft"){var l=i.get(s.id());var c=i.get(h.id());var f=[];var g=[];if(s.isParent()){var u=a.calcBoundingBox(s,o[e].xCoords,o[e].yCoords,i);f.push(u.topLeftX+u.width/2);f.push(u.topLeftY+u.height/2)}else{f.push(o[e].xCoords[l]);f.push(o[e].yCoords[l])}if(h.isParent()){var p=a.calcBoundingBox(h,o[e].xCoords,o[e].yCoords,i);g.push(p.topLeftX+p.width/2);g.push(p.topLeftY+p.height/2)}else{g.push(o[e].xCoords[c]);g.push(o[e].yCoords[c])}n.edges.push({startX:f[0],startY:f[1],endX:g[0],endY:g[1]})}else{if(d[e][s.id()]&&d[e][h.id()]){n.edges.push({startX:d[e][s.id()].getCenterX(),startY:d[e][s.id()].getCenterY(),endX:d[e][h.id()].getCenterX(),endY:d[e][h.id()].getCenterY()})}}}}));if(n.nodes.length>0){O.push(n);x.add(e)}}}));var R=p.packComponents(O,r.randomize).shifts;if(r.quality=="draft"){o.forEach((function(t,e){var r=t.xCoords.map((function(t){return t+R[e].dx}));var i=t.yCoords.map((function(t){return t+R[e].dy}));t.xCoords=r;t.yCoords=i}))}else{var b=0;x.forEach((function(t){Object.keys(d[t]).forEach((function(e){var r=d[t][e];r.setCenter(r.getCenterX()+R[b].dx,r.getCenterY()+R[b].dy)}));b++}))}}}}var G=function t(e,i){if(r.quality=="default"||r.quality=="proof"){if(typeof e==="number"){e=i}var n=void 0;var a=void 0;var s=e.data("id");d.forEach((function(t){if(s in t){n={x:t[s].getRect().getCenterX(),y:t[s].getRect().getCenterY()};a=t[s]}}));if(r.nodeDimensionsIncludeLabels){if(a.labelWidth){if(a.labelPosHorizontal=="left"){n.x+=a.labelWidth/2}else if(a.labelPosHorizontal=="right"){n.x-=a.labelWidth/2}}if(a.labelHeight){if(a.labelPosVertical=="top"){n.y+=a.labelHeight/2}else if(a.labelPosVertical=="bottom"){n.y-=a.labelHeight/2}}}if(n==undefined)n={x:e.position("x"),y:e.position("y")};return{x:n.x,y:n.y}}else{var h=void 0;o.forEach((function(t){var r=t.nodeIndexes.get(e.id());if(r!=undefined){h={x:t.xCoords[r],y:t.yCoords[r]}}}));if(h==undefined)h={x:e.position("x"),y:e.position("y")};return{x:h.x,y:h.y}}};if(r.quality=="default"||r.quality=="proof"||r.randomize){var F=a.calcParentsWithoutChildren(i,n);var S=n.filter((function(t){return t.css("display")=="none"}));r.eles=n.not(S);n.nodes().not(":parent").not(S).layoutPositions(e,r,G);if(F.length>0){F.forEach((function(t){t.position(G(t))}))}}else{console.log("If randomize option is set to false, then quality option must be 'default' or 'proof'.")}}}]);return t}();t.exports=f},657:(t,e,r)=>{var i=r(548);var n=r(140).layoutBase.Matrix;var o=r(140).layoutBase.SVD;var a=function t(e){var r=e.cy;var a=e.eles;var s=a.nodes();var h=a.nodes(":parent");var l=new Map;var c=new Map;var d=new Map;var f=[];var g=[];var u=[];var p=[];var v=[];var y=[];var m=[];var E=[];var N=void 0;var T=void 0;var A=1e8;var w=1e-9;var L=e.piTol;var I=e.samplingType;var _=e.nodeSeparation;var C=void 0;var M=function t(){var e=0;var r=0;var i=false;while(r<C){e=Math.floor(Math.random()*T);i=false;for(var n=0;n<r;n++){if(p[n]==e){i=true;break}}if(!i){p[r]=e;r++}else{continue}}};var x=function t(e,r,i){var n=[];var o=0;var a=0;var s=0;var h=void 0;var l=[];var d=0;var g=1;for(var u=0;u<T;u++){l[u]=A}n[a]=e;l[e]=0;while(a>=o){s=n[o++];var p=f[s];for(var m=0;m<p.length;m++){h=c.get(p[m]);if(l[h]==A){l[h]=l[s]+1;n[++a]=h}}y[s][r]=l[s]*_}if(i){for(var E=0;E<T;E++){if(y[E][r]<v[E])v[E]=y[E][r]}for(var N=0;N<T;N++){if(v[N]>d){d=v[N];g=N}}}return g};var O=function t(e){var r=void 0;if(!e){M();for(var i=0;i<C;i++){x(p[i],i,e,false)}}else{r=Math.floor(Math.random()*T);N=r;for(var n=0;n<T;n++){v[n]=A}for(var o=0;o<C;o++){p[o]=r;r=x(r,o,e)}}for(var a=0;a<T;a++){for(var s=0;s<C;s++){y[a][s]*=y[a][s]}}for(var h=0;h<C;h++){m[h]=[]}for(var l=0;l<C;l++){for(var c=0;c<C;c++){m[l][c]=y[p[c]][l]}}};var D=function t(){var e=o.svd(m);var r=e.S;var i=e.U;var a=e.V;var s=r[0]*r[0]*r[0];var h=[];for(var l=0;l<C;l++){h[l]=[];for(var c=0;c<C;c++){h[l][c]=0;if(l==c){h[l][c]=r[l]/(r[l]*r[l]+s/(r[l]*r[l]))}}}E=n.multMat(n.multMat(a,h),n.transpose(i))};var R=function t(){var e=void 0;var r=void 0;var i=[];var o=[];var a=[];var s=[];for(var h=0;h<T;h++){i[h]=Math.random();o[h]=Math.random()}i=n.normalize(i);o=n.normalize(o);var l=0;var c=w;var d=w;var f=void 0;while(true){l++;for(var p=0;p<T;p++){a[p]=i[p]}i=n.multGamma(n.multL(n.multGamma(a),y,E));e=n.dotProduct(a,i);i=n.normalize(i);c=n.dotProduct(a,i);f=Math.abs(c/d);if(f<=1+L&&f>=1){break}d=c}for(var v=0;v<T;v++){a[v]=i[v]}l=0;d=w;while(true){l++;for(var m=0;m<T;m++){s[m]=o[m]}s=n.minusOp(s,n.multCons(a,n.dotProduct(a,s)));o=n.multGamma(n.multL(n.multGamma(s),y,E));r=n.dotProduct(s,o);o=n.normalize(o);c=n.dotProduct(s,o);f=Math.abs(c/d);if(f<=1+L&&f>=1){break}d=c}for(var N=0;N<T;N++){s[N]=o[N]}g=n.multCons(a,Math.sqrt(Math.abs(e)));u=n.multCons(s,Math.sqrt(Math.abs(r)))};i.connectComponents(r,a,i.getTopMostNodes(s),l);h.forEach((function(t){i.connectComponents(r,a,i.getTopMostNodes(t.descendants().intersection(a)),l)}));var b=0;for(var G=0;G<s.length;G++){if(!s[G].isParent()){c.set(s[G].id(),b++)}}var F=true;var S=false;var P=undefined;try{for(var U=l.keys()[Symbol.iterator](),k;!(F=(k=U.next()).done);F=true){var Y=k.value;c.set(Y,b++)}}catch(nt){S=true;P=nt}finally{try{if(!F&&U.return){U.return()}}finally{if(S){throw P}}}for(var H=0;H<c.size;H++){f[H]=[]}h.forEach((function(t){var e=t.children().intersection(a);while(e.nodes(":childless").length==0){e=e.nodes()[0].children().intersection(a)}var r=0;var i=e.nodes(":childless")[0].connectedEdges().length;e.nodes(":childless").forEach((function(t,e){if(t.connectedEdges().length<i){i=t.connectedEdges().length;r=e}}));d.set(t.id(),e.nodes(":childless")[r].id())}));s.forEach((function(t){var e=void 0;if(t.isParent())e=c.get(d.get(t.id()));else e=c.get(t.id());t.neighborhood().nodes().forEach((function(r){if(a.intersection(t.edgesWith(r)).length>0){if(r.isParent())f[e].push(d.get(r.id()));else f[e].push(r.id())}}))}));var X=function t(e){var i=c.get(e);var n=void 0;l.get(e).forEach((function(t){if(r.getElementById(t).isParent())n=d.get(t);else n=t;f[i].push(n);f[c.get(n)].push(e)}))};var z=true;var V=false;var B=undefined;try{for(var W=l.keys()[Symbol.iterator](),j;!(z=(j=W.next()).done);z=true){var q=j.value;X(q)}}catch(nt){V=true;B=nt}finally{try{if(!z&&W.return){W.return()}}finally{if(V){throw B}}}T=c.size;var $=void 0;if(T>2){C=T<e.sampleSize?T:e.sampleSize;for(var K=0;K<T;K++){y[K]=[]}for(var Z=0;Z<C;Z++){E[Z]=[]}if(e.quality=="draft"||e.step=="all"){O(I);D();R();$={nodeIndexes:c,xCoords:g,yCoords:u}}else{c.forEach((function(t,e){g.push(r.getElementById(e).position("x"));u.push(r.getElementById(e).position("y"))}));$={nodeIndexes:c,xCoords:g,yCoords:u}}return $}else{var Q=c.keys();var J=r.getElementById(Q.next().value);var tt=J.position();var et=J.outerWidth();g.push(tt.x);u.push(tt.y);if(T==2){var rt=r.getElementById(Q.next().value);var it=rt.outerWidth();g.push(tt.x+et/2+it/2+e.idealEdgeLength);u.push(tt.y)}$={nodeIndexes:c,xCoords:g,yCoords:u};return $}};t.exports={spectralLayout:a}},579:(t,e,r)=>{var i=r(212);var n=function t(e){if(!e){return}e("layout","fcose",i)};if(typeof cytoscape!=="undefined"){n(cytoscape)}t.exports=n},140:e=>{e.exports=t}};var r={};function i(t){var n=r[t];if(n!==undefined){return n.exports}var o=r[t]={exports:{}};e[t](o,o.exports,i);return o.exports}var n=i(579);return n})()}))},41709:function(t,e,r){(function e(i,n){if(true)t.exports=n(r(1917));else{}})(this,(function(t){return(()=>{"use strict";var e={45:(t,e,r)=>{var i={};i.layoutBase=r(551);i.CoSEConstants=r(806);i.CoSEEdge=r(767);i.CoSEGraph=r(880);i.CoSEGraphManager=r(578);i.CoSELayout=r(765);i.CoSENode=r(991);i.ConstraintHandler=r(902);t.exports=i},806:(t,e,r)=>{var i=r(551).FDLayoutConstants;function n(){}for(var o in i){n[o]=i[o]}n.DEFAULT_USE_MULTI_LEVEL_SCALING=false;n.DEFAULT_RADIAL_SEPARATION=i.DEFAULT_EDGE_LENGTH;n.DEFAULT_COMPONENT_SEPERATION=60;n.TILE=true;n.TILING_PADDING_VERTICAL=10;n.TILING_PADDING_HORIZONTAL=10;n.TRANSFORM_ON_CONSTRAINT_HANDLING=true;n.ENFORCE_CONSTRAINTS=true;n.APPLY_LAYOUT=true;n.RELAX_MOVEMENT_ON_CONSTRAINTS=true;n.TREE_REDUCTION_ON_INCREMENTAL=true;n.PURE_INCREMENTAL=n.DEFAULT_INCREMENTAL;t.exports=n},767:(t,e,r)=>{var i=r(551).FDLayoutEdge;function n(t,e,r){i.call(this,t,e,r)}n.prototype=Object.create(i.prototype);for(var o in i){n[o]=i[o]}t.exports=n},880:(t,e,r)=>{var i=r(551).LGraph;function n(t,e,r){i.call(this,t,e,r)}n.prototype=Object.create(i.prototype);for(var o in i){n[o]=i[o]}t.exports=n},578:(t,e,r)=>{var i=r(551).LGraphManager;function n(t){i.call(this,t)}n.prototype=Object.create(i.prototype);for(var o in i){n[o]=i[o]}t.exports=n},765:(t,e,r)=>{var i=r(551).FDLayout;var n=r(578);var o=r(880);var a=r(991);var s=r(767);var h=r(806);var l=r(902);var c=r(551).FDLayoutConstants;var d=r(551).LayoutConstants;var f=r(551).Point;var g=r(551).PointD;var u=r(551).DimensionD;var p=r(551).Layout;var v=r(551).Integer;var y=r(551).IGeometry;var m=r(551).LGraph;var E=r(551).Transform;var N=r(551).LinkedList;function T(){i.call(this);this.toBeTiled={};this.constraints={}}T.prototype=Object.create(i.prototype);for(var A in i){T[A]=i[A]}T.prototype.newGraphManager=function(){var t=new n(this);this.graphManager=t;return t};T.prototype.newGraph=function(t){return new o(null,this.graphManager,t)};T.prototype.newNode=function(t){return new a(this.graphManager,t)};T.prototype.newEdge=function(t){return new s(null,null,t)};T.prototype.initParameters=function(){i.prototype.initParameters.call(this,arguments);if(!this.isSubLayout){if(h.DEFAULT_EDGE_LENGTH<10){this.idealEdgeLength=10}else{this.idealEdgeLength=h.DEFAULT_EDGE_LENGTH}this.useSmartIdealEdgeLengthCalculation=h.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION;this.gravityConstant=c.DEFAULT_GRAVITY_STRENGTH;this.compoundGravityConstant=c.DEFAULT_COMPOUND_GRAVITY_STRENGTH;this.gravityRangeFactor=c.DEFAULT_GRAVITY_RANGE_FACTOR;this.compoundGravityRangeFactor=c.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR;this.prunedNodesAll=[];this.growTreeIterations=0;this.afterGrowthIterations=0;this.isTreeGrowing=false;this.isGrowthFinished=false}};T.prototype.initSpringEmbedder=function(){i.prototype.initSpringEmbedder.call(this);this.coolingCycle=0;this.maxCoolingCycle=this.maxIterations/c.CONVERGENCE_CHECK_PERIOD;this.finalTemperature=.04;this.coolingAdjuster=1};T.prototype.layout=function(){var t=d.DEFAULT_CREATE_BENDS_AS_NEEDED;if(t){this.createBendpoints();this.graphManager.resetAllEdges()}this.level=0;return this.classicLayout()};T.prototype.classicLayout=function(){this.nodesWithGravity=this.calculateNodesToApplyGravitationTo();this.graphManager.setAllNodesToApplyGravitation(this.nodesWithGravity);this.calcNoOfChildrenForAllNodes();this.graphManager.calcLowestCommonAncestors();this.graphManager.calcInclusionTreeDepths();this.graphManager.getRoot().calcEstimatedSize();this.calcIdealEdgeLengths();if(!this.incremental){var t=this.getFlatForest();if(t.length>0){this.positionNodesRadially(t)}else{this.reduceTrees();this.graphManager.resetAllNodesToApplyGravitation();var e=new Set(this.getAllNodes());var r=this.nodesWithGravity.filter((function(t){return e.has(t)}));this.graphManager.setAllNodesToApplyGravitation(r);this.positionNodesRandomly()}}else{if(h.TREE_REDUCTION_ON_INCREMENTAL){this.reduceTrees();this.graphManager.resetAllNodesToApplyGravitation();var e=new Set(this.getAllNodes());var r=this.nodesWithGravity.filter((function(t){return e.has(t)}));this.graphManager.setAllNodesToApplyGravitation(r)}}if(Object.keys(this.constraints).length>0){l.handleConstraints(this);this.initConstraintVariables()}this.initSpringEmbedder();if(h.APPLY_LAYOUT){this.runSpringEmbedder()}return true};T.prototype.tick=function(){this.totalIterations++;if(this.totalIterations===this.maxIterations&&!this.isTreeGrowing&&!this.isGrowthFinished){if(this.prunedNodesAll.length>0){this.isTreeGrowing=true}else{return true}}if(this.totalIterations%c.CONVERGENCE_CHECK_PERIOD==0&&!this.isTreeGrowing&&!this.isGrowthFinished){if(this.isConverged()){if(this.prunedNodesAll.length>0){this.isTreeGrowing=true}else{return true}}this.coolingCycle++;if(this.layoutQuality==0){this.coolingAdjuster=this.coolingCycle}else if(this.layoutQuality==1){this.coolingAdjuster=this.coolingCycle/3}this.coolingFactor=Math.max(this.initialCoolingFactor-Math.pow(this.coolingCycle,Math.log(100*(this.initialCoolingFactor-this.finalTemperature))/Math.log(this.maxCoolingCycle))/100*this.coolingAdjuster,this.finalTemperature);this.animationPeriod=Math.ceil(this.initialAnimationPeriod*Math.sqrt(this.coolingFactor))}if(this.isTreeGrowing){if(this.growTreeIterations%10==0){if(this.prunedNodesAll.length>0){this.graphManager.updateBounds();this.updateGrid();this.growTree(this.prunedNodesAll);this.graphManager.resetAllNodesToApplyGravitation();var t=new Set(this.getAllNodes());var e=this.nodesWithGravity.filter((function(e){return t.has(e)}));this.graphManager.setAllNodesToApplyGravitation(e);this.graphManager.updateBounds();this.updateGrid();if(h.PURE_INCREMENTAL)this.coolingFactor=c.DEFAULT_COOLING_FACTOR_INCREMENTAL/2;else this.coolingFactor=c.DEFAULT_COOLING_FACTOR_INCREMENTAL}else{this.isTreeGrowing=false;this.isGrowthFinished=true}}this.growTreeIterations++}if(this.isGrowthFinished){if(this.isConverged()){return true}if(this.afterGrowthIterations%10==0){this.graphManager.updateBounds();this.updateGrid()}if(h.PURE_INCREMENTAL)this.coolingFactor=c.DEFAULT_COOLING_FACTOR_INCREMENTAL/2*((100-this.afterGrowthIterations)/100);else this.coolingFactor=c.DEFAULT_COOLING_FACTOR_INCREMENTAL*((100-this.afterGrowthIterations)/100);this.afterGrowthIterations++}var r=!this.isTreeGrowing&&!this.isGrowthFinished;var i=this.growTreeIterations%10==1&&this.isTreeGrowing||this.afterGrowthIterations%10==1&&this.isGrowthFinished;this.totalDisplacement=0;this.graphManager.updateBounds();this.calcSpringForces();this.calcRepulsionForces(r,i);this.calcGravitationalForces();this.moveNodes();this.animate();return false};T.prototype.getPositionsData=function(){var t=this.graphManager.getAllNodes();var e={};for(var r=0;r<t.length;r++){var i=t[r].rect;var n=t[r].id;e[n]={id:n,x:i.getCenterX(),y:i.getCenterY(),w:i.width,h:i.height}}return e};T.prototype.runSpringEmbedder=function(){this.initialAnimationPeriod=25;this.animationPeriod=this.initialAnimationPeriod;var t=false;if(c.ANIMATE==="during"){this.emit("layoutstarted")}else{while(!t){t=this.tick()}this.graphManager.updateBounds()}};T.prototype.moveNodes=function(){var t=this.getAllNodes();var e;for(var r=0;r<t.length;r++){e=t[r];e.calculateDisplacement()}if(Object.keys(this.constraints).length>0){this.updateDisplacements()}for(var r=0;r<t.length;r++){e=t[r];e.move()}};T.prototype.initConstraintVariables=function(){var t=this;this.idToNodeMap=new Map;this.fixedNodeSet=new Set;var e=this.graphManager.getAllNodes();for(var r=0;r<e.length;r++){var i=e[r];this.idToNodeMap.set(i.id,i)}var n=function e(r){var i=r.getChild().getNodes();var n;var o=0;for(var a=0;a<i.length;a++){n=i[a];if(n.getChild()==null){if(t.fixedNodeSet.has(n.id)){o+=100}}else{o+=e(n)}}return o};if(this.constraints.fixedNodeConstraint){this.constraints.fixedNodeConstraint.forEach((function(e){t.fixedNodeSet.add(e.nodeId)}));var e=this.graphManager.getAllNodes();var i;for(var r=0;r<e.length;r++){i=e[r];if(i.getChild()!=null){var o=n(i);if(o>0){i.fixedNodeWeight=o}}}}if(this.constraints.relativePlacementConstraint){var a=new Map;var s=new Map;this.dummyToNodeForVerticalAlignment=new Map;this.dummyToNodeForHorizontalAlignment=new Map;this.fixedNodesOnHorizontal=new Set;this.fixedNodesOnVertical=new Set;this.fixedNodeSet.forEach((function(e){t.fixedNodesOnHorizontal.add(e);t.fixedNodesOnVertical.add(e)}));if(this.constraints.alignmentConstraint){if(this.constraints.alignmentConstraint.vertical){var l=this.constraints.alignmentConstraint.vertical;for(var r=0;r<l.length;r++){this.dummyToNodeForVerticalAlignment.set("dummy"+r,[]);l[r].forEach((function(e){a.set(e,"dummy"+r);t.dummyToNodeForVerticalAlignment.get("dummy"+r).push(e);if(t.fixedNodeSet.has(e)){t.fixedNodesOnHorizontal.add("dummy"+r)}}))}}if(this.constraints.alignmentConstraint.horizontal){var c=this.constraints.alignmentConstraint.horizontal;for(var r=0;r<c.length;r++){this.dummyToNodeForHorizontalAlignment.set("dummy"+r,[]);c[r].forEach((function(e){s.set(e,"dummy"+r);t.dummyToNodeForHorizontalAlignment.get("dummy"+r).push(e);if(t.fixedNodeSet.has(e)){t.fixedNodesOnVertical.add("dummy"+r)}}))}}}if(h.RELAX_MOVEMENT_ON_CONSTRAINTS){this.shuffle=function(t){var e,r,i;for(i=t.length-1;i>=2*t.length/3;i--){e=Math.floor(Math.random()*(i+1));r=t[i];t[i]=t[e];t[e]=r}return t};this.nodesInRelativeHorizontal=[];this.nodesInRelativeVertical=[];this.nodeToRelativeConstraintMapHorizontal=new Map;this.nodeToRelativeConstraintMapVertical=new Map;this.nodeToTempPositionMapHorizontal=new Map;this.nodeToTempPositionMapVertical=new Map;this.constraints.relativePlacementConstraint.forEach((function(e){if(e.left){var r=a.has(e.left)?a.get(e.left):e.left;var i=a.has(e.right)?a.get(e.right):e.right;if(!t.nodesInRelativeHorizontal.includes(r)){t.nodesInRelativeHorizontal.push(r);t.nodeToRelativeConstraintMapHorizontal.set(r,[]);if(t.dummyToNodeForVerticalAlignment.has(r)){t.nodeToTempPositionMapHorizontal.set(r,t.idToNodeMap.get(t.dummyToNodeForVerticalAlignment.get(r)[0]).getCenterX())}else{t.nodeToTempPositionMapHorizontal.set(r,t.idToNodeMap.get(r).getCenterX())}}if(!t.nodesInRelativeHorizontal.includes(i)){t.nodesInRelativeHorizontal.push(i);t.nodeToRelativeConstraintMapHorizontal.set(i,[]);if(t.dummyToNodeForVerticalAlignment.has(i)){t.nodeToTempPositionMapHorizontal.set(i,t.idToNodeMap.get(t.dummyToNodeForVerticalAlignment.get(i)[0]).getCenterX())}else{t.nodeToTempPositionMapHorizontal.set(i,t.idToNodeMap.get(i).getCenterX())}}t.nodeToRelativeConstraintMapHorizontal.get(r).push({right:i,gap:e.gap});t.nodeToRelativeConstraintMapHorizontal.get(i).push({left:r,gap:e.gap})}else{var n=s.has(e.top)?s.get(e.top):e.top;var o=s.has(e.bottom)?s.get(e.bottom):e.bottom;if(!t.nodesInRelativeVertical.includes(n)){t.nodesInRelativeVertical.push(n);t.nodeToRelativeConstraintMapVertical.set(n,[]);if(t.dummyToNodeForHorizontalAlignment.has(n)){t.nodeToTempPositionMapVertical.set(n,t.idToNodeMap.get(t.dummyToNodeForHorizontalAlignment.get(n)[0]).getCenterY())}else{t.nodeToTempPositionMapVertical.set(n,t.idToNodeMap.get(n).getCenterY())}}if(!t.nodesInRelativeVertical.includes(o)){t.nodesInRelativeVertical.push(o);t.nodeToRelativeConstraintMapVertical.set(o,[]);if(t.dummyToNodeForHorizontalAlignment.has(o)){t.nodeToTempPositionMapVertical.set(o,t.idToNodeMap.get(t.dummyToNodeForHorizontalAlignment.get(o)[0]).getCenterY())}else{t.nodeToTempPositionMapVertical.set(o,t.idToNodeMap.get(o).getCenterY())}}t.nodeToRelativeConstraintMapVertical.get(n).push({bottom:o,gap:e.gap});t.nodeToRelativeConstraintMapVertical.get(o).push({top:n,gap:e.gap})}}))}else{var d=new Map;var f=new Map;this.constraints.relativePlacementConstraint.forEach((function(t){if(t.left){var e=a.has(t.left)?a.get(t.left):t.left;var r=a.has(t.right)?a.get(t.right):t.right;if(d.has(e)){d.get(e).push(r)}else{d.set(e,[r])}if(d.has(r)){d.get(r).push(e)}else{d.set(r,[e])}}else{var i=s.has(t.top)?s.get(t.top):t.top;var n=s.has(t.bottom)?s.get(t.bottom):t.bottom;if(f.has(i)){f.get(i).push(n)}else{f.set(i,[n])}if(f.has(n)){f.get(n).push(i)}else{f.set(n,[i])}}}));var g=function t(e,r){var i=[];var n=[];var o=new N;var a=new Set;var s=0;e.forEach((function(t,h){if(!a.has(h)){i[s]=[];n[s]=false;var l=h;o.push(l);a.add(l);i[s].push(l);while(o.length!=0){l=o.shift();if(r.has(l)){n[s]=true}var c=e.get(l);c.forEach((function(t){if(!a.has(t)){o.push(t);a.add(t);i[s].push(t)}}))}s++}}));return{components:i,isFixed:n}};var u=g(d,t.fixedNodesOnHorizontal);this.componentsOnHorizontal=u.components;this.fixedComponentsOnHorizontal=u.isFixed;var p=g(f,t.fixedNodesOnVertical);this.componentsOnVertical=p.components;this.fixedComponentsOnVertical=p.isFixed}}};T.prototype.updateDisplacements=function(){var t=this;if(this.constraints.fixedNodeConstraint){this.constraints.fixedNodeConstraint.forEach((function(e){var r=t.idToNodeMap.get(e.nodeId);r.displacementX=0;r.displacementY=0}))}if(this.constraints.alignmentConstraint){if(this.constraints.alignmentConstraint.vertical){var e=this.constraints.alignmentConstraint.vertical;for(var r=0;r<e.length;r++){var i=0;for(var n=0;n<e[r].length;n++){if(this.fixedNodeSet.has(e[r][n])){i=0;break}i+=this.idToNodeMap.get(e[r][n]).displacementX}var o=i/e[r].length;for(var n=0;n<e[r].length;n++){this.idToNodeMap.get(e[r][n]).displacementX=o}}}if(this.constraints.alignmentConstraint.horizontal){var a=this.constraints.alignmentConstraint.horizontal;for(var r=0;r<a.length;r++){var s=0;for(var n=0;n<a[r].length;n++){if(this.fixedNodeSet.has(a[r][n])){s=0;break}s+=this.idToNodeMap.get(a[r][n]).displacementY}var l=s/a[r].length;for(var n=0;n<a[r].length;n++){this.idToNodeMap.get(a[r][n]).displacementY=l}}}}if(this.constraints.relativePlacementConstraint){if(h.RELAX_MOVEMENT_ON_CONSTRAINTS){if(this.totalIterations%10==0){this.shuffle(this.nodesInRelativeHorizontal);this.shuffle(this.nodesInRelativeVertical)}this.nodesInRelativeHorizontal.forEach((function(e){if(!t.fixedNodesOnHorizontal.has(e)){var r=0;if(t.dummyToNodeForVerticalAlignment.has(e)){r=t.idToNodeMap.get(t.dummyToNodeForVerticalAlignment.get(e)[0]).displacementX}else{r=t.idToNodeMap.get(e).displacementX}t.nodeToRelativeConstraintMapHorizontal.get(e).forEach((function(i){if(i.right){var n=t.nodeToTempPositionMapHorizontal.get(i.right)-t.nodeToTempPositionMapHorizontal.get(e)-r;if(n<i.gap){r-=i.gap-n}}else{var n=t.nodeToTempPositionMapHorizontal.get(e)-t.nodeToTempPositionMapHorizontal.get(i.left)+r;if(n<i.gap){r+=i.gap-n}}}));t.nodeToTempPositionMapHorizontal.set(e,t.nodeToTempPositionMapHorizontal.get(e)+r);if(t.dummyToNodeForVerticalAlignment.has(e)){t.dummyToNodeForVerticalAlignment.get(e).forEach((function(e){t.idToNodeMap.get(e).displacementX=r}))}else{t.idToNodeMap.get(e).displacementX=r}}}));this.nodesInRelativeVertical.forEach((function(e){if(!t.fixedNodesOnHorizontal.has(e)){var r=0;if(t.dummyToNodeForHorizontalAlignment.has(e)){r=t.idToNodeMap.get(t.dummyToNodeForHorizontalAlignment.get(e)[0]).displacementY}else{r=t.idToNodeMap.get(e).displacementY}t.nodeToRelativeConstraintMapVertical.get(e).forEach((function(i){if(i.bottom){var n=t.nodeToTempPositionMapVertical.get(i.bottom)-t.nodeToTempPositionMapVertical.get(e)-r;if(n<i.gap){r-=i.gap-n}}else{var n=t.nodeToTempPositionMapVertical.get(e)-t.nodeToTempPositionMapVertical.get(i.top)+r;if(n<i.gap){r+=i.gap-n}}}));t.nodeToTempPositionMapVertical.set(e,t.nodeToTempPositionMapVertical.get(e)+r);if(t.dummyToNodeForHorizontalAlignment.has(e)){t.dummyToNodeForHorizontalAlignment.get(e).forEach((function(e){t.idToNodeMap.get(e).displacementY=r}))}else{t.idToNodeMap.get(e).displacementY=r}}}))}else{for(var r=0;r<this.componentsOnHorizontal.length;r++){var c=this.componentsOnHorizontal[r];if(this.fixedComponentsOnHorizontal[r]){for(var n=0;n<c.length;n++){if(this.dummyToNodeForVerticalAlignment.has(c[n])){this.dummyToNodeForVerticalAlignment.get(c[n]).forEach((function(e){t.idToNodeMap.get(e).displacementX=0}))}else{this.idToNodeMap.get(c[n]).displacementX=0}}}else{var d=0;var f=0;for(var n=0;n<c.length;n++){if(this.dummyToNodeForVerticalAlignment.has(c[n])){var g=this.dummyToNodeForVerticalAlignment.get(c[n]);d+=g.length*this.idToNodeMap.get(g[0]).displacementX;f+=g.length}else{d+=this.idToNodeMap.get(c[n]).displacementX;f++}}var u=d/f;for(var n=0;n<c.length;n++){if(this.dummyToNodeForVerticalAlignment.has(c[n])){this.dummyToNodeForVerticalAlignment.get(c[n]).forEach((function(e){t.idToNodeMap.get(e).displacementX=u}))}else{this.idToNodeMap.get(c[n]).displacementX=u}}}}for(var r=0;r<this.componentsOnVertical.length;r++){var c=this.componentsOnVertical[r];if(this.fixedComponentsOnVertical[r]){for(var n=0;n<c.length;n++){if(this.dummyToNodeForHorizontalAlignment.has(c[n])){this.dummyToNodeForHorizontalAlignment.get(c[n]).forEach((function(e){t.idToNodeMap.get(e).displacementY=0}))}else{this.idToNodeMap.get(c[n]).displacementY=0}}}else{var d=0;var f=0;for(var n=0;n<c.length;n++){if(this.dummyToNodeForHorizontalAlignment.has(c[n])){var g=this.dummyToNodeForHorizontalAlignment.get(c[n]);d+=g.length*this.idToNodeMap.get(g[0]).displacementY;f+=g.length}else{d+=this.idToNodeMap.get(c[n]).displacementY;f++}}var u=d/f;for(var n=0;n<c.length;n++){if(this.dummyToNodeForHorizontalAlignment.has(c[n])){this.dummyToNodeForHorizontalAlignment.get(c[n]).forEach((function(e){t.idToNodeMap.get(e).displacementY=u}))}else{this.idToNodeMap.get(c[n]).displacementY=u}}}}}}};T.prototype.calculateNodesToApplyGravitationTo=function(){var t=[];var e;var r=this.graphManager.getGraphs();var i=r.length;var n;for(n=0;n<i;n++){e=r[n];e.updateConnected();if(!e.isConnected){t=t.concat(e.getNodes())}}return t};T.prototype.createBendpoints=function(){var t=[];t=t.concat(this.graphManager.getAllEdges());var e=new Set;var r;for(r=0;r<t.length;r++){var i=t[r];if(!e.has(i)){var n=i.getSource();var o=i.getTarget();if(n==o){i.getBendpoints().push(new g);i.getBendpoints().push(new g);this.createDummyNodesForBendpoints(i);e.add(i)}else{var a=[];a=a.concat(n.getEdgeListToNode(o));a=a.concat(o.getEdgeListToNode(n));if(!e.has(a[0])){if(a.length>1){var s;for(s=0;s<a.length;s++){var h=a[s];h.getBendpoints().push(new g);this.createDummyNodesForBendpoints(h)}}a.forEach((function(t){e.add(t)}))}}}if(e.size==t.length){break}}};T.prototype.positionNodesRadially=function(t){var e=new f(0,0);var r=Math.ceil(Math.sqrt(t.length));var i=0;var n=0;var o=0;var a=new g(0,0);for(var s=0;s<t.length;s++){if(s%r==0){o=0;n=i;if(s!=0){n+=h.DEFAULT_COMPONENT_SEPERATION}i=0}var l=t[s];var c=p.findCenterOfTree(l);e.x=o;e.y=n;a=T.radialLayout(l,c,e);if(a.y>i){i=Math.floor(a.y)}o=Math.floor(a.x+h.DEFAULT_COMPONENT_SEPERATION)}this.transform(new g(d.WORLD_CENTER_X-a.x/2,d.WORLD_CENTER_Y-a.y/2))};T.radialLayout=function(t,e,r){var i=Math.max(this.maxDiagonalInTree(t),h.DEFAULT_RADIAL_SEPARATION);T.branchRadialLayout(e,null,0,359,0,i);var n=m.calculateBounds(t);var o=new E;o.setDeviceOrgX(n.getMinX());o.setDeviceOrgY(n.getMinY());o.setWorldOrgX(r.x);o.setWorldOrgY(r.y);for(var a=0;a<t.length;a++){var s=t[a];s.transform(o)}var l=new g(n.getMaxX(),n.getMaxY());return o.inverseTransformPoint(l)};T.branchRadialLayout=function(t,e,r,i,n,o){var a=(i-r+1)/2;if(a<0){a+=180}var s=(a+r)%360;var h=s*y.TWO_PI/360;var l=Math.cos(h);var c=n*Math.cos(h);var d=n*Math.sin(h);t.setCenter(c,d);var f=[];f=f.concat(t.getEdges());var g=f.length;if(e!=null){g--}var u=0;var p=f.length;var v;var m=t.getEdgesBetween(e);while(m.length>1){var E=m[0];m.splice(0,1);var N=f.indexOf(E);if(N>=0){f.splice(N,1)}p--;g--}if(e!=null){v=(f.indexOf(m[0])+1)%p}else{v=0}var A=Math.abs(i-r)/g;for(var w=v;u!=g;w=++w%p){var L=f[w].getOtherEnd(t);if(L==e){continue}var I=(r+u*A)%360;var _=(I+A)%360;T.branchRadialLayout(L,t,I,_,n+o,o);u++}};T.maxDiagonalInTree=function(t){var e=v.MIN_VALUE;for(var r=0;r<t.length;r++){var i=t[r];var n=i.getDiagonal();if(n>e){e=n}}return e};T.prototype.calcRepulsionRange=function(){return 2*(this.level+1)*this.idealEdgeLength};T.prototype.groupZeroDegreeMembers=function(){var t=this;var e={};this.memberGroups={};this.idToDummyNode={};var r=[];var i=this.graphManager.getAllNodes();for(var n=0;n<i.length;n++){var o=i[n];var s=o.getParent();if(this.getNodeDegreeWithChildren(o)===0&&(s.id==undefined||!this.getToBeTiled(s))){r.push(o)}}for(var n=0;n<r.length;n++){var o=r[n];var h=o.getParent().id;if(typeof e[h]==="undefined")e[h]=[];e[h]=e[h].concat(o)}Object.keys(e).forEach((function(r){if(e[r].length>1){var i="DummyCompound_"+r;t.memberGroups[i]=e[r];var n=e[r][0].getParent();var o=new a(t.graphManager);o.id=i;o.paddingLeft=n.paddingLeft||0;o.paddingRight=n.paddingRight||0;o.paddingBottom=n.paddingBottom||0;o.paddingTop=n.paddingTop||0;t.idToDummyNode[i]=o;var s=t.getGraphManager().add(t.newGraph(),o);var h=n.getChild();h.add(o);for(var l=0;l<e[r].length;l++){var c=e[r][l];h.remove(c);s.add(c)}}}))};T.prototype.clearCompounds=function(){var t={};var e={};this.performDFSOnCompounds();for(var r=0;r<this.compoundOrder.length;r++){e[this.compoundOrder[r].id]=this.compoundOrder[r];t[this.compoundOrder[r].id]=[].concat(this.compoundOrder[r].getChild().getNodes());this.graphManager.remove(this.compoundOrder[r].getChild());this.compoundOrder[r].child=null}this.graphManager.resetAllNodes();this.tileCompoundMembers(t,e)};T.prototype.clearZeroDegreeMembers=function(){var t=this;var e=this.tiledZeroDegreePack=[];Object.keys(this.memberGroups).forEach((function(r){var i=t.idToDummyNode[r];e[r]=t.tileNodes(t.memberGroups[r],i.paddingLeft+i.paddingRight);i.rect.width=e[r].width;i.rect.height=e[r].height;i.setCenter(e[r].centerX,e[r].centerY);i.labelMarginLeft=0;i.labelMarginTop=0;if(h.NODE_DIMENSIONS_INCLUDE_LABELS){var n=i.rect.width;var o=i.rect.height;if(i.labelWidth){if(i.labelPosHorizontal=="left"){i.rect.x-=i.labelWidth;i.setWidth(n+i.labelWidth);i.labelMarginLeft=i.labelWidth}else if(i.labelPosHorizontal=="center"&&i.labelWidth>n){i.rect.x-=(i.labelWidth-n)/2;i.setWidth(i.labelWidth);i.labelMarginLeft=(i.labelWidth-n)/2}else if(i.labelPosHorizontal=="right"){i.setWidth(n+i.labelWidth)}}if(i.labelHeight){if(i.labelPosVertical=="top"){i.rect.y-=i.labelHeight;i.setHeight(o+i.labelHeight);i.labelMarginTop=i.labelHeight}else if(i.labelPosVertical=="center"&&i.labelHeight>o){i.rect.y-=(i.labelHeight-o)/2;i.setHeight(i.labelHeight);i.labelMarginTop=(i.labelHeight-o)/2}else if(i.labelPosVertical=="bottom"){i.setHeight(o+i.labelHeight)}}}}))};T.prototype.repopulateCompounds=function(){for(var t=this.compoundOrder.length-1;t>=0;t--){var e=this.compoundOrder[t];var r=e.id;var i=e.paddingLeft;var n=e.paddingTop;var o=e.labelMarginLeft;var a=e.labelMarginTop;this.adjustLocations(this.tiledMemberPack[r],e.rect.x,e.rect.y,i,n,o,a)}};T.prototype.repopulateZeroDegreeMembers=function(){var t=this;var e=this.tiledZeroDegreePack;Object.keys(e).forEach((function(r){var i=t.idToDummyNode[r];var n=i.paddingLeft;var o=i.paddingTop;var a=i.labelMarginLeft;var s=i.labelMarginTop;t.adjustLocations(e[r],i.rect.x,i.rect.y,n,o,a,s)}))};T.prototype.getToBeTiled=function(t){var e=t.id;if(this.toBeTiled[e]!=null){return this.toBeTiled[e]}var r=t.getChild();if(r==null){this.toBeTiled[e]=false;return false}var i=r.getNodes();for(var n=0;n<i.length;n++){var o=i[n];if(this.getNodeDegree(o)>0){this.toBeTiled[e]=false;return false}if(o.getChild()==null){this.toBeTiled[o.id]=false;continue}if(!this.getToBeTiled(o)){this.toBeTiled[e]=false;return false}}this.toBeTiled[e]=true;return true};T.prototype.getNodeDegree=function(t){var e=t.id;var r=t.getEdges();var i=0;for(var n=0;n<r.length;n++){var o=r[n];if(o.getSource().id!==o.getTarget().id){i=i+1}}return i};T.prototype.getNodeDegreeWithChildren=function(t){var e=this.getNodeDegree(t);if(t.getChild()==null){return e}var r=t.getChild().getNodes();for(var i=0;i<r.length;i++){var n=r[i];e+=this.getNodeDegreeWithChildren(n)}return e};T.prototype.performDFSOnCompounds=function(){this.compoundOrder=[];this.fillCompexOrderByDFS(this.graphManager.getRoot().getNodes())};T.prototype.fillCompexOrderByDFS=function(t){for(var e=0;e<t.length;e++){var r=t[e];if(r.getChild()!=null){this.fillCompexOrderByDFS(r.getChild().getNodes())}if(this.getToBeTiled(r)){this.compoundOrder.push(r)}}};T.prototype.adjustLocations=function(t,e,r,i,n,o,a){e+=i+o;r+=n+a;var s=e;for(var h=0;h<t.rows.length;h++){var l=t.rows[h];e=s;var c=0;for(var d=0;d<l.length;d++){var f=l[d];f.rect.x=e;f.rect.y=r;e+=f.rect.width+t.horizontalPadding;if(f.rect.height>c)c=f.rect.height}r+=c+t.verticalPadding}};T.prototype.tileCompoundMembers=function(t,e){var r=this;this.tiledMemberPack=[];Object.keys(t).forEach((function(i){var n=e[i];r.tiledMemberPack[i]=r.tileNodes(t[i],n.paddingLeft+n.paddingRight);n.rect.width=r.tiledMemberPack[i].width;n.rect.height=r.tiledMemberPack[i].height;n.setCenter(r.tiledMemberPack[i].centerX,r.tiledMemberPack[i].centerY);n.labelMarginLeft=0;n.labelMarginTop=0;if(h.NODE_DIMENSIONS_INCLUDE_LABELS){var o=n.rect.width;var a=n.rect.height;if(n.labelWidth){if(n.labelPosHorizontal=="left"){n.rect.x-=n.labelWidth;n.setWidth(o+n.labelWidth);n.labelMarginLeft=n.labelWidth}else if(n.labelPosHorizontal=="center"&&n.labelWidth>o){n.rect.x-=(n.labelWidth-o)/2;n.setWidth(n.labelWidth);n.labelMarginLeft=(n.labelWidth-o)/2}else if(n.labelPosHorizontal=="right"){n.setWidth(o+n.labelWidth)}}if(n.labelHeight){if(n.labelPosVertical=="top"){n.rect.y-=n.labelHeight;n.setHeight(a+n.labelHeight);n.labelMarginTop=n.labelHeight}else if(n.labelPosVertical=="center"&&n.labelHeight>a){n.rect.y-=(n.labelHeight-a)/2;n.setHeight(n.labelHeight);n.labelMarginTop=(n.labelHeight-a)/2}else if(n.labelPosVertical=="bottom"){n.setHeight(a+n.labelHeight)}}}}))};T.prototype.tileNodes=function(t,e){var r=this.tileNodesByFavoringDim(t,e,true);var i=this.tileNodesByFavoringDim(t,e,false);var n=this.getOrgRatio(r);var o=this.getOrgRatio(i);var a;if(o<n){a=i}else{a=r}return a};T.prototype.getOrgRatio=function(t){var e=t.width;var r=t.height;var i=e/r;if(i<1){i=1/i}return i};T.prototype.calcIdealRowWidth=function(t,e){var r=h.TILING_PADDING_VERTICAL;var i=h.TILING_PADDING_HORIZONTAL;var n=t.length;var o=0;var a=0;var s=0;t.forEach((function(t){o+=t.getWidth();a+=t.getHeight();if(t.getWidth()>s){s=t.getWidth()}}));var l=o/n;var c=a/n;var d=Math.pow(r-i,2)+4*(l+i)*(c+r)*n;var f=(i-r+Math.sqrt(d))/(2*(l+i));var g;if(e){g=Math.ceil(f);if(g==f){g++}}else{g=Math.floor(f)}var u=g*(l+i)-i;if(s>u){u=s}u+=i*2;return u};T.prototype.tileNodesByFavoringDim=function(t,e,r){var i=h.TILING_PADDING_VERTICAL;var n=h.TILING_PADDING_HORIZONTAL;var o=h.TILING_COMPARE_BY;var a={rows:[],rowWidth:[],rowHeight:[],width:0,height:e,verticalPadding:i,horizontalPadding:n,centerX:0,centerY:0};if(o){a.idealRowWidth=this.calcIdealRowWidth(t,r)}var s=function t(e){return e.rect.width*e.rect.height};var l=function t(e,r){return s(r)-s(e)};t.sort((function(t,e){var r=l;if(a.idealRowWidth){r=o;return r(t.id,e.id)}return r(t,e)}));var c=0;var d=0;for(var f=0;f<t.length;f++){var g=t[f];c+=g.getCenterX();d+=g.getCenterY()}a.centerX=c/t.length;a.centerY=d/t.length;for(var f=0;f<t.length;f++){var g=t[f];if(a.rows.length==0){this.insertNodeToRow(a,g,0,e)}else if(this.canAddHorizontal(a,g.rect.width,g.rect.height)){var u=a.rows.length-1;if(!a.idealRowWidth){u=this.getShortestRowIndex(a)}this.insertNodeToRow(a,g,u,e)}else{this.insertNodeToRow(a,g,a.rows.length,e)}this.shiftToLastRow(a)}return a};T.prototype.insertNodeToRow=function(t,e,r,i){var n=i;if(r==t.rows.length){var o=[];t.rows.push(o);t.rowWidth.push(n);t.rowHeight.push(0)}var a=t.rowWidth[r]+e.rect.width;if(t.rows[r].length>0){a+=t.horizontalPadding}t.rowWidth[r]=a;if(t.width<a){t.width=a}var s=e.rect.height;if(r>0)s+=t.verticalPadding;var h=0;if(s>t.rowHeight[r]){h=t.rowHeight[r];t.rowHeight[r]=s;h=t.rowHeight[r]-h}t.height+=h;t.rows[r].push(e)};T.prototype.getShortestRowIndex=function(t){var e=-1;var r=Number.MAX_VALUE;for(var i=0;i<t.rows.length;i++){if(t.rowWidth[i]<r){e=i;r=t.rowWidth[i]}}return e};T.prototype.getLongestRowIndex=function(t){var e=-1;var r=Number.MIN_VALUE;for(var i=0;i<t.rows.length;i++){if(t.rowWidth[i]>r){e=i;r=t.rowWidth[i]}}return e};T.prototype.canAddHorizontal=function(t,e,r){if(t.idealRowWidth){var i=t.rows.length-1;var n=t.rowWidth[i];return n+e+t.horizontalPadding<=t.idealRowWidth}var o=this.getShortestRowIndex(t);if(o<0){return true}var a=t.rowWidth[o];if(a+t.horizontalPadding+e<=t.width)return true;var s=0;if(t.rowHeight[o]<r){if(o>0)s=r+t.verticalPadding-t.rowHeight[o]}var h;if(t.width-a>=e+t.horizontalPadding){h=(t.height+s)/(a+e+t.horizontalPadding)}else{h=(t.height+s)/t.width}s=r+t.verticalPadding;var l;if(t.width<e){l=(t.height+s)/e}else{l=(t.height+s)/t.width}if(l<1)l=1/l;if(h<1)h=1/h;return h<l};T.prototype.shiftToLastRow=function(t){var e=this.getLongestRowIndex(t);var r=t.rowWidth.length-1;var i=t.rows[e];var n=i[i.length-1];var o=n.width+t.horizontalPadding;if(t.width-t.rowWidth[r]>o&&e!=r){i.splice(-1,1);t.rows[r].push(n);t.rowWidth[e]=t.rowWidth[e]-o;t.rowWidth[r]=t.rowWidth[r]+o;t.width=t.rowWidth[instance.getLongestRowIndex(t)];var a=Number.MIN_VALUE;for(var s=0;s<i.length;s++){if(i[s].height>a)a=i[s].height}if(e>0)a+=t.verticalPadding;var h=t.rowHeight[e]+t.rowHeight[r];t.rowHeight[e]=a;if(t.rowHeight[r]<n.height+t.verticalPadding)t.rowHeight[r]=n.height+t.verticalPadding;var l=t.rowHeight[e]+t.rowHeight[r];t.height+=l-h;this.shiftToLastRow(t)}};T.prototype.tilingPreLayout=function(){if(h.TILE){this.groupZeroDegreeMembers();this.clearCompounds();this.clearZeroDegreeMembers()}};T.prototype.tilingPostLayout=function(){if(h.TILE){this.repopulateZeroDegreeMembers();this.repopulateCompounds()}};T.prototype.reduceTrees=function(){var t=[];var e=true;var r;while(e){var i=this.graphManager.getAllNodes();var n=[];e=false;for(var o=0;o<i.length;o++){r=i[o];if(r.getEdges().length==1&&!r.getEdges()[0].isInterGraph&&r.getChild()==null){if(h.PURE_INCREMENTAL){var a=r.getEdges()[0].getOtherEnd(r);var s=new u(r.getCenterX()-a.getCenterX(),r.getCenterY()-a.getCenterY());n.push([r,r.getEdges()[0],r.getOwner(),s])}else{n.push([r,r.getEdges()[0],r.getOwner()])}e=true}}if(e==true){var l=[];for(var c=0;c<n.length;c++){if(n[c][0].getEdges().length==1){l.push(n[c]);n[c][0].getOwner().remove(n[c][0])}}t.push(l);this.graphManager.resetAllNodes();this.graphManager.resetAllEdges()}}this.prunedNodesAll=t};T.prototype.growTree=function(t){var e=t.length;var r=t[e-1];var i;for(var n=0;n<r.length;n++){i=r[n];this.findPlaceforPrunedNode(i);i[2].add(i[0]);i[2].add(i[1],i[1].source,i[1].target)}t.splice(t.length-1,1);this.graphManager.resetAllNodes();this.graphManager.resetAllEdges()};T.prototype.findPlaceforPrunedNode=function(t){var e;var r;var i=t[0];if(i==t[1].source){r=t[1].target}else{r=t[1].source}if(h.PURE_INCREMENTAL){i.setCenter(r.getCenterX()+t[3].getWidth(),r.getCenterY()+t[3].getHeight())}else{var n=r.startX;var o=r.finishX;var a=r.startY;var s=r.finishY;var l=0;var d=0;var f=0;var g=0;var u=[l,f,d,g];if(a>0){for(var p=n;p<=o;p++){u[0]+=this.grid[p][a-1].length+this.grid[p][a].length-1}}if(o<this.grid.length-1){for(var p=a;p<=s;p++){u[1]+=this.grid[o+1][p].length+this.grid[o][p].length-1}}if(s<this.grid[0].length-1){for(var p=n;p<=o;p++){u[2]+=this.grid[p][s+1].length+this.grid[p][s].length-1}}if(n>0){for(var p=a;p<=s;p++){u[3]+=this.grid[n-1][p].length+this.grid[n][p].length-1}}var y=v.MAX_VALUE;var m;var E;for(var N=0;N<u.length;N++){if(u[N]<y){y=u[N];m=1;E=N}else if(u[N]==y){m++}}if(m==3&&y==0){if(u[0]==0&&u[1]==0&&u[2]==0){e=1}else if(u[0]==0&&u[1]==0&&u[3]==0){e=0}else if(u[0]==0&&u[2]==0&&u[3]==0){e=3}else if(u[1]==0&&u[2]==0&&u[3]==0){e=2}}else if(m==2&&y==0){var T=Math.floor(Math.random()*2);if(u[0]==0&&u[1]==0){if(T==0){e=0}else{e=1}}else if(u[0]==0&&u[2]==0){if(T==0){e=0}else{e=2}}else if(u[0]==0&&u[3]==0){if(T==0){e=0}else{e=3}}else if(u[1]==0&&u[2]==0){if(T==0){e=1}else{e=2}}else if(u[1]==0&&u[3]==0){if(T==0){e=1}else{e=3}}else{if(T==0){e=2}else{e=3}}}else if(m==4&&y==0){var T=Math.floor(Math.random()*4);e=T}else{e=E}if(e==0){i.setCenter(r.getCenterX(),r.getCenterY()-r.getHeight()/2-c.DEFAULT_EDGE_LENGTH-i.getHeight()/2)}else if(e==1){i.setCenter(r.getCenterX()+r.getWidth()/2+c.DEFAULT_EDGE_LENGTH+i.getWidth()/2,r.getCenterY())}else if(e==2){i.setCenter(r.getCenterX(),r.getCenterY()+r.getHeight()/2+c.DEFAULT_EDGE_LENGTH+i.getHeight()/2)}else{i.setCenter(r.getCenterX()-r.getWidth()/2-c.DEFAULT_EDGE_LENGTH-i.getWidth()/2,r.getCenterY())}}};t.exports=T},991:(t,e,r)=>{var i=r(551).FDLayoutNode;var n=r(551).IMath;function o(t,e,r,n){i.call(this,t,e,r,n)}o.prototype=Object.create(i.prototype);for(var a in i){o[a]=i[a]}o.prototype.calculateDisplacement=function(){var t=this.graphManager.getLayout();if(this.getChild()!=null&&this.fixedNodeWeight){this.displacementX+=t.coolingFactor*(this.springForceX+this.repulsionForceX+this.gravitationForceX)/this.fixedNodeWeight;this.displacementY+=t.coolingFactor*(this.springForceY+this.repulsionForceY+this.gravitationForceY)/this.fixedNodeWeight}else{this.displacementX+=t.coolingFactor*(this.springForceX+this.repulsionForceX+this.gravitationForceX)/this.noOfChildren;this.displacementY+=t.coolingFactor*(this.springForceY+this.repulsionForceY+this.gravitationForceY)/this.noOfChildren}if(Math.abs(this.displacementX)>t.coolingFactor*t.maxNodeDisplacement){this.displacementX=t.coolingFactor*t.maxNodeDisplacement*n.sign(this.displacementX)}if(Math.abs(this.displacementY)>t.coolingFactor*t.maxNodeDisplacement){this.displacementY=t.coolingFactor*t.maxNodeDisplacement*n.sign(this.displacementY)}if(this.child&&this.child.getNodes().length>0){this.propogateDisplacementToChildren(this.displacementX,this.displacementY)}};o.prototype.propogateDisplacementToChildren=function(t,e){var r=this.getChild().getNodes();var i;for(var n=0;n<r.length;n++){i=r[n];if(i.getChild()==null){i.displacementX+=t;i.displacementY+=e}else{i.propogateDisplacementToChildren(t,e)}}};o.prototype.move=function(){var t=this.graphManager.getLayout();if(this.child==null||this.child.getNodes().length==0){this.moveBy(this.displacementX,this.displacementY);t.totalDisplacement+=Math.abs(this.displacementX)+Math.abs(this.displacementY)}this.springForceX=0;this.springForceY=0;this.repulsionForceX=0;this.repulsionForceY=0;this.gravitationForceX=0;this.gravitationForceY=0;this.displacementX=0;this.displacementY=0};o.prototype.setPred1=function(t){this.pred1=t};o.prototype.getPred1=function(){return pred1};o.prototype.getPred2=function(){return pred2};o.prototype.setNext=function(t){this.next=t};o.prototype.getNext=function(){return next};o.prototype.setProcessed=function(t){this.processed=t};o.prototype.isProcessed=function(){return processed};t.exports=o},902:(t,e,r)=>{function i(t){if(Array.isArray(t)){for(var e=0,r=Array(t.length);e<t.length;e++){r[e]=t[e]}return r}else{return Array.from(t)}}var n=r(806);var o=r(551).LinkedList;var a=r(551).Matrix;var s=r(551).SVD;function h(){}h.handleConstraints=function(t){var e={};e.fixedNodeConstraint=t.constraints.fixedNodeConstraint;e.alignmentConstraint=t.constraints.alignmentConstraint;e.relativePlacementConstraint=t.constraints.relativePlacementConstraint;var r=new Map;var h=new Map;var l=[];var c=[];var d=t.getAllNodes();var f=0;for(var g=0;g<d.length;g++){var u=d[g];if(u.getChild()==null){h.set(u.id,f++);l.push(u.getCenterX());c.push(u.getCenterY());r.set(u.id,u)}}if(e.relativePlacementConstraint){e.relativePlacementConstraint.forEach((function(t){if(!t.gap&&t.gap!=0){if(t.left){t.gap=n.DEFAULT_EDGE_LENGTH+r.get(t.left).getWidth()/2+r.get(t.right).getWidth()/2}else{t.gap=n.DEFAULT_EDGE_LENGTH+r.get(t.top).getHeight()/2+r.get(t.bottom).getHeight()/2}}}))}var p=function t(e,r){return{x:e.x-r.x,y:e.y-r.y}};var v=function t(e){var r=0;var i=0;e.forEach((function(t){r+=l[h.get(t)];i+=c[h.get(t)]}));return{x:r/e.size,y:i/e.size}};var y=function t(e,r,n,a,s){function d(t,e){var r=new Set(t);var i=true;var n=false;var o=undefined;try{for(var a=e[Symbol.iterator](),s;!(i=(s=a.next()).done);i=true){var h=s.value;r.add(h)}}catch(l){n=true;o=l}finally{try{if(!i&&a.return){a.return()}}finally{if(n){throw o}}}return r}var f=new Map;e.forEach((function(t,e){f.set(e,0)}));e.forEach((function(t,e){t.forEach((function(t){f.set(t.id,f.get(t.id)+1)}))}));var g=new Map;var u=new Map;var p=new o;f.forEach((function(t,e){if(t==0){p.push(e);if(!n){if(r=="horizontal"){g.set(e,h.has(e)?l[h.get(e)]:a.get(e))}else{g.set(e,h.has(e)?c[h.get(e)]:a.get(e))}}}else{g.set(e,Number.NEGATIVE_INFINITY)}if(n){u.set(e,new Set([e]))}}));if(n){s.forEach((function(t){var e=[];t.forEach((function(t){if(n.has(t)){e.push(t)}}));if(e.length>0){var i=0;e.forEach((function(t){if(r=="horizontal"){g.set(t,h.has(t)?l[h.get(t)]:a.get(t));i+=g.get(t)}else{g.set(t,h.has(t)?c[h.get(t)]:a.get(t));i+=g.get(t)}}));i=i/e.length;t.forEach((function(t){if(!n.has(t)){g.set(t,i)}}))}else{var o=0;t.forEach((function(t){if(r=="horizontal"){o+=h.has(t)?l[h.get(t)]:a.get(t)}else{o+=h.has(t)?c[h.get(t)]:a.get(t)}}));o=o/t.length;t.forEach((function(t){g.set(t,o)}))}}))}var v=function t(){var i=p.shift();var o=e.get(i);o.forEach((function(t){if(g.get(t.id)<g.get(i)+t.gap){if(n&&n.has(t.id)){var e=void 0;if(r=="horizontal"){e=h.has(t.id)?l[h.get(t.id)]:a.get(t.id)}else{e=h.has(t.id)?c[h.get(t.id)]:a.get(t.id)}g.set(t.id,e);if(e<g.get(i)+t.gap){var o=g.get(i)+t.gap-e;u.get(i).forEach((function(t){g.set(t,g.get(t)-o)}))}}else{g.set(t.id,g.get(i)+t.gap)}}f.set(t.id,f.get(t.id)-1);if(f.get(t.id)==0){p.push(t.id)}if(n){u.set(t.id,d(u.get(i),u.get(t.id)))}}))};while(p.length!=0){v()}if(n){var y=new Set;e.forEach((function(t,e){if(t.length==0){y.add(e)}}));var m=[];u.forEach((function(t,e){if(y.has(e)){var r=false;var o=true;var a=false;var s=undefined;try{for(var h=t[Symbol.iterator](),l;!(o=(l=h.next()).done);o=true){var c=l.value;if(n.has(c)){r=true}}}catch(g){a=true;s=g}finally{try{if(!o&&h.return){h.return()}}finally{if(a){throw s}}}if(!r){var d=false;var f=void 0;m.forEach((function(e,r){if(e.has([].concat(i(t))[0])){d=true;f=r}}));if(!d){m.push(new Set(t))}else{t.forEach((function(t){m[f].add(t)}))}}}}));m.forEach((function(t,e){var i=Number.POSITIVE_INFINITY;var n=Number.POSITIVE_INFINITY;var o=Number.NEGATIVE_INFINITY;var s=Number.NEGATIVE_INFINITY;var d=true;var f=false;var u=undefined;try{for(var p=t[Symbol.iterator](),v;!(d=(v=p.next()).done);d=true){var y=v.value;var m=void 0;if(r=="horizontal"){m=h.has(y)?l[h.get(y)]:a.get(y)}else{m=h.has(y)?c[h.get(y)]:a.get(y)}var E=g.get(y);if(m<i){i=m}if(m>o){o=m}if(E<n){n=E}if(E>s){s=E}}}catch(C){f=true;u=C}finally{try{if(!d&&p.return){p.return()}}finally{if(f){throw u}}}var N=(i+o)/2-(n+s)/2;var T=true;var A=false;var w=undefined;try{for(var L=t[Symbol.iterator](),I;!(T=(I=L.next()).done);T=true){var _=I.value;g.set(_,g.get(_)+N)}}catch(C){A=true;w=C}finally{try{if(!T&&L.return){L.return()}}finally{if(A){throw w}}}}))}return g};var m=function t(e){var r=0,i=0;var n=0,o=0;e.forEach((function(t){if(t.left){l[h.get(t.left)]-l[h.get(t.right)]>=0?r++:i++}else{c[h.get(t.top)]-c[h.get(t.bottom)]>=0?n++:o++}}));if(r>i&&n>o){for(var a=0;a<h.size;a++){l[a]=-1*l[a];c[a]=-1*c[a]}}else if(r>i){for(var s=0;s<h.size;s++){l[s]=-1*l[s]}}else if(n>o){for(var d=0;d<h.size;d++){c[d]=-1*c[d]}}};var E=function t(e){var r=[];var i=new o;var n=new Set;var a=0;e.forEach((function(t,o){if(!n.has(o)){r[a]=[];var s=o;i.push(s);n.add(s);r[a].push(s);while(i.length!=0){s=i.shift();var h=e.get(s);h.forEach((function(t){if(!n.has(t.id)){i.push(t.id);n.add(t.id);r[a].push(t.id)}}))}a++}}));return r};var N=function t(e){var r=new Map;e.forEach((function(t,e){r.set(e,[])}));e.forEach((function(t,e){t.forEach((function(t){r.get(e).push(t);r.get(t.id).push({id:e,gap:t.gap,direction:t.direction})}))}));return r};var T=function t(e){var r=new Map;e.forEach((function(t,e){r.set(e,[])}));e.forEach((function(t,e){t.forEach((function(t){r.get(t.id).push({id:e,gap:t.gap,direction:t.direction})}))}));return r};var A=[];var w=[];var L=false;var I=false;var _=new Set;var C=new Map;var M=new Map;var x=[];if(e.fixedNodeConstraint){e.fixedNodeConstraint.forEach((function(t){_.add(t.nodeId)}))}if(e.relativePlacementConstraint){e.relativePlacementConstraint.forEach((function(t){if(t.left){if(C.has(t.left)){C.get(t.left).push({id:t.right,gap:t.gap,direction:"horizontal"})}else{C.set(t.left,[{id:t.right,gap:t.gap,direction:"horizontal"}])}if(!C.has(t.right)){C.set(t.right,[])}}else{if(C.has(t.top)){C.get(t.top).push({id:t.bottom,gap:t.gap,direction:"vertical"})}else{C.set(t.top,[{id:t.bottom,gap:t.gap,direction:"vertical"}])}if(!C.has(t.bottom)){C.set(t.bottom,[])}}}));M=N(C);x=E(M)}if(n.TRANSFORM_ON_CONSTRAINT_HANDLING){if(e.fixedNodeConstraint&&e.fixedNodeConstraint.length>1){e.fixedNodeConstraint.forEach((function(t,e){A[e]=[t.position.x,t.position.y];w[e]=[l[h.get(t.nodeId)],c[h.get(t.nodeId)]]}));L=true}else if(e.alignmentConstraint){(function(){var t=0;if(e.alignmentConstraint.vertical){var r=e.alignmentConstraint.vertical;var n=function e(n){var o=new Set;r[n].forEach((function(t){o.add(t)}));var a=new Set([].concat(i(o)).filter((function(t){return _.has(t)})));var s=void 0;if(a.size>0)s=l[h.get(a.values().next().value)];else s=v(o).x;r[n].forEach((function(e){A[t]=[s,c[h.get(e)]];w[t]=[l[h.get(e)],c[h.get(e)]];t++}))};for(var o=0;o<r.length;o++){n(o)}L=true}if(e.alignmentConstraint.horizontal){var a=e.alignmentConstraint.horizontal;var s=function e(r){var n=new Set;a[r].forEach((function(t){n.add(t)}));var o=new Set([].concat(i(n)).filter((function(t){return _.has(t)})));var s=void 0;if(o.size>0)s=l[h.get(o.values().next().value)];else s=v(n).y;a[r].forEach((function(e){A[t]=[l[h.get(e)],s];w[t]=[l[h.get(e)],c[h.get(e)]];t++}))};for(var d=0;d<a.length;d++){s(d)}L=true}if(e.relativePlacementConstraint){I=true}})()}else if(e.relativePlacementConstraint){var O=0;var D=0;for(var R=0;R<x.length;R++){if(x[R].length>O){O=x[R].length;D=R}}if(O<M.size/2){m(e.relativePlacementConstraint);L=false;I=false}else{var b=new Map;var G=new Map;var F=[];x[D].forEach((function(t){C.get(t).forEach((function(e){if(e.direction=="horizontal"){if(b.has(t)){b.get(t).push(e)}else{b.set(t,[e])}if(!b.has(e.id)){b.set(e.id,[])}F.push({left:t,right:e.id})}else{if(G.has(t)){G.get(t).push(e)}else{G.set(t,[e])}if(!G.has(e.id)){G.set(e.id,[])}F.push({top:t,bottom:e.id})}}))}));m(F);I=false;var S=y(b,"horizontal");var P=y(G,"vertical");x[D].forEach((function(t,e){w[e]=[l[h.get(t)],c[h.get(t)]];A[e]=[];if(S.has(t)){A[e][0]=S.get(t)}else{A[e][0]=l[h.get(t)]}if(P.has(t)){A[e][1]=P.get(t)}else{A[e][1]=c[h.get(t)]}}));L=true}}if(L){var U=void 0;var k=a.transpose(A);var Y=a.transpose(w);for(var H=0;H<k.length;H++){k[H]=a.multGamma(k[H]);Y[H]=a.multGamma(Y[H])}var X=a.multMat(k,a.transpose(Y));var z=s.svd(X);U=a.multMat(z.V,a.transpose(z.U));for(var V=0;V<h.size;V++){var B=[l[V],c[V]];var W=[U[0][0],U[1][0]];var j=[U[0][1],U[1][1]];l[V]=a.dotProduct(B,W);c[V]=a.dotProduct(B,j)}if(I){m(e.relativePlacementConstraint)}}}if(n.ENFORCE_CONSTRAINTS){if(e.fixedNodeConstraint&&e.fixedNodeConstraint.length>0){var q={x:0,y:0};e.fixedNodeConstraint.forEach((function(t,e){var r={x:l[h.get(t.nodeId)],y:c[h.get(t.nodeId)]};var i=t.position;var n=p(i,r);q.x+=n.x;q.y+=n.y}));q.x/=e.fixedNodeConstraint.length;q.y/=e.fixedNodeConstraint.length;l.forEach((function(t,e){l[e]+=q.x}));c.forEach((function(t,e){c[e]+=q.y}));e.fixedNodeConstraint.forEach((function(t){l[h.get(t.nodeId)]=t.position.x;c[h.get(t.nodeId)]=t.position.y}))}if(e.alignmentConstraint){if(e.alignmentConstraint.vertical){var $=e.alignmentConstraint.vertical;var K=function t(e){var r=new Set;$[e].forEach((function(t){r.add(t)}));var n=new Set([].concat(i(r)).filter((function(t){return _.has(t)})));var o=void 0;if(n.size>0)o=l[h.get(n.values().next().value)];else o=v(r).x;r.forEach((function(t){if(!_.has(t))l[h.get(t)]=o}))};for(var Z=0;Z<$.length;Z++){K(Z)}}if(e.alignmentConstraint.horizontal){var Q=e.alignmentConstraint.horizontal;var J=function t(e){var r=new Set;Q[e].forEach((function(t){r.add(t)}));var n=new Set([].concat(i(r)).filter((function(t){return _.has(t)})));var o=void 0;if(n.size>0)o=c[h.get(n.values().next().value)];else o=v(r).y;r.forEach((function(t){if(!_.has(t))c[h.get(t)]=o}))};for(var tt=0;tt<Q.length;tt++){J(tt)}}}if(e.relativePlacementConstraint){(function(){var t=new Map;var r=new Map;var i=new Map;var n=new Map;var o=new Map;var a=new Map;var s=new Set;var d=new Set;_.forEach((function(t){s.add(t);d.add(t)}));if(e.alignmentConstraint){if(e.alignmentConstraint.vertical){var f=e.alignmentConstraint.vertical;var g=function e(r){i.set("dummy"+r,[]);f[r].forEach((function(e){t.set(e,"dummy"+r);i.get("dummy"+r).push(e);if(_.has(e)){s.add("dummy"+r)}}));o.set("dummy"+r,l[h.get(f[r][0])])};for(var u=0;u<f.length;u++){g(u)}}if(e.alignmentConstraint.horizontal){var p=e.alignmentConstraint.horizontal;var v=function t(e){n.set("dummy"+e,[]);p[e].forEach((function(t){r.set(t,"dummy"+e);n.get("dummy"+e).push(t);if(_.has(t)){d.add("dummy"+e)}}));a.set("dummy"+e,c[h.get(p[e][0])])};for(var m=0;m<p.length;m++){v(m)}}}var A=new Map;var w=new Map;var L=function e(i){C.get(i).forEach((function(e){var n=void 0;var o=void 0;if(e["direction"]=="horizontal"){n=t.get(i)?t.get(i):i;if(t.get(e.id)){o={id:t.get(e.id),gap:e.gap,direction:e.direction}}else{o=e}if(A.has(n)){A.get(n).push(o)}else{A.set(n,[o])}if(!A.has(o.id)){A.set(o.id,[])}}else{n=r.get(i)?r.get(i):i;if(r.get(e.id)){o={id:r.get(e.id),gap:e.gap,direction:e.direction}}else{o=e}if(w.has(n)){w.get(n).push(o)}else{w.set(n,[o])}if(!w.has(o.id)){w.set(o.id,[])}}}))};var I=true;var M=false;var x=undefined;try{for(var O=C.keys()[Symbol.iterator](),D;!(I=(D=O.next()).done);I=true){var R=D.value;L(R)}}catch(rt){M=true;x=rt}finally{try{if(!I&&O.return){O.return()}}finally{if(M){throw x}}}var b=N(A);var G=N(w);var F=E(b);var S=E(G);var P=T(A);var U=T(w);var k=[];var Y=[];F.forEach((function(t,e){k[e]=[];t.forEach((function(t){if(P.get(t).length==0){k[e].push(t)}}))}));S.forEach((function(t,e){Y[e]=[];t.forEach((function(t){if(U.get(t).length==0){Y[e].push(t)}}))}));var H=y(A,"horizontal",s,o,k);var X=y(w,"vertical",d,a,Y);var z=function t(e){if(i.get(e)){i.get(e).forEach((function(t){l[h.get(t)]=H.get(e)}))}else{l[h.get(e)]=H.get(e)}};var V=true;var B=false;var W=undefined;try{for(var j=H.keys()[Symbol.iterator](),q;!(V=(q=j.next()).done);V=true){var $=q.value;z($)}}catch(rt){B=true;W=rt}finally{try{if(!V&&j.return){j.return()}}finally{if(B){throw W}}}var K=function t(e){if(n.get(e)){n.get(e).forEach((function(t){c[h.get(t)]=X.get(e)}))}else{c[h.get(e)]=X.get(e)}};var Z=true;var Q=false;var J=undefined;try{for(var tt=X.keys()[Symbol.iterator](),et;!(Z=(et=tt.next()).done);Z=true){var $=et.value;K($)}}catch(rt){Q=true;J=rt}finally{try{if(!Z&&tt.return){tt.return()}}finally{if(Q){throw J}}}})()}}for(var et=0;et<d.length;et++){var rt=d[et];if(rt.getChild()==null){rt.setCenter(l[h.get(rt.id)],c[h.get(rt.id)])}}};t.exports=h},551:e=>{e.exports=t}};var r={};function i(t){var n=r[t];if(n!==undefined){return n.exports}var o=r[t]={exports:{}};e[t](o,o.exports,i);return o.exports}var n=i(45);return n})()}))},1917:function(t){(function e(r,i){if(true)t.exports=i();else{}})(this,(function(){return function(t){var e={};function r(i){if(e[i]){return e[i].exports}var n=e[i]={i,l:false,exports:{}};t[i].call(n.exports,n,n.exports,r);n.l=true;return n.exports}r.m=t;r.c=e;r.i=function(t){return t};r.d=function(t,e,i){if(!r.o(t,e)){Object.defineProperty(t,e,{configurable:false,enumerable:true,get:i})}};r.n=function(t){var e=t&&t.__esModule?function e(){return t["default"]}:function e(){return t};r.d(e,"a",e);return e};r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)};r.p="";return r(r.s=28)}([function(t,e,r){"use strict";function i(){}i.QUALITY=1;i.DEFAULT_CREATE_BENDS_AS_NEEDED=false;i.DEFAULT_INCREMENTAL=false;i.DEFAULT_ANIMATION_ON_LAYOUT=true;i.DEFAULT_ANIMATION_DURING_LAYOUT=false;i.DEFAULT_ANIMATION_PERIOD=50;i.DEFAULT_UNIFORM_LEAF_NODE_SIZES=false;i.DEFAULT_GRAPH_MARGIN=15;i.NODE_DIMENSIONS_INCLUDE_LABELS=false;i.SIMPLE_NODE_SIZE=40;i.SIMPLE_NODE_HALF_SIZE=i.SIMPLE_NODE_SIZE/2;i.EMPTY_COMPOUND_NODE_SIZE=40;i.MIN_EDGE_LENGTH=1;i.WORLD_BOUNDARY=1e6;i.INITIAL_WORLD_BOUNDARY=i.WORLD_BOUNDARY/1e3;i.WORLD_CENTER_X=1200;i.WORLD_CENTER_Y=900;t.exports=i},function(t,e,r){"use strict";var i=r(2);var n=r(8);var o=r(9);function a(t,e,r){i.call(this,r);this.isOverlapingSourceAndTarget=false;this.vGraphObject=r;this.bendpoints=[];this.source=t;this.target=e}a.prototype=Object.create(i.prototype);for(var s in i){a[s]=i[s]}a.prototype.getSource=function(){return this.source};a.prototype.getTarget=function(){return this.target};a.prototype.isInterGraph=function(){return this.isInterGraph};a.prototype.getLength=function(){return this.length};a.prototype.isOverlapingSourceAndTarget=function(){return this.isOverlapingSourceAndTarget};a.prototype.getBendpoints=function(){return this.bendpoints};a.prototype.getLca=function(){return this.lca};a.prototype.getSourceInLca=function(){return this.sourceInLca};a.prototype.getTargetInLca=function(){return this.targetInLca};a.prototype.getOtherEnd=function(t){if(this.source===t){return this.target}else if(this.target===t){return this.source}else{throw"Node is not incident with this edge"}};a.prototype.getOtherEndInGraph=function(t,e){var r=this.getOtherEnd(t);var i=e.getGraphManager().getRoot();while(true){if(r.getOwner()==e){return r}if(r.getOwner()==i){break}r=r.getOwner().getParent()}return null};a.prototype.updateLength=function(){var t=new Array(4);this.isOverlapingSourceAndTarget=n.getIntersection(this.target.getRect(),this.source.getRect(),t);if(!this.isOverlapingSourceAndTarget){this.lengthX=t[0]-t[2];this.lengthY=t[1]-t[3];if(Math.abs(this.lengthX)<1){this.lengthX=o.sign(this.lengthX)}if(Math.abs(this.lengthY)<1){this.lengthY=o.sign(this.lengthY)}this.length=Math.sqrt(this.lengthX*this.lengthX+this.lengthY*this.lengthY)}};a.prototype.updateLengthSimple=function(){this.lengthX=this.target.getCenterX()-this.source.getCenterX();this.lengthY=this.target.getCenterY()-this.source.getCenterY();if(Math.abs(this.lengthX)<1){this.lengthX=o.sign(this.lengthX)}if(Math.abs(this.lengthY)<1){this.lengthY=o.sign(this.lengthY)}this.length=Math.sqrt(this.lengthX*this.lengthX+this.lengthY*this.lengthY)};t.exports=a},function(t,e,r){"use strict";function i(t){this.vGraphObject=t}t.exports=i},function(t,e,r){"use strict";var i=r(2);var n=r(10);var o=r(13);var a=r(0);var s=r(16);var h=r(5);function l(t,e,r,a){if(r==null&&a==null){a=e}i.call(this,a);if(t.graphManager!=null)t=t.graphManager;this.estimatedSize=n.MIN_VALUE;this.inclusionTreeDepth=n.MAX_VALUE;this.vGraphObject=a;this.edges=[];this.graphManager=t;if(r!=null&&e!=null)this.rect=new o(e.x,e.y,r.width,r.height);else this.rect=new o}l.prototype=Object.create(i.prototype);for(var c in i){l[c]=i[c]}l.prototype.getEdges=function(){return this.edges};l.prototype.getChild=function(){return this.child};l.prototype.getOwner=function(){return this.owner};l.prototype.getWidth=function(){return this.rect.width};l.prototype.setWidth=function(t){this.rect.width=t};l.prototype.getHeight=function(){return this.rect.height};l.prototype.setHeight=function(t){this.rect.height=t};l.prototype.getCenterX=function(){return this.rect.x+this.rect.width/2};l.prototype.getCenterY=function(){return this.rect.y+this.rect.height/2};l.prototype.getCenter=function(){return new h(this.rect.x+this.rect.width/2,this.rect.y+this.rect.height/2)};l.prototype.getLocation=function(){return new h(this.rect.x,this.rect.y)};l.prototype.getRect=function(){return this.rect};l.prototype.getDiagonal=function(){return Math.sqrt(this.rect.width*this.rect.width+this.rect.height*this.rect.height)};l.prototype.getHalfTheDiagonal=function(){return Math.sqrt(this.rect.height*this.rect.height+this.rect.width*this.rect.width)/2};l.prototype.setRect=function(t,e){this.rect.x=t.x;this.rect.y=t.y;this.rect.width=e.width;this.rect.height=e.height};l.prototype.setCenter=function(t,e){this.rect.x=t-this.rect.width/2;this.rect.y=e-this.rect.height/2};l.prototype.setLocation=function(t,e){this.rect.x=t;this.rect.y=e};l.prototype.moveBy=function(t,e){this.rect.x+=t;this.rect.y+=e};l.prototype.getEdgeListToNode=function(t){var e=[];var r;var i=this;i.edges.forEach((function(r){if(r.target==t){if(r.source!=i)throw"Incorrect edge source!";e.push(r)}}));return e};l.prototype.getEdgesBetween=function(t){var e=[];var r;var i=this;i.edges.forEach((function(r){if(!(r.source==i||r.target==i))throw"Incorrect edge source and/or target";if(r.target==t||r.source==t){e.push(r)}}));return e};l.prototype.getNeighborsList=function(){var t=new Set;var e=this;e.edges.forEach((function(r){if(r.source==e){t.add(r.target)}else{if(r.target!=e){throw"Incorrect incidency!"}t.add(r.source)}}));return t};l.prototype.withChildren=function(){var t=new Set;var e;var r;t.add(this);if(this.child!=null){var i=this.child.getNodes();for(var n=0;n<i.length;n++){e=i[n];r=e.withChildren();r.forEach((function(e){t.add(e)}))}}return t};l.prototype.getNoOfChildren=function(){var t=0;var e;if(this.child==null){t=1}else{var r=this.child.getNodes();for(var i=0;i<r.length;i++){e=r[i];t+=e.getNoOfChildren()}}if(t==0){t=1}return t};l.prototype.getEstimatedSize=function(){if(this.estimatedSize==n.MIN_VALUE){throw"assert failed"}return this.estimatedSize};l.prototype.calcEstimatedSize=function(){if(this.child==null){return this.estimatedSize=(this.rect.width+this.rect.height)/2}else{this.estimatedSize=this.child.calcEstimatedSize();this.rect.width=this.estimatedSize;this.rect.height=this.estimatedSize;return this.estimatedSize}};l.prototype.scatter=function(){var t;var e;var r=-a.INITIAL_WORLD_BOUNDARY;var i=a.INITIAL_WORLD_BOUNDARY;t=a.WORLD_CENTER_X+s.nextDouble()*(i-r)+r;var n=-a.INITIAL_WORLD_BOUNDARY;var o=a.INITIAL_WORLD_BOUNDARY;e=a.WORLD_CENTER_Y+s.nextDouble()*(o-n)+n;this.rect.x=t;this.rect.y=e};l.prototype.updateBounds=function(){if(this.getChild()==null){throw"assert failed"}if(this.getChild().getNodes().length!=0){var t=this.getChild();t.updateBounds(true);this.rect.x=t.getLeft();this.rect.y=t.getTop();this.setWidth(t.getRight()-t.getLeft());this.setHeight(t.getBottom()-t.getTop());if(a.NODE_DIMENSIONS_INCLUDE_LABELS){var e=t.getRight()-t.getLeft();var r=t.getBottom()-t.getTop();if(this.labelWidth){if(this.labelPosHorizontal=="left"){this.rect.x-=this.labelWidth;this.setWidth(e+this.labelWidth)}else if(this.labelPosHorizontal=="center"&&this.labelWidth>e){this.rect.x-=(this.labelWidth-e)/2;this.setWidth(this.labelWidth)}else if(this.labelPosHorizontal=="right"){this.setWidth(e+this.labelWidth)}}if(this.labelHeight){if(this.labelPosVertical=="top"){this.rect.y-=this.labelHeight;this.setHeight(r+this.labelHeight)}else if(this.labelPosVertical=="center"&&this.labelHeight>r){this.rect.y-=(this.labelHeight-r)/2;this.setHeight(this.labelHeight)}else if(this.labelPosVertical=="bottom"){this.setHeight(r+this.labelHeight)}}}}};l.prototype.getInclusionTreeDepth=function(){if(this.inclusionTreeDepth==n.MAX_VALUE){throw"assert failed"}return this.inclusionTreeDepth};l.prototype.transform=function(t){var e=this.rect.x;if(e>a.WORLD_BOUNDARY){e=a.WORLD_BOUNDARY}else if(e<-a.WORLD_BOUNDARY){e=-a.WORLD_BOUNDARY}var r=this.rect.y;if(r>a.WORLD_BOUNDARY){r=a.WORLD_BOUNDARY}else if(r<-a.WORLD_BOUNDARY){r=-a.WORLD_BOUNDARY}var i=new h(e,r);var n=t.inverseTransformPoint(i);this.setLocation(n.x,n.y)};l.prototype.getLeft=function(){return this.rect.x};l.prototype.getRight=function(){return this.rect.x+this.rect.width};l.prototype.getTop=function(){return this.rect.y};l.prototype.getBottom=function(){return this.rect.y+this.rect.height};l.prototype.getParent=function(){if(this.owner==null){return null}return this.owner.getParent()};t.exports=l},function(t,e,r){"use strict";var i=r(0);function n(){}for(var o in i){n[o]=i[o]}n.MAX_ITERATIONS=2500;n.DEFAULT_EDGE_LENGTH=50;n.DEFAULT_SPRING_STRENGTH=.45;n.DEFAULT_REPULSION_STRENGTH=4500;n.DEFAULT_GRAVITY_STRENGTH=.4;n.DEFAULT_COMPOUND_GRAVITY_STRENGTH=1;n.DEFAULT_GRAVITY_RANGE_FACTOR=3.8;n.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=1.5;n.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION=true;n.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION=true;n.DEFAULT_COOLING_FACTOR_INCREMENTAL=.3;n.COOLING_ADAPTATION_FACTOR=.33;n.ADAPTATION_LOWER_NODE_LIMIT=1e3;n.ADAPTATION_UPPER_NODE_LIMIT=5e3;n.MAX_NODE_DISPLACEMENT_INCREMENTAL=100;n.MAX_NODE_DISPLACEMENT=n.MAX_NODE_DISPLACEMENT_INCREMENTAL*3;n.MIN_REPULSION_DIST=n.DEFAULT_EDGE_LENGTH/10;n.CONVERGENCE_CHECK_PERIOD=100;n.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=.1;n.MIN_EDGE_LENGTH=1;n.GRID_CALCULATION_CHECK_PERIOD=10;t.exports=n},function(t,e,r){"use strict";function i(t,e){if(t==null&&e==null){this.x=0;this.y=0}else{this.x=t;this.y=e}}i.prototype.getX=function(){return this.x};i.prototype.getY=function(){return this.y};i.prototype.setX=function(t){this.x=t};i.prototype.setY=function(t){this.y=t};i.prototype.getDifference=function(t){return new DimensionD(this.x-t.x,this.y-t.y)};i.prototype.getCopy=function(){return new i(this.x,this.y)};i.prototype.translate=function(t){this.x+=t.width;this.y+=t.height;return this};t.exports=i},function(t,e,r){"use strict";var i=r(2);var n=r(10);var o=r(0);var a=r(7);var s=r(3);var h=r(1);var l=r(13);var c=r(12);var d=r(11);function f(t,e,r){i.call(this,r);this.estimatedSize=n.MIN_VALUE;this.margin=o.DEFAULT_GRAPH_MARGIN;this.edges=[];this.nodes=[];this.isConnected=false;this.parent=t;if(e!=null&&e instanceof a){this.graphManager=e}else if(e!=null&&e instanceof Layout){this.graphManager=e.graphManager}}f.prototype=Object.create(i.prototype);for(var g in i){f[g]=i[g]}f.prototype.getNodes=function(){return this.nodes};f.prototype.getEdges=function(){return this.edges};f.prototype.getGraphManager=function(){return this.graphManager};f.prototype.getParent=function(){return this.parent};f.prototype.getLeft=function(){return this.left};f.prototype.getRight=function(){return this.right};f.prototype.getTop=function(){return this.top};f.prototype.getBottom=function(){return this.bottom};f.prototype.isConnected=function(){return this.isConnected};f.prototype.add=function(t,e,r){if(e==null&&r==null){var i=t;if(this.graphManager==null){throw"Graph has no graph mgr!"}if(this.getNodes().indexOf(i)>-1){throw"Node already in graph!"}i.owner=this;this.getNodes().push(i);return i}else{var n=t;if(!(this.getNodes().indexOf(e)>-1&&this.getNodes().indexOf(r)>-1)){throw"Source or target not in graph!"}if(!(e.owner==r.owner&&e.owner==this)){throw"Both owners must be this graph!"}if(e.owner!=r.owner){return null}n.source=e;n.target=r;n.isInterGraph=false;this.getEdges().push(n);e.edges.push(n);if(r!=e){r.edges.push(n)}return n}};f.prototype.remove=function(t){var e=t;if(t instanceof s){if(e==null){throw"Node is null!"}if(!(e.owner!=null&&e.owner==this)){throw"Owner graph is invalid!"}if(this.graphManager==null){throw"Owner graph manager is invalid!"}var r=e.edges.slice();var i;var n=r.length;for(var o=0;o<n;o++){i=r[o];if(i.isInterGraph){this.graphManager.remove(i)}else{i.source.owner.remove(i)}}var a=this.nodes.indexOf(e);if(a==-1){throw"Node not in owner node list!"}this.nodes.splice(a,1)}else if(t instanceof h){var i=t;if(i==null){throw"Edge is null!"}if(!(i.source!=null&&i.target!=null)){throw"Source and/or target is null!"}if(!(i.source.owner!=null&&i.target.owner!=null&&i.source.owner==this&&i.target.owner==this)){throw"Source and/or target owner is invalid!"}var l=i.source.edges.indexOf(i);var c=i.target.edges.indexOf(i);if(!(l>-1&&c>-1)){throw"Source and/or target doesn't know this edge!"}i.source.edges.splice(l,1);if(i.target!=i.source){i.target.edges.splice(c,1)}var a=i.source.owner.getEdges().indexOf(i);if(a==-1){throw"Not in owner's edge list!"}i.source.owner.getEdges().splice(a,1)}};f.prototype.updateLeftTop=function(){var t=n.MAX_VALUE;var e=n.MAX_VALUE;var r;var i;var o;var a=this.getNodes();var s=a.length;for(var h=0;h<s;h++){var l=a[h];r=l.getTop();i=l.getLeft();if(t>r){t=r}if(e>i){e=i}}if(t==n.MAX_VALUE){return null}if(a[0].getParent().paddingLeft!=undefined){o=a[0].getParent().paddingLeft}else{o=this.margin}this.left=e-o;this.top=t-o;return new c(this.left,this.top)};f.prototype.updateBounds=function(t){var e=n.MAX_VALUE;var r=-n.MAX_VALUE;var i=n.MAX_VALUE;var o=-n.MAX_VALUE;var a;var s;var h;var c;var d;var f=this.nodes;var g=f.length;for(var u=0;u<g;u++){var p=f[u];if(t&&p.child!=null){p.updateBounds()}a=p.getLeft();s=p.getRight();h=p.getTop();c=p.getBottom();if(e>a){e=a}if(r<s){r=s}if(i>h){i=h}if(o<c){o=c}}var v=new l(e,i,r-e,o-i);if(e==n.MAX_VALUE){this.left=this.parent.getLeft();this.right=this.parent.getRight();this.top=this.parent.getTop();this.bottom=this.parent.getBottom()}if(f[0].getParent().paddingLeft!=undefined){d=f[0].getParent().paddingLeft}else{d=this.margin}this.left=v.x-d;this.right=v.x+v.width+d;this.top=v.y-d;this.bottom=v.y+v.height+d};f.calculateBounds=function(t){var e=n.MAX_VALUE;var r=-n.MAX_VALUE;var i=n.MAX_VALUE;var o=-n.MAX_VALUE;var a;var s;var h;var c;var d=t.length;for(var f=0;f<d;f++){var g=t[f];a=g.getLeft();s=g.getRight();h=g.getTop();c=g.getBottom();if(e>a){e=a}if(r<s){r=s}if(i>h){i=h}if(o<c){o=c}}var u=new l(e,i,r-e,o-i);return u};f.prototype.getInclusionTreeDepth=function(){if(this==this.graphManager.getRoot()){return 1}else{return this.parent.getInclusionTreeDepth()}};f.prototype.getEstimatedSize=function(){if(this.estimatedSize==n.MIN_VALUE){throw"assert failed"}return this.estimatedSize};f.prototype.calcEstimatedSize=function(){var t=0;var e=this.nodes;var r=e.length;for(var i=0;i<r;i++){var n=e[i];t+=n.calcEstimatedSize()}if(t==0){this.estimatedSize=o.EMPTY_COMPOUND_NODE_SIZE}else{this.estimatedSize=t/Math.sqrt(this.nodes.length)}return this.estimatedSize};f.prototype.updateConnected=function(){var t=this;if(this.nodes.length==0){this.isConnected=true;return}var e=new d;var r=new Set;var i=this.nodes[0];var n;var o;var a=i.withChildren();a.forEach((function(t){e.push(t);r.add(t)}));while(e.length!==0){i=e.shift();n=i.getEdges();var s=n.length;for(var h=0;h<s;h++){var l=n[h];o=l.getOtherEndInGraph(i,this);if(o!=null&&!r.has(o)){var c=o.withChildren();c.forEach((function(t){e.push(t);r.add(t)}))}}}this.isConnected=false;if(r.size>=this.nodes.length){var f=0;r.forEach((function(e){if(e.owner==t){f++}}));if(f==this.nodes.length){this.isConnected=true}}};t.exports=f},function(t,e,r){"use strict";var i;var n=r(1);function o(t){i=r(6);this.layout=t;this.graphs=[];this.edges=[]}o.prototype.addRoot=function(){var t=this.layout.newGraph();var e=this.layout.newNode(null);var r=this.add(t,e);this.setRootGraph(r);return this.rootGraph};o.prototype.add=function(t,e,r,i,n){if(r==null&&i==null&&n==null){if(t==null){throw"Graph is null!"}if(e==null){throw"Parent node is null!"}if(this.graphs.indexOf(t)>-1){throw"Graph already in this graph mgr!"}this.graphs.push(t);if(t.parent!=null){throw"Already has a parent!"}if(e.child!=null){throw"Already has a child!"}t.parent=e;e.child=t;return t}else{n=r;i=e;r=t;var o=i.getOwner();var a=n.getOwner();if(!(o!=null&&o.getGraphManager()==this)){throw"Source not in this graph mgr!"}if(!(a!=null&&a.getGraphManager()==this)){throw"Target not in this graph mgr!"}if(o==a){r.isInterGraph=false;return o.add(r,i,n)}else{r.isInterGraph=true;r.source=i;r.target=n;if(this.edges.indexOf(r)>-1){throw"Edge already in inter-graph edge list!"}this.edges.push(r);if(!(r.source!=null&&r.target!=null)){throw"Edge source and/or target is null!"}if(!(r.source.edges.indexOf(r)==-1&&r.target.edges.indexOf(r)==-1)){throw"Edge already in source and/or target incidency list!"}r.source.edges.push(r);r.target.edges.push(r);return r}}};o.prototype.remove=function(t){if(t instanceof i){var e=t;if(e.getGraphManager()!=this){throw"Graph not in this graph mgr"}if(!(e==this.rootGraph||e.parent!=null&&e.parent.graphManager==this)){throw"Invalid parent node!"}var r=[];r=r.concat(e.getEdges());var o;var a=r.length;for(var s=0;s<a;s++){o=r[s];e.remove(o)}var h=[];h=h.concat(e.getNodes());var l;a=h.length;for(var s=0;s<a;s++){l=h[s];e.remove(l)}if(e==this.rootGraph){this.setRootGraph(null)}var c=this.graphs.indexOf(e);this.graphs.splice(c,1);e.parent=null}else if(t instanceof n){o=t;if(o==null){throw"Edge is null!"}if(!o.isInterGraph){throw"Not an inter-graph edge!"}if(!(o.source!=null&&o.target!=null)){throw"Source and/or target is null!"}if(!(o.source.edges.indexOf(o)!=-1&&o.target.edges.indexOf(o)!=-1)){throw"Source and/or target doesn't know this edge!"}var c=o.source.edges.indexOf(o);o.source.edges.splice(c,1);c=o.target.edges.indexOf(o);o.target.edges.splice(c,1);if(!(o.source.owner!=null&&o.source.owner.getGraphManager()!=null)){throw"Edge owner graph or owner graph manager is null!"}if(o.source.owner.getGraphManager().edges.indexOf(o)==-1){throw"Not in owner graph manager's edge list!"}var c=o.source.owner.getGraphManager().edges.indexOf(o);o.source.owner.getGraphManager().edges.splice(c,1)}};o.prototype.updateBounds=function(){this.rootGraph.updateBounds(true)};o.prototype.getGraphs=function(){return this.graphs};o.prototype.getAllNodes=function(){if(this.allNodes==null){var t=[];var e=this.getGraphs();var r=e.length;for(var i=0;i<r;i++){t=t.concat(e[i].getNodes())}this.allNodes=t}return this.allNodes};o.prototype.resetAllNodes=function(){this.allNodes=null};o.prototype.resetAllEdges=function(){this.allEdges=null};o.prototype.resetAllNodesToApplyGravitation=function(){this.allNodesToApplyGravitation=null};o.prototype.getAllEdges=function(){if(this.allEdges==null){var t=[];var e=this.getGraphs();var r=e.length;for(var i=0;i<e.length;i++){t=t.concat(e[i].getEdges())}t=t.concat(this.edges);this.allEdges=t}return this.allEdges};o.prototype.getAllNodesToApplyGravitation=function(){return this.allNodesToApplyGravitation};o.prototype.setAllNodesToApplyGravitation=function(t){if(this.allNodesToApplyGravitation!=null){throw"assert failed"}this.allNodesToApplyGravitation=t};o.prototype.getRoot=function(){return this.rootGraph};o.prototype.setRootGraph=function(t){if(t.getGraphManager()!=this){throw"Root not in this graph mgr!"}this.rootGraph=t;if(t.parent==null){t.parent=this.layout.newNode("Root node")}};o.prototype.getLayout=function(){return this.layout};o.prototype.isOneAncestorOfOther=function(t,e){if(!(t!=null&&e!=null)){throw"assert failed"}if(t==e){return true}var r=t.getOwner();var i;do{i=r.getParent();if(i==null){break}if(i==e){return true}r=i.getOwner();if(r==null){break}}while(true);r=e.getOwner();do{i=r.getParent();if(i==null){break}if(i==t){return true}r=i.getOwner();if(r==null){break}}while(true);return false};o.prototype.calcLowestCommonAncestors=function(){var t;var e;var r;var i;var n;var o=this.getAllEdges();var a=o.length;for(var s=0;s<a;s++){t=o[s];e=t.source;r=t.target;t.lca=null;t.sourceInLca=e;t.targetInLca=r;if(e==r){t.lca=e.getOwner();continue}i=e.getOwner();while(t.lca==null){t.targetInLca=r;n=r.getOwner();while(t.lca==null){if(n==i){t.lca=n;break}if(n==this.rootGraph){break}if(t.lca!=null){throw"assert failed"}t.targetInLca=n.getParent();n=t.targetInLca.getOwner()}if(i==this.rootGraph){break}if(t.lca==null){t.sourceInLca=i.getParent();i=t.sourceInLca.getOwner()}}if(t.lca==null){throw"assert failed"}}};o.prototype.calcLowestCommonAncestor=function(t,e){if(t==e){return t.getOwner()}var r=t.getOwner();do{if(r==null){break}var i=e.getOwner();do{if(i==null){break}if(i==r){return i}i=i.getParent().getOwner()}while(true);r=r.getParent().getOwner()}while(true);return r};o.prototype.calcInclusionTreeDepths=function(t,e){if(t==null&&e==null){t=this.rootGraph;e=1}var r;var i=t.getNodes();var n=i.length;for(var o=0;o<n;o++){r=i[o];r.inclusionTreeDepth=e;if(r.child!=null){this.calcInclusionTreeDepths(r.child,e+1)}}};o.prototype.includesInvalidEdge=function(){var t;var e=[];var r=this.edges.length;for(var i=0;i<r;i++){t=this.edges[i];if(this.isOneAncestorOfOther(t.source,t.target)){e.push(t)}}for(var i=0;i<e.length;i++){this.remove(e[i])}return false};t.exports=o},function(t,e,r){"use strict";var i=r(12);function n(){}n.calcSeparationAmount=function(t,e,r,i){if(!t.intersects(e)){throw"assert failed"}var n=new Array(2);this.decideDirectionsForOverlappingNodes(t,e,n);r[0]=Math.min(t.getRight(),e.getRight())-Math.max(t.x,e.x);r[1]=Math.min(t.getBottom(),e.getBottom())-Math.max(t.y,e.y);if(t.getX()<=e.getX()&&t.getRight()>=e.getRight()){r[0]+=Math.min(e.getX()-t.getX(),t.getRight()-e.getRight())}else if(e.getX()<=t.getX()&&e.getRight()>=t.getRight()){r[0]+=Math.min(t.getX()-e.getX(),e.getRight()-t.getRight())}if(t.getY()<=e.getY()&&t.getBottom()>=e.getBottom()){r[1]+=Math.min(e.getY()-t.getY(),t.getBottom()-e.getBottom())}else if(e.getY()<=t.getY()&&e.getBottom()>=t.getBottom()){r[1]+=Math.min(t.getY()-e.getY(),e.getBottom()-t.getBottom())}var o=Math.abs((e.getCenterY()-t.getCenterY())/(e.getCenterX()-t.getCenterX()));if(e.getCenterY()===t.getCenterY()&&e.getCenterX()===t.getCenterX()){o=1}var a=o*r[0];var s=r[1]/o;if(r[0]<s){s=r[0]}else{a=r[1]}r[0]=-1*n[0]*(s/2+i);r[1]=-1*n[1]*(a/2+i)};n.decideDirectionsForOverlappingNodes=function(t,e,r){if(t.getCenterX()<e.getCenterX()){r[0]=-1}else{r[0]=1}if(t.getCenterY()<e.getCenterY()){r[1]=-1}else{r[1]=1}};n.getIntersection2=function(t,e,r){var i=t.getCenterX();var n=t.getCenterY();var o=e.getCenterX();var a=e.getCenterY();if(t.intersects(e)){r[0]=i;r[1]=n;r[2]=o;r[3]=a;return true}var s=t.getX();var h=t.getY();var l=t.getRight();var c=t.getX();var d=t.getBottom();var f=t.getRight();var g=t.getWidthHalf();var u=t.getHeightHalf();var p=e.getX();var v=e.getY();var y=e.getRight();var m=e.getX();var E=e.getBottom();var N=e.getRight();var T=e.getWidthHalf();var A=e.getHeightHalf();var w=false;var L=false;if(i===o){if(n>a){r[0]=i;r[1]=h;r[2]=o;r[3]=E;return false}else if(n<a){r[0]=i;r[1]=d;r[2]=o;r[3]=v;return false}else{}}else if(n===a){if(i>o){r[0]=s;r[1]=n;r[2]=y;r[3]=a;return false}else if(i<o){r[0]=l;r[1]=n;r[2]=p;r[3]=a;return false}else{}}else{var I=t.height/t.width;var _=e.height/e.width;var C=(a-n)/(o-i);var M=void 0;var x=void 0;var O=void 0;var D=void 0;var R=void 0;var b=void 0;if(-I===C){if(i>o){r[0]=c;r[1]=d;w=true}else{r[0]=l;r[1]=h;w=true}}else if(I===C){if(i>o){r[0]=s;r[1]=h;w=true}else{r[0]=f;r[1]=d;w=true}}if(-_===C){if(o>i){r[2]=m;r[3]=E;L=true}else{r[2]=y;r[3]=v;L=true}}else if(_===C){if(o>i){r[2]=p;r[3]=v;L=true}else{r[2]=N;r[3]=E;L=true}}if(w&&L){return false}if(i>o){if(n>a){M=this.getCardinalDirection(I,C,4);x=this.getCardinalDirection(_,C,2)}else{M=this.getCardinalDirection(-I,C,3);x=this.getCardinalDirection(-_,C,1)}}else{if(n>a){M=this.getCardinalDirection(-I,C,1);x=this.getCardinalDirection(-_,C,3)}else{M=this.getCardinalDirection(I,C,2);x=this.getCardinalDirection(_,C,4)}}if(!w){switch(M){case 1:D=h;O=i+-u/C;r[0]=O;r[1]=D;break;case 2:O=f;D=n+g*C;r[0]=O;r[1]=D;break;case 3:D=d;O=i+u/C;r[0]=O;r[1]=D;break;case 4:O=c;D=n+-g*C;r[0]=O;r[1]=D;break}}if(!L){switch(x){case 1:b=v;R=o+-A/C;r[2]=R;r[3]=b;break;case 2:R=N;b=a+T*C;r[2]=R;r[3]=b;break;case 3:b=E;R=o+A/C;r[2]=R;r[3]=b;break;case 4:R=m;b=a+-T*C;r[2]=R;r[3]=b;break}}}return false};n.getCardinalDirection=function(t,e,r){if(t>e){return r}else{return 1+r%4}};n.getIntersection=function(t,e,r,n){if(n==null){return this.getIntersection2(t,e,r)}var o=t.x;var a=t.y;var s=e.x;var h=e.y;var l=r.x;var c=r.y;var d=n.x;var f=n.y;var g=void 0,u=void 0;var p=void 0,v=void 0,y=void 0,m=void 0,E=void 0,N=void 0;var T=void 0;p=h-a;y=o-s;E=s*a-o*h;v=f-c;m=l-d;N=d*c-l*f;T=p*m-v*y;if(T===0){return null}g=(y*N-m*E)/T;u=(v*E-p*N)/T;return new i(g,u)};n.angleOfVector=function(t,e,r,i){var n=void 0;if(t!==r){n=Math.atan((i-e)/(r-t));if(r<t){n+=Math.PI}else if(i<e){n+=this.TWO_PI}}else if(i<e){n=this.ONE_AND_HALF_PI}else{n=this.HALF_PI}return n};n.doIntersect=function(t,e,r,i){var n=t.x;var o=t.y;var a=e.x;var s=e.y;var h=r.x;var l=r.y;var c=i.x;var d=i.y;var f=(a-n)*(d-l)-(c-h)*(s-o);if(f===0){return false}else{var g=((d-l)*(c-n)+(h-c)*(d-o))/f;var u=((o-s)*(c-n)+(a-n)*(d-o))/f;return 0<g&&g<1&&0<u&&u<1}};n.findCircleLineIntersections=function(t,e,r,i,n,o,a){var s=(r-t)*(r-t)+(i-e)*(i-e);var h=2*((t-n)*(r-t)+(e-o)*(i-e));var l=(t-n)*(t-n)+(e-o)*(e-o)-a*a;var c=h*h-4*s*l;if(c>=0){var d=(-h+Math.sqrt(h*h-4*s*l))/(2*s);var f=(-h-Math.sqrt(h*h-4*s*l))/(2*s);var g=null;if(d>=0&&d<=1){return[d]}if(f>=0&&f<=1){return[f]}return g}else return null};n.HALF_PI=.5*Math.PI;n.ONE_AND_HALF_PI=1.5*Math.PI;n.TWO_PI=2*Math.PI;n.THREE_PI=3*Math.PI;t.exports=n},function(t,e,r){"use strict";function i(){}i.sign=function(t){if(t>0){return 1}else if(t<0){return-1}else{return 0}};i.floor=function(t){return t<0?Math.ceil(t):Math.floor(t)};i.ceil=function(t){return t<0?Math.floor(t):Math.ceil(t)};t.exports=i},function(t,e,r){"use strict";function i(){}i.MAX_VALUE=2147483647;i.MIN_VALUE=-2147483648;t.exports=i},function(t,e,r){"use strict";var i=function(){function t(t,e){for(var r=0;r<e.length;r++){var i=e[r];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(t,i.key,i)}}return function(e,r,i){if(r)t(e.prototype,r);if(i)t(e,i);return e}}();function n(t,e){if(!(t instanceof e)){throw new TypeError("Cannot call a class as a function")}}var o=function t(e){return{value:e,next:null,prev:null}};var a=function t(e,r,i,n){if(e!==null){e.next=r}else{n.head=r}if(i!==null){i.prev=r}else{n.tail=r}r.prev=e;r.next=i;n.length++;return r};var s=function t(e,r){var i=e.prev,n=e.next;if(i!==null){i.next=n}else{r.head=n}if(n!==null){n.prev=i}else{r.tail=i}e.prev=e.next=null;r.length--;return e};var h=function(){function t(e){var r=this;n(this,t);this.length=0;this.head=null;this.tail=null;if(e!=null){e.forEach((function(t){return r.push(t)}))}}i(t,[{key:"size",value:function t(){return this.length}},{key:"insertBefore",value:function t(e,r){return a(r.prev,o(e),r,this)}},{key:"insertAfter",value:function t(e,r){return a(r,o(e),r.next,this)}},{key:"insertNodeBefore",value:function t(e,r){return a(r.prev,e,r,this)}},{key:"insertNodeAfter",value:function t(e,r){return a(r,e,r.next,this)}},{key:"push",value:function t(e){return a(this.tail,o(e),null,this)}},{key:"unshift",value:function t(e){return a(null,o(e),this.head,this)}},{key:"remove",value:function t(e){return s(e,this)}},{key:"pop",value:function t(){return s(this.tail,this).value}},{key:"popNode",value:function t(){return s(this.tail,this)}},{key:"shift",value:function t(){return s(this.head,this).value}},{key:"shiftNode",value:function t(){return s(this.head,this)}},{key:"get_object_at",value:function t(e){if(e<=this.length()){var r=1;var i=this.head;while(r<e){i=i.next;r++}return i.value}}},{key:"set_object_at",value:function t(e,r){if(e<=this.length()){var i=1;var n=this.head;while(i<e){n=n.next;i++}n.value=r}}}]);return t}();t.exports=h},function(t,e,r){"use strict";function i(t,e,r){this.x=null;this.y=null;if(t==null&&e==null&&r==null){this.x=0;this.y=0}else if(typeof t=="number"&&typeof e=="number"&&r==null){this.x=t;this.y=e}else if(t.constructor.name=="Point"&&e==null&&r==null){r=t;this.x=r.x;this.y=r.y}}i.prototype.getX=function(){return this.x};i.prototype.getY=function(){return this.y};i.prototype.getLocation=function(){return new i(this.x,this.y)};i.prototype.setLocation=function(t,e,r){if(t.constructor.name=="Point"&&e==null&&r==null){r=t;this.setLocation(r.x,r.y)}else if(typeof t=="number"&&typeof e=="number"&&r==null){if(parseInt(t)==t&&parseInt(e)==e){this.move(t,e)}else{this.x=Math.floor(t+.5);this.y=Math.floor(e+.5)}}};i.prototype.move=function(t,e){this.x=t;this.y=e};i.prototype.translate=function(t,e){this.x+=t;this.y+=e};i.prototype.equals=function(t){if(t.constructor.name=="Point"){var e=t;return this.x==e.x&&this.y==e.y}return this==t};i.prototype.toString=function(){return(new i).constructor.name+"[x="+this.x+",y="+this.y+"]"};t.exports=i},function(t,e,r){"use strict";function i(t,e,r,i){this.x=0;this.y=0;this.width=0;this.height=0;if(t!=null&&e!=null&&r!=null&&i!=null){this.x=t;this.y=e;this.width=r;this.height=i}}i.prototype.getX=function(){return this.x};i.prototype.setX=function(t){this.x=t};i.prototype.getY=function(){return this.y};i.prototype.setY=function(t){this.y=t};i.prototype.getWidth=function(){return this.width};i.prototype.setWidth=function(t){this.width=t};i.prototype.getHeight=function(){return this.height};i.prototype.setHeight=function(t){this.height=t};i.prototype.getRight=function(){return this.x+this.width};i.prototype.getBottom=function(){return this.y+this.height};i.prototype.intersects=function(t){if(this.getRight()<t.x){return false}if(this.getBottom()<t.y){return false}if(t.getRight()<this.x){return false}if(t.getBottom()<this.y){return false}return true};i.prototype.getCenterX=function(){return this.x+this.width/2};i.prototype.getMinX=function(){return this.getX()};i.prototype.getMaxX=function(){return this.getX()+this.width};i.prototype.getCenterY=function(){return this.y+this.height/2};i.prototype.getMinY=function(){return this.getY()};i.prototype.getMaxY=function(){return this.getY()+this.height};i.prototype.getWidthHalf=function(){return this.width/2};i.prototype.getHeightHalf=function(){return this.height/2};t.exports=i},function(t,e,r){"use strict";var i=typeof Symbol==="function"&&typeof Symbol.iterator==="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol==="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};function n(){}n.lastID=0;n.createID=function(t){if(n.isPrimitive(t)){return t}if(t.uniqueID!=null){return t.uniqueID}t.uniqueID=n.getString();n.lastID++;return t.uniqueID};n.getString=function(t){if(t==null)t=n.lastID;return"Object#"+t+""};n.isPrimitive=function(t){var e=typeof t==="undefined"?"undefined":i(t);return t==null||e!="object"&&e!="function"};t.exports=n},function(t,e,r){"use strict";function i(t){if(Array.isArray(t)){for(var e=0,r=Array(t.length);e<t.length;e++){r[e]=t[e]}return r}else{return Array.from(t)}}var n=r(0);var o=r(7);var a=r(3);var s=r(1);var h=r(6);var l=r(5);var c=r(17);var d=r(29);function f(t){d.call(this);this.layoutQuality=n.QUALITY;this.createBendsAsNeeded=n.DEFAULT_CREATE_BENDS_AS_NEEDED;this.incremental=n.DEFAULT_INCREMENTAL;this.animationOnLayout=n.DEFAULT_ANIMATION_ON_LAYOUT;this.animationDuringLayout=n.DEFAULT_ANIMATION_DURING_LAYOUT;this.animationPeriod=n.DEFAULT_ANIMATION_PERIOD;this.uniformLeafNodeSizes=n.DEFAULT_UNIFORM_LEAF_NODE_SIZES;this.edgeToDummyNodes=new Map;this.graphManager=new o(this);this.isLayoutFinished=false;this.isSubLayout=false;this.isRemoteUse=false;if(t!=null){this.isRemoteUse=t}}f.RANDOM_SEED=1;f.prototype=Object.create(d.prototype);f.prototype.getGraphManager=function(){return this.graphManager};f.prototype.getAllNodes=function(){return this.graphManager.getAllNodes()};f.prototype.getAllEdges=function(){return this.graphManager.getAllEdges()};f.prototype.getAllNodesToApplyGravitation=function(){return this.graphManager.getAllNodesToApplyGravitation()};f.prototype.newGraphManager=function(){var t=new o(this);this.graphManager=t;return t};f.prototype.newGraph=function(t){return new h(null,this.graphManager,t)};f.prototype.newNode=function(t){return new a(this.graphManager,t)};f.prototype.newEdge=function(t){return new s(null,null,t)};f.prototype.checkLayoutSuccess=function(){return this.graphManager.getRoot()==null||this.graphManager.getRoot().getNodes().length==0||this.graphManager.includesInvalidEdge()};f.prototype.runLayout=function(){this.isLayoutFinished=false;if(this.tilingPreLayout){this.tilingPreLayout()}this.initParameters();var t;if(this.checkLayoutSuccess()){t=false}else{t=this.layout()}if(n.ANIMATE==="during"){return false}if(t){if(!this.isSubLayout){this.doPostLayout()}}if(this.tilingPostLayout){this.tilingPostLayout()}this.isLayoutFinished=true;return t};f.prototype.doPostLayout=function(){if(!this.incremental){this.transform()}this.update()};f.prototype.update2=function(){if(this.createBendsAsNeeded){this.createBendpointsFromDummyNodes();this.graphManager.resetAllEdges()}if(!this.isRemoteUse){var t;var e=this.graphManager.getAllEdges();for(var r=0;r<e.length;r++){t=e[r]}var i;var n=this.graphManager.getRoot().getNodes();for(var r=0;r<n.length;r++){i=n[r]}this.update(this.graphManager.getRoot())}};f.prototype.update=function(t){if(t==null){this.update2()}else if(t instanceof a){var e=t;if(e.getChild()!=null){var r=e.getChild().getNodes();for(var i=0;i<r.length;i++){update(r[i])}}if(e.vGraphObject!=null){var n=e.vGraphObject;n.update(e)}}else if(t instanceof s){var o=t;if(o.vGraphObject!=null){var l=o.vGraphObject;l.update(o)}}else if(t instanceof h){var c=t;if(c.vGraphObject!=null){var d=c.vGraphObject;d.update(c)}}};f.prototype.initParameters=function(){if(!this.isSubLayout){this.layoutQuality=n.QUALITY;this.animationDuringLayout=n.DEFAULT_ANIMATION_DURING_LAYOUT;this.animationPeriod=n.DEFAULT_ANIMATION_PERIOD;this.animationOnLayout=n.DEFAULT_ANIMATION_ON_LAYOUT;this.incremental=n.DEFAULT_INCREMENTAL;this.createBendsAsNeeded=n.DEFAULT_CREATE_BENDS_AS_NEEDED;this.uniformLeafNodeSizes=n.DEFAULT_UNIFORM_LEAF_NODE_SIZES}if(this.animationDuringLayout){this.animationOnLayout=false}};f.prototype.transform=function(t){if(t==undefined){this.transform(new l(0,0))}else{var e=new c;var r=this.graphManager.getRoot().updateLeftTop();if(r!=null){e.setWorldOrgX(t.x);e.setWorldOrgY(t.y);e.setDeviceOrgX(r.x);e.setDeviceOrgY(r.y);var i=this.getAllNodes();var n;for(var o=0;o<i.length;o++){n=i[o];n.transform(e)}}}};f.prototype.positionNodesRandomly=function(t){if(t==undefined){this.positionNodesRandomly(this.getGraphManager().getRoot());this.getGraphManager().getRoot().updateBounds(true)}else{var e;var r;var i=t.getNodes();for(var n=0;n<i.length;n++){e=i[n];r=e.getChild();if(r==null){e.scatter()}else if(r.getNodes().length==0){e.scatter()}else{this.positionNodesRandomly(r);e.updateBounds()}}}};f.prototype.getFlatForest=function(){var t=[];var e=true;var r=this.graphManager.getRoot().getNodes();var n=true;for(var o=0;o<r.length;o++){if(r[o].getChild()!=null){n=false}}if(!n){return t}var a=new Set;var s=[];var h=new Map;var l=[];l=l.concat(r);while(l.length>0&&e){s.push(l[0]);while(s.length>0&&e){var c=s[0];s.splice(0,1);a.add(c);var d=c.getEdges();for(var o=0;o<d.length;o++){var f=d[o].getOtherEnd(c);if(h.get(c)!=f){if(!a.has(f)){s.push(f);h.set(f,c)}else{e=false;break}}}}if(!e){t=[]}else{var g=[].concat(i(a));t.push(g);for(var o=0;o<g.length;o++){var u=g[o];var p=l.indexOf(u);if(p>-1){l.splice(p,1)}}a=new Set;h=new Map}}return t};f.prototype.createDummyNodesForBendpoints=function(t){var e=[];var r=t.source;var i=this.graphManager.calcLowestCommonAncestor(t.source,t.target);for(var n=0;n<t.bendpoints.length;n++){var o=this.newNode(null);o.setRect(new Point(0,0),new Dimension(1,1));i.add(o);var a=this.newEdge(null);this.graphManager.add(a,r,o);e.add(o);r=o}var a=this.newEdge(null);this.graphManager.add(a,r,t.target);this.edgeToDummyNodes.set(t,e);if(t.isInterGraph()){this.graphManager.remove(t)}else{i.remove(t)}return e};f.prototype.createBendpointsFromDummyNodes=function(){var t=[];t=t.concat(this.graphManager.getAllEdges());t=[].concat(i(this.edgeToDummyNodes.keys())).concat(t);for(var e=0;e<t.length;e++){var r=t[e];if(r.bendpoints.length>0){var n=this.edgeToDummyNodes.get(r);for(var o=0;o<n.length;o++){var a=n[o];var s=new l(a.getCenterX(),a.getCenterY());var h=r.bendpoints.get(o);h.x=s.x;h.y=s.y;a.getOwner().remove(a)}this.graphManager.add(r,r.source,r.target)}}};f.transform=function(t,e,r,i){if(r!=undefined&&i!=undefined){var n=e;if(t<=50){var o=e/r;n-=(e-o)/50*(50-t)}else{var a=e*i;n+=(a-e)/50*(t-50)}return n}else{var s,h;if(t<=50){s=9*e/500;h=e/10}else{s=9*e/50;h=-8*e}return s*t+h}};f.findCenterOfTree=function(t){var e=[];e=e.concat(t);var r=[];var i=new Map;var n=false;var o=null;if(e.length==1||e.length==2){n=true;o=e[0]}for(var a=0;a<e.length;a++){var s=e[a];var h=s.getNeighborsList().size;i.set(s,s.getNeighborsList().size);if(h==1){r.push(s)}}var l=[];l=l.concat(r);while(!n){var c=[];c=c.concat(l);l=[];for(var a=0;a<e.length;a++){var s=e[a];var d=e.indexOf(s);if(d>=0){e.splice(d,1)}var f=s.getNeighborsList();f.forEach((function(t){if(r.indexOf(t)<0){var e=i.get(t);var n=e-1;if(n==1){l.push(t)}i.set(t,n)}}))}r=r.concat(l);if(e.length==1||e.length==2){n=true;o=e[0]}}return o};f.prototype.setGraphManager=function(t){this.graphManager=t};t.exports=f},function(t,e,r){"use strict";function i(){}i.seed=1;i.x=0;i.nextDouble=function(){i.x=Math.sin(i.seed++)*1e4;return i.x-Math.floor(i.x)};t.exports=i},function(t,e,r){"use strict";var i=r(5);function n(t,e){this.lworldOrgX=0;this.lworldOrgY=0;this.ldeviceOrgX=0;this.ldeviceOrgY=0;this.lworldExtX=1;this.lworldExtY=1;this.ldeviceExtX=1;this.ldeviceExtY=1}n.prototype.getWorldOrgX=function(){return this.lworldOrgX};n.prototype.setWorldOrgX=function(t){this.lworldOrgX=t};n.prototype.getWorldOrgY=function(){return this.lworldOrgY};n.prototype.setWorldOrgY=function(t){this.lworldOrgY=t};n.prototype.getWorldExtX=function(){return this.lworldExtX};n.prototype.setWorldExtX=function(t){this.lworldExtX=t};n.prototype.getWorldExtY=function(){return this.lworldExtY};n.prototype.setWorldExtY=function(t){this.lworldExtY=t};n.prototype.getDeviceOrgX=function(){return this.ldeviceOrgX};n.prototype.setDeviceOrgX=function(t){this.ldeviceOrgX=t};n.prototype.getDeviceOrgY=function(){return this.ldeviceOrgY};n.prototype.setDeviceOrgY=function(t){this.ldeviceOrgY=t};n.prototype.getDeviceExtX=function(){return this.ldeviceExtX};n.prototype.setDeviceExtX=function(t){this.ldeviceExtX=t};n.prototype.getDeviceExtY=function(){return this.ldeviceExtY};n.prototype.setDeviceExtY=function(t){this.ldeviceExtY=t};n.prototype.transformX=function(t){var e=0;var r=this.lworldExtX;if(r!=0){e=this.ldeviceOrgX+(t-this.lworldOrgX)*this.ldeviceExtX/r}return e};n.prototype.transformY=function(t){var e=0;var r=this.lworldExtY;if(r!=0){e=this.ldeviceOrgY+(t-this.lworldOrgY)*this.ldeviceExtY/r}return e};n.prototype.inverseTransformX=function(t){var e=0;var r=this.ldeviceExtX;if(r!=0){e=this.lworldOrgX+(t-this.ldeviceOrgX)*this.lworldExtX/r}return e};n.prototype.inverseTransformY=function(t){var e=0;var r=this.ldeviceExtY;if(r!=0){e=this.lworldOrgY+(t-this.ldeviceOrgY)*this.lworldExtY/r}return e};n.prototype.inverseTransformPoint=function(t){var e=new i(this.inverseTransformX(t.x),this.inverseTransformY(t.y));return e};t.exports=n},function(t,e,r){"use strict";function i(t){if(Array.isArray(t)){for(var e=0,r=Array(t.length);e<t.length;e++){r[e]=t[e]}return r}else{return Array.from(t)}}var n=r(15);var o=r(4);var a=r(0);var s=r(8);var h=r(9);function l(){n.call(this);this.useSmartIdealEdgeLengthCalculation=o.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION;this.gravityConstant=o.DEFAULT_GRAVITY_STRENGTH;this.compoundGravityConstant=o.DEFAULT_COMPOUND_GRAVITY_STRENGTH;this.gravityRangeFactor=o.DEFAULT_GRAVITY_RANGE_FACTOR;this.compoundGravityRangeFactor=o.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR;this.displacementThresholdPerNode=3*o.DEFAULT_EDGE_LENGTH/100;this.coolingFactor=o.DEFAULT_COOLING_FACTOR_INCREMENTAL;this.initialCoolingFactor=o.DEFAULT_COOLING_FACTOR_INCREMENTAL;this.totalDisplacement=0;this.oldTotalDisplacement=0;this.maxIterations=o.MAX_ITERATIONS}l.prototype=Object.create(n.prototype);for(var c in n){l[c]=n[c]}l.prototype.initParameters=function(){n.prototype.initParameters.call(this,arguments);this.totalIterations=0;this.notAnimatedIterations=0;this.useFRGridVariant=o.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION;this.grid=[]};l.prototype.calcIdealEdgeLengths=function(){var t;var e;var r;var i;var n;var s;var h;var l=this.getGraphManager().getAllEdges();for(var c=0;c<l.length;c++){t=l[c];e=t.idealLength;if(t.isInterGraph){i=t.getSource();n=t.getTarget();s=t.getSourceInLca().getEstimatedSize();h=t.getTargetInLca().getEstimatedSize();if(this.useSmartIdealEdgeLengthCalculation){t.idealLength+=s+h-2*a.SIMPLE_NODE_SIZE}r=t.getLca().getInclusionTreeDepth();t.idealLength+=e*o.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR*(i.getInclusionTreeDepth()+n.getInclusionTreeDepth()-2*r)}}};l.prototype.initSpringEmbedder=function(){var t=this.getAllNodes().length;if(this.incremental){if(t>o.ADAPTATION_LOWER_NODE_LIMIT){this.coolingFactor=Math.max(this.coolingFactor*o.COOLING_ADAPTATION_FACTOR,this.coolingFactor-(t-o.ADAPTATION_LOWER_NODE_LIMIT)/(o.ADAPTATION_UPPER_NODE_LIMIT-o.ADAPTATION_LOWER_NODE_LIMIT)*this.coolingFactor*(1-o.COOLING_ADAPTATION_FACTOR))}this.maxNodeDisplacement=o.MAX_NODE_DISPLACEMENT_INCREMENTAL}else{if(t>o.ADAPTATION_LOWER_NODE_LIMIT){this.coolingFactor=Math.max(o.COOLING_ADAPTATION_FACTOR,1-(t-o.ADAPTATION_LOWER_NODE_LIMIT)/(o.ADAPTATION_UPPER_NODE_LIMIT-o.ADAPTATION_LOWER_NODE_LIMIT)*(1-o.COOLING_ADAPTATION_FACTOR))}else{this.coolingFactor=1}this.initialCoolingFactor=this.coolingFactor;this.maxNodeDisplacement=o.MAX_NODE_DISPLACEMENT}this.maxIterations=Math.max(this.getAllNodes().length*5,this.maxIterations);this.displacementThresholdPerNode=3*o.DEFAULT_EDGE_LENGTH/100;this.totalDisplacementThreshold=this.displacementThresholdPerNode*this.getAllNodes().length;this.repulsionRange=this.calcRepulsionRange()};l.prototype.calcSpringForces=function(){var t=this.getAllEdges();var e;for(var r=0;r<t.length;r++){e=t[r];this.calcSpringForce(e,e.idealLength)}};l.prototype.calcRepulsionForces=function(){var t=arguments.length>0&&arguments[0]!==undefined?arguments[0]:true;var e=arguments.length>1&&arguments[1]!==undefined?arguments[1]:false;var r,i;var n,a;var s=this.getAllNodes();var h;if(this.useFRGridVariant){if(this.totalIterations%o.GRID_CALCULATION_CHECK_PERIOD==1&&t){this.updateGrid()}h=new Set;for(r=0;r<s.length;r++){n=s[r];this.calculateRepulsionForceOfANode(n,h,t,e);h.add(n)}}else{for(r=0;r<s.length;r++){n=s[r];for(i=r+1;i<s.length;i++){a=s[i];if(n.getOwner()!=a.getOwner()){continue}this.calcRepulsionForce(n,a)}}}};l.prototype.calcGravitationalForces=function(){var t;var e=this.getAllNodesToApplyGravitation();for(var r=0;r<e.length;r++){t=e[r];this.calcGravitationalForce(t)}};l.prototype.moveNodes=function(){var t=this.getAllNodes();var e;for(var r=0;r<t.length;r++){e=t[r];e.move()}};l.prototype.calcSpringForce=function(t,e){var r=t.getSource();var i=t.getTarget();var n;var o;var a;var s;if(this.uniformLeafNodeSizes&&r.getChild()==null&&i.getChild()==null){t.updateLengthSimple()}else{t.updateLength();if(t.isOverlapingSourceAndTarget){return}}n=t.getLength();if(n==0)return;o=t.edgeElasticity*(n-e);a=o*(t.lengthX/n);s=o*(t.lengthY/n);r.springForceX+=a;r.springForceY+=s;i.springForceX-=a;i.springForceY-=s};l.prototype.calcRepulsionForce=function(t,e){var r=t.getRect();var i=e.getRect();var n=new Array(2);var a=new Array(4);var l;var c;var d;var f;var g;var u;var p;if(r.intersects(i)){s.calcSeparationAmount(r,i,n,o.DEFAULT_EDGE_LENGTH/2);u=2*n[0];p=2*n[1];var v=t.noOfChildren*e.noOfChildren/(t.noOfChildren+e.noOfChildren);t.repulsionForceX-=v*u;t.repulsionForceY-=v*p;e.repulsionForceX+=v*u;e.repulsionForceY+=v*p}else{if(this.uniformLeafNodeSizes&&t.getChild()==null&&e.getChild()==null){l=i.getCenterX()-r.getCenterX();c=i.getCenterY()-r.getCenterY()}else{s.getIntersection(r,i,a);l=a[2]-a[0];c=a[3]-a[1]}if(Math.abs(l)<o.MIN_REPULSION_DIST){l=h.sign(l)*o.MIN_REPULSION_DIST}if(Math.abs(c)<o.MIN_REPULSION_DIST){c=h.sign(c)*o.MIN_REPULSION_DIST}d=l*l+c*c;f=Math.sqrt(d);g=(t.nodeRepulsion/2+e.nodeRepulsion/2)*t.noOfChildren*e.noOfChildren/d;u=g*l/f;p=g*c/f;t.repulsionForceX-=u;t.repulsionForceY-=p;e.repulsionForceX+=u;e.repulsionForceY+=p}};l.prototype.calcGravitationalForce=function(t){var e;var r;var i;var n;var o;var a;var s;var h;e=t.getOwner();r=(e.getRight()+e.getLeft())/2;i=(e.getTop()+e.getBottom())/2;n=t.getCenterX()-r;o=t.getCenterY()-i;a=Math.abs(n)+t.getWidth()/2;s=Math.abs(o)+t.getHeight()/2;if(t.getOwner()==this.graphManager.getRoot()){h=e.getEstimatedSize()*this.gravityRangeFactor;if(a>h||s>h){t.gravitationForceX=-this.gravityConstant*n;t.gravitationForceY=-this.gravityConstant*o}}else{h=e.getEstimatedSize()*this.compoundGravityRangeFactor;if(a>h||s>h){t.gravitationForceX=-this.gravityConstant*n*this.compoundGravityConstant;t.gravitationForceY=-this.gravityConstant*o*this.compoundGravityConstant}}};l.prototype.isConverged=function(){var t;var e=false;if(this.totalIterations>this.maxIterations/3){e=Math.abs(this.totalDisplacement-this.oldTotalDisplacement)<2}t=this.totalDisplacement<this.totalDisplacementThreshold;this.oldTotalDisplacement=this.totalDisplacement;return t||e};l.prototype.animate=function(){if(this.animationDuringLayout&&!this.isSubLayout){if(this.notAnimatedIterations==this.animationPeriod){this.update();this.notAnimatedIterations=0}else{this.notAnimatedIterations++}}};l.prototype.calcNoOfChildrenForAllNodes=function(){var t;var e=this.graphManager.getAllNodes();for(var r=0;r<e.length;r++){t=e[r];t.noOfChildren=t.getNoOfChildren()}};l.prototype.calcGrid=function(t){var e=0;var r=0;e=parseInt(Math.ceil((t.getRight()-t.getLeft())/this.repulsionRange));r=parseInt(Math.ceil((t.getBottom()-t.getTop())/this.repulsionRange));var i=new Array(e);for(var n=0;n<e;n++){i[n]=new Array(r)}for(var n=0;n<e;n++){for(var o=0;o<r;o++){i[n][o]=new Array}}return i};l.prototype.addNodeToGrid=function(t,e,r){var i=0;var n=0;var o=0;var a=0;i=parseInt(Math.floor((t.getRect().x-e)/this.repulsionRange));n=parseInt(Math.floor((t.getRect().width+t.getRect().x-e)/this.repulsionRange));o=parseInt(Math.floor((t.getRect().y-r)/this.repulsionRange));a=parseInt(Math.floor((t.getRect().height+t.getRect().y-r)/this.repulsionRange));for(var s=i;s<=n;s++){for(var h=o;h<=a;h++){this.grid[s][h].push(t);t.setGridCoordinates(i,n,o,a)}}};l.prototype.updateGrid=function(){var t;var e;var r=this.getAllNodes();this.grid=this.calcGrid(this.graphManager.getRoot());for(t=0;t<r.length;t++){e=r[t];this.addNodeToGrid(e,this.graphManager.getRoot().getLeft(),this.graphManager.getRoot().getTop())}};l.prototype.calculateRepulsionForceOfANode=function(t,e,r,n){if(this.totalIterations%o.GRID_CALCULATION_CHECK_PERIOD==1&&r||n){var a=new Set;t.surrounding=new Array;var s;var h=this.grid;for(var l=t.startX-1;l<t.finishX+2;l++){for(var c=t.startY-1;c<t.finishY+2;c++){if(!(l<0||c<0||l>=h.length||c>=h[0].length)){for(var d=0;d<h[l][c].length;d++){s=h[l][c][d];if(t.getOwner()!=s.getOwner()||t==s){continue}if(!e.has(s)&&!a.has(s)){var f=Math.abs(t.getCenterX()-s.getCenterX())-(t.getWidth()/2+s.getWidth()/2);var g=Math.abs(t.getCenterY()-s.getCenterY())-(t.getHeight()/2+s.getHeight()/2);if(f<=this.repulsionRange&&g<=this.repulsionRange){a.add(s)}}}}}}t.surrounding=[].concat(i(a))}for(l=0;l<t.surrounding.length;l++){this.calcRepulsionForce(t,t.surrounding[l])}};l.prototype.calcRepulsionRange=function(){return 0};t.exports=l},function(t,e,r){"use strict";var i=r(1);var n=r(4);function o(t,e,r){i.call(this,t,e,r);this.idealLength=n.DEFAULT_EDGE_LENGTH;this.edgeElasticity=n.DEFAULT_SPRING_STRENGTH}o.prototype=Object.create(i.prototype);for(var a in i){o[a]=i[a]}t.exports=o},function(t,e,r){"use strict";var i=r(3);var n=r(4);function o(t,e,r,o){i.call(this,t,e,r,o);this.nodeRepulsion=n.DEFAULT_REPULSION_STRENGTH;this.springForceX=0;this.springForceY=0;this.repulsionForceX=0;this.repulsionForceY=0;this.gravitationForceX=0;this.gravitationForceY=0;this.displacementX=0;this.displacementY=0;this.startX=0;this.finishX=0;this.startY=0;this.finishY=0;this.surrounding=[]}o.prototype=Object.create(i.prototype);for(var a in i){o[a]=i[a]}o.prototype.setGridCoordinates=function(t,e,r,i){this.startX=t;this.finishX=e;this.startY=r;this.finishY=i};t.exports=o},function(t,e,r){"use strict";function i(t,e){this.width=0;this.height=0;if(t!==null&&e!==null){this.height=e;this.width=t}}i.prototype.getWidth=function(){return this.width};i.prototype.setWidth=function(t){this.width=t};i.prototype.getHeight=function(){return this.height};i.prototype.setHeight=function(t){this.height=t};t.exports=i},function(t,e,r){"use strict";var i=r(14);function n(){this.map={};this.keys=[]}n.prototype.put=function(t,e){var r=i.createID(t);if(!this.contains(r)){this.map[r]=e;this.keys.push(t)}};n.prototype.contains=function(t){var e=i.createID(t);return this.map[t]!=null};n.prototype.get=function(t){var e=i.createID(t);return this.map[e]};n.prototype.keySet=function(){return this.keys};t.exports=n},function(t,e,r){"use strict";var i=r(14);function n(){this.set={}}n.prototype.add=function(t){var e=i.createID(t);if(!this.contains(e))this.set[e]=t};n.prototype.remove=function(t){delete this.set[i.createID(t)]};n.prototype.clear=function(){this.set={}};n.prototype.contains=function(t){return this.set[i.createID(t)]==t};n.prototype.isEmpty=function(){return this.size()===0};n.prototype.size=function(){return Object.keys(this.set).length};n.prototype.addAllTo=function(t){var e=Object.keys(this.set);var r=e.length;for(var i=0;i<r;i++){t.push(this.set[e[i]])}};n.prototype.size=function(){return Object.keys(this.set).length};n.prototype.addAll=function(t){var e=t.length;for(var r=0;r<e;r++){var i=t[r];this.add(i)}};t.exports=n},function(t,e,r){"use strict";function i(){}i.multMat=function(t,e){var r=[];for(var i=0;i<t.length;i++){r[i]=[];for(var n=0;n<e[0].length;n++){r[i][n]=0;for(var o=0;o<t[0].length;o++){r[i][n]+=t[i][o]*e[o][n]}}}return r};i.transpose=function(t){var e=[];for(var r=0;r<t[0].length;r++){e[r]=[];for(var i=0;i<t.length;i++){e[r][i]=t[i][r]}}return e};i.multCons=function(t,e){var r=[];for(var i=0;i<t.length;i++){r[i]=t[i]*e}return r};i.minusOp=function(t,e){var r=[];for(var i=0;i<t.length;i++){r[i]=t[i]-e[i]}return r};i.dotProduct=function(t,e){var r=0;for(var i=0;i<t.length;i++){r+=t[i]*e[i]}return r};i.mag=function(t){return Math.sqrt(this.dotProduct(t,t))};i.normalize=function(t){var e=[];var r=this.mag(t);for(var i=0;i<t.length;i++){e[i]=t[i]/r}return e};i.multGamma=function(t){var e=[];var r=0;for(var i=0;i<t.length;i++){r+=t[i]}r*=-1/t.length;for(var n=0;n<t.length;n++){e[n]=r+t[n]}return e};i.multL=function(t,e,r){var i=[];var n=[];var o=[];for(var a=0;a<e[0].length;a++){var s=0;for(var h=0;h<e.length;h++){s+=-.5*e[h][a]*t[h]}n[a]=s}for(var l=0;l<r.length;l++){var c=0;for(var d=0;d<r.length;d++){c+=r[l][d]*n[d]}o[l]=c}for(var f=0;f<e.length;f++){var g=0;for(var u=0;u<e[0].length;u++){g+=e[f][u]*o[u]}i[f]=g}return i};t.exports=i},function(t,e,r){"use strict";var i=function(){function t(t,e){for(var r=0;r<e.length;r++){var i=e[r];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(t,i.key,i)}}return function(e,r,i){if(r)t(e.prototype,r);if(i)t(e,i);return e}}();function n(t,e){if(!(t instanceof e)){throw new TypeError("Cannot call a class as a function")}}var o=r(11);var a=function(){function t(e,r){n(this,t);if(r!==null||r!==undefined)this.compareFunction=this._defaultCompareFunction;var i=void 0;if(e instanceof o)i=e.size();else i=e.length;this._quicksort(e,0,i-1)}i(t,[{key:"_quicksort",value:function t(e,r,i){if(r<i){var n=this._partition(e,r,i);this._quicksort(e,r,n);this._quicksort(e,n+1,i)}}},{key:"_partition",value:function t(e,r,i){var n=this._get(e,r);var o=r;var a=i;while(true){while(this.compareFunction(n,this._get(e,a))){a--}while(this.compareFunction(this._get(e,o),n)){o++}if(o<a){this._swap(e,o,a);o++;a--}else return a}}},{key:"_get",value:function t(e,r){if(e instanceof o)return e.get_object_at(r);else return e[r]}},{key:"_set",value:function t(e,r,i){if(e instanceof o)e.set_object_at(r,i);else e[r]=i}},{key:"_swap",value:function t(e,r,i){var n=this._get(e,r);this._set(e,r,this._get(e,i));this._set(e,i,n)}},{key:"_defaultCompareFunction",value:function t(e,r){return r>e}}]);return t}();t.exports=a},function(t,e,r){"use strict";function i(){}i.svd=function(t){this.U=null;this.V=null;this.s=null;this.m=0;this.n=0;this.m=t.length;this.n=t[0].length;var e=Math.min(this.m,this.n);this.s=function(t){var e=[];while(t-- >0){e.push(0)}return e}(Math.min(this.m+1,this.n));this.U=function(t){var e=function t(e){if(e.length==0){return 0}else{var r=[];for(var i=0;i<e[0];i++){r.push(t(e.slice(1)))}return r}};return e(t)}([this.m,e]);this.V=function(t){var e=function t(e){if(e.length==0){return 0}else{var r=[];for(var i=0;i<e[0];i++){r.push(t(e.slice(1)))}return r}};return e(t)}([this.n,this.n]);var r=function(t){var e=[];while(t-- >0){e.push(0)}return e}(this.n);var n=function(t){var e=[];while(t-- >0){e.push(0)}return e}(this.m);var o=true;var a=true;var s=Math.min(this.m-1,this.n);var h=Math.max(0,Math.min(this.n-2,this.m));for(var l=0;l<Math.max(s,h);l++){if(l<s){this.s[l]=0;for(var c=l;c<this.m;c++){this.s[l]=i.hypot(this.s[l],t[c][l])}if(this.s[l]!==0){if(t[l][l]<0){this.s[l]=-this.s[l]}for(var d=l;d<this.m;d++){t[d][l]/=this.s[l]}t[l][l]+=1}this.s[l]=-this.s[l]}for(var f=l+1;f<this.n;f++){if(function(t,e){return t&&e}(l<s,this.s[l]!==0)){var g=0;for(var u=l;u<this.m;u++){g+=t[u][l]*t[u][f]}g=-g/t[l][l];for(var p=l;p<this.m;p++){t[p][f]+=g*t[p][l]}}r[f]=t[l][f]}if(function(t,e){return t&&e}(o,l<s)){for(var v=l;v<this.m;v++){this.U[v][l]=t[v][l]}}if(l<h){r[l]=0;for(var y=l+1;y<this.n;y++){r[l]=i.hypot(r[l],r[y])}if(r[l]!==0){if(r[l+1]<0){r[l]=-r[l]}for(var m=l+1;m<this.n;m++){r[m]/=r[l]}r[l+1]+=1}r[l]=-r[l];if(function(t,e){return t&&e}(l+1<this.m,r[l]!==0)){for(var E=l+1;E<this.m;E++){n[E]=0}for(var N=l+1;N<this.n;N++){for(var T=l+1;T<this.m;T++){n[T]+=r[N]*t[T][N]}}for(var A=l+1;A<this.n;A++){var w=-r[A]/r[l+1];for(var L=l+1;L<this.m;L++){t[L][A]+=w*n[L]}}}if(a){for(var I=l+1;I<this.n;I++){this.V[I][l]=r[I]}}}}var _=Math.min(this.n,this.m+1);if(s<this.n){this.s[s]=t[s][s]}if(this.m<_){this.s[_-1]=0}if(h+1<_){r[h]=t[h][_-1]}r[_-1]=0;if(o){for(var C=s;C<e;C++){for(var M=0;M<this.m;M++){this.U[M][C]=0}this.U[C][C]=1}for(var x=s-1;x>=0;x--){if(this.s[x]!==0){for(var O=x+1;O<e;O++){var D=0;for(var R=x;R<this.m;R++){D+=this.U[R][x]*this.U[R][O]}D=-D/this.U[x][x];for(var b=x;b<this.m;b++){this.U[b][O]+=D*this.U[b][x]}}for(var G=x;G<this.m;G++){this.U[G][x]=-this.U[G][x]}this.U[x][x]=1+this.U[x][x];for(var F=0;F<x-1;F++){this.U[F][x]=0}}else{for(var S=0;S<this.m;S++){this.U[S][x]=0}this.U[x][x]=1}}}if(a){for(var P=this.n-1;P>=0;P--){if(function(t,e){return t&&e}(P<h,r[P]!==0)){for(var U=P+1;U<e;U++){var k=0;for(var Y=P+1;Y<this.n;Y++){k+=this.V[Y][P]*this.V[Y][U]}k=-k/this.V[P+1][P];for(var H=P+1;H<this.n;H++){this.V[H][U]+=k*this.V[H][P]}}}for(var X=0;X<this.n;X++){this.V[X][P]=0}this.V[P][P]=1}}var z=_-1;var V=0;var B=Math.pow(2,-52);var W=Math.pow(2,-966);while(_>0){var j=void 0;var q=void 0;for(j=_-2;j>=-1;j--){if(j===-1){break}if(Math.abs(r[j])<=W+B*(Math.abs(this.s[j])+Math.abs(this.s[j+1]))){r[j]=0;break}}if(j===_-2){q=4}else{var $=void 0;for($=_-1;$>=j;$--){if($===j){break}var K=($!==_?Math.abs(r[$]):0)+($!==j+1?Math.abs(r[$-1]):0);if(Math.abs(this.s[$])<=W+B*K){this.s[$]=0;break}}if($===j){q=3}else if($===_-1){q=1}else{q=2;j=$}}j++;switch(q){case 1:{var Z=r[_-2];r[_-2]=0;for(var Q=_-2;Q>=j;Q--){var J=i.hypot(this.s[Q],Z);var tt=this.s[Q]/J;var et=Z/J;this.s[Q]=J;if(Q!==j){Z=-et*r[Q-1];r[Q-1]=tt*r[Q-1]}if(a){for(var rt=0;rt<this.n;rt++){J=tt*this.V[rt][Q]+et*this.V[rt][_-1];this.V[rt][_-1]=-et*this.V[rt][Q]+tt*this.V[rt][_-1];this.V[rt][Q]=J}}}};break;case 2:{var it=r[j-1];r[j-1]=0;for(var nt=j;nt<_;nt++){var ot=i.hypot(this.s[nt],it);var at=this.s[nt]/ot;var st=it/ot;this.s[nt]=ot;it=-st*r[nt];r[nt]=at*r[nt];if(o){for(var ht=0;ht<this.m;ht++){ot=at*this.U[ht][nt]+st*this.U[ht][j-1];this.U[ht][j-1]=-st*this.U[ht][nt]+at*this.U[ht][j-1];this.U[ht][nt]=ot}}}};break;case 3:{var lt=Math.max(Math.max(Math.max(Math.max(Math.abs(this.s[_-1]),Math.abs(this.s[_-2])),Math.abs(r[_-2])),Math.abs(this.s[j])),Math.abs(r[j]));var ct=this.s[_-1]/lt;var dt=this.s[_-2]/lt;var ft=r[_-2]/lt;var gt=this.s[j]/lt;var ut=r[j]/lt;var pt=((dt+ct)*(dt-ct)+ft*ft)/2;var vt=ct*ft*(ct*ft);var yt=0;if(function(t,e){return t||e}(pt!==0,vt!==0)){yt=Math.sqrt(pt*pt+vt);if(pt<0){yt=-yt}yt=vt/(pt+yt)}var mt=(gt+ct)*(gt-ct)+yt;var Et=gt*ut;for(var Nt=j;Nt<_-1;Nt++){var Tt=i.hypot(mt,Et);var At=mt/Tt;var wt=Et/Tt;if(Nt!==j){r[Nt-1]=Tt}mt=At*this.s[Nt]+wt*r[Nt];r[Nt]=At*r[Nt]-wt*this.s[Nt];Et=wt*this.s[Nt+1];this.s[Nt+1]=At*this.s[Nt+1];if(a){for(var Lt=0;Lt<this.n;Lt++){Tt=At*this.V[Lt][Nt]+wt*this.V[Lt][Nt+1];this.V[Lt][Nt+1]=-wt*this.V[Lt][Nt]+At*this.V[Lt][Nt+1];this.V[Lt][Nt]=Tt}}Tt=i.hypot(mt,Et);At=mt/Tt;wt=Et/Tt;this.s[Nt]=Tt;mt=At*r[Nt]+wt*this.s[Nt+1];this.s[Nt+1]=-wt*r[Nt]+At*this.s[Nt+1];Et=wt*r[Nt+1];r[Nt+1]=At*r[Nt+1];if(o&&Nt<this.m-1){for(var It=0;It<this.m;It++){Tt=At*this.U[It][Nt]+wt*this.U[It][Nt+1];this.U[It][Nt+1]=-wt*this.U[It][Nt]+At*this.U[It][Nt+1];this.U[It][Nt]=Tt}}}r[_-2]=mt;V=V+1};break;case 4:{if(this.s[j]<=0){this.s[j]=this.s[j]<0?-this.s[j]:0;if(a){for(var _t=0;_t<=z;_t++){this.V[_t][j]=-this.V[_t][j]}}}while(j<z){if(this.s[j]>=this.s[j+1]){break}var Ct=this.s[j];this.s[j]=this.s[j+1];this.s[j+1]=Ct;if(a&&j<this.n-1){for(var Mt=0;Mt<this.n;Mt++){Ct=this.V[Mt][j+1];this.V[Mt][j+1]=this.V[Mt][j];this.V[Mt][j]=Ct}}if(o&&j<this.m-1){for(var xt=0;xt<this.m;xt++){Ct=this.U[xt][j+1];this.U[xt][j+1]=this.U[xt][j];this.U[xt][j]=Ct}}j++}V=0;_--};break}}var Ot={U:this.U,V:this.V,S:this.s};return Ot};i.hypot=function(t,e){var r=void 0;if(Math.abs(t)>Math.abs(e)){r=e/t;r=Math.abs(t)*Math.sqrt(1+r*r)}else if(e!=0){r=t/e;r=Math.abs(e)*Math.sqrt(1+r*r)}else{r=0}return r};t.exports=i},function(t,e,r){"use strict";var i=function(){function t(t,e){for(var r=0;r<e.length;r++){var i=e[r];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(t,i.key,i)}}return function(e,r,i){if(r)t(e.prototype,r);if(i)t(e,i);return e}}();function n(t,e){if(!(t instanceof e)){throw new TypeError("Cannot call a class as a function")}}var o=function(){function t(e,r){var i=arguments.length>2&&arguments[2]!==undefined?arguments[2]:1;var o=arguments.length>3&&arguments[3]!==undefined?arguments[3]:-1;var a=arguments.length>4&&arguments[4]!==undefined?arguments[4]:-1;n(this,t);this.sequence1=e;this.sequence2=r;this.match_score=i;this.mismatch_penalty=o;this.gap_penalty=a;this.iMax=e.length+1;this.jMax=r.length+1;this.grid=new Array(this.iMax);for(var s=0;s<this.iMax;s++){this.grid[s]=new Array(this.jMax);for(var h=0;h<this.jMax;h++){this.grid[s][h]=0}}this.tracebackGrid=new Array(this.iMax);for(var l=0;l<this.iMax;l++){this.tracebackGrid[l]=new Array(this.jMax);for(var c=0;c<this.jMax;c++){this.tracebackGrid[l][c]=[null,null,null]}}this.alignments=[];this.score=-1;this.computeGrids()}i(t,[{key:"getScore",value:function t(){return this.score}},{key:"getAlignments",value:function t(){return this.alignments}},{key:"computeGrids",value:function t(){for(var e=1;e<this.jMax;e++){this.grid[0][e]=this.grid[0][e-1]+this.gap_penalty;this.tracebackGrid[0][e]=[false,false,true]}for(var r=1;r<this.iMax;r++){this.grid[r][0]=this.grid[r-1][0]+this.gap_penalty;this.tracebackGrid[r][0]=[false,true,false]}for(var i=1;i<this.iMax;i++){for(var n=1;n<this.jMax;n++){var o=void 0;if(this.sequence1[i-1]===this.sequence2[n-1])o=this.grid[i-1][n-1]+this.match_score;else o=this.grid[i-1][n-1]+this.mismatch_penalty;var a=this.grid[i-1][n]+this.gap_penalty;var s=this.grid[i][n-1]+this.gap_penalty;var h=[o,a,s];var l=this.arrayAllMaxIndexes(h);this.grid[i][n]=h[l[0]];this.tracebackGrid[i][n]=[l.includes(0),l.includes(1),l.includes(2)]}}this.score=this.grid[this.iMax-1][this.jMax-1]}},{key:"alignmentTraceback",value:function t(){var e=[];e.push({pos:[this.sequence1.length,this.sequence2.length],seq1:"",seq2:""});while(e[0]){var r=e[0];var i=this.tracebackGrid[r.pos[0]][r.pos[1]];if(i[0]){e.push({pos:[r.pos[0]-1,r.pos[1]-1],seq1:this.sequence1[r.pos[0]-1]+r.seq1,seq2:this.sequence2[r.pos[1]-1]+r.seq2})}if(i[1]){e.push({pos:[r.pos[0]-1,r.pos[1]],seq1:this.sequence1[r.pos[0]-1]+r.seq1,seq2:"-"+r.seq2})}if(i[2]){e.push({pos:[r.pos[0],r.pos[1]-1],seq1:"-"+r.seq1,seq2:this.sequence2[r.pos[1]-1]+r.seq2})}if(r.pos[0]===0&&r.pos[1]===0)this.alignments.push({sequence1:r.seq1,sequence2:r.seq2});e.shift()}return this.alignments}},{key:"getAllIndexes",value:function t(e,r){var i=[],n=-1;while((n=e.indexOf(r,n+1))!==-1){i.push(n)}return i}},{key:"arrayAllMaxIndexes",value:function t(e){return this.getAllIndexes(e,Math.max.apply(null,e))}}]);return t}();t.exports=o},function(t,e,r){"use strict";var i=function t(){return};i.FDLayout=r(18);i.FDLayoutConstants=r(4);i.FDLayoutEdge=r(19);i.FDLayoutNode=r(20);i.DimensionD=r(21);i.HashMap=r(22);i.HashSet=r(23);i.IGeometry=r(8);i.IMath=r(9);i.Integer=r(10);i.Point=r(12);i.PointD=r(5);i.RandomSeed=r(16);i.RectangleD=r(13);i.Transform=r(17);i.UniqueIDGeneretor=r(14);i.Quicksort=r(25);i.LinkedList=r(11);i.LGraphObject=r(2);i.LGraph=r(6);i.LEdge=r(1);i.LGraphManager=r(7);i.LNode=r(3);i.Layout=r(15);i.LayoutConstants=r(0);i.NeedlemanWunsch=r(27);i.Matrix=r(24);i.SVD=r(26);t.exports=i},function(t,e,r){"use strict";function i(){this.listeners=[]}var n=i.prototype;n.addListener=function(t,e){this.listeners.push({event:t,callback:e})};n.removeListener=function(t,e){for(var r=this.listeners.length;r>=0;r--){var i=this.listeners[r];if(i.event===t&&i.callback===e){this.listeners.splice(r,1)}}};n.emit=function(t,e){for(var r=0;r<this.listeners.length;r++){var i=this.listeners[r];if(t===i.event){i.callback(e)}}};t.exports=i}])}))},17371:(t,e,r)=>{"use strict";r.d(e,{diagram:()=>yt});var i=r(68232);var n=r(76261);var o=r(19163);var a=r(13249);var s=r(96049);var h=r(93113);var l=r(75905);var c=r(24010);var d=r(76405);var f=r(26527);var g=r.n(f);var u=r(24982);var p={L:"left",R:"right",T:"top",B:"bottom"};var v={L:(0,l.K2)((t=>`${t},${t/2} 0,${t} 0,0`),"L"),R:(0,l.K2)((t=>`0,${t/2} ${t},0 ${t},${t}`),"R"),T:(0,l.K2)((t=>`0,0 ${t},0 ${t/2},${t}`),"T"),B:(0,l.K2)((t=>`${t/2},0 ${t},${t} 0,${t}`),"B")};var y={L:(0,l.K2)(((t,e)=>t-e+2),"L"),R:(0,l.K2)(((t,e)=>t-2),"R"),T:(0,l.K2)(((t,e)=>t-e+2),"T"),B:(0,l.K2)(((t,e)=>t-2),"B")};var m=(0,l.K2)((function(t){if(N(t)){return t==="L"?"R":"L"}else{return t==="T"?"B":"T"}}),"getOppositeArchitectureDirection");var E=(0,l.K2)((function(t){const e=t;return e==="L"||e==="R"||e==="T"||e==="B"}),"isArchitectureDirection");var N=(0,l.K2)((function(t){const e=t;return e==="L"||e==="R"}),"isArchitectureDirectionX");var T=(0,l.K2)((function(t){const e=t;return e==="T"||e==="B"}),"isArchitectureDirectionY");var A=(0,l.K2)((function(t,e){const r=N(t)&&T(e);const i=T(t)&&N(e);return r||i}),"isArchitectureDirectionXY");var w=(0,l.K2)((function(t){const e=t[0];const r=t[1];const i=N(e)&&T(r);const n=T(e)&&N(r);return i||n}),"isArchitecturePairXY");var L=(0,l.K2)((function(t){return t!=="LL"&&t!=="RR"&&t!=="TT"&&t!=="BB"}),"isValidArchitectureDirectionPair");var I=(0,l.K2)((function(t,e){const r=`${t}${e}`;return L(r)?r:void 0}),"getArchitectureDirectionPair");var _=(0,l.K2)((function([t,e],r){const i=r[0];const n=r[1];if(N(i)){if(T(n)){return[t+(i==="L"?-1:1),e+(n==="T"?1:-1)]}else{return[t+(i==="L"?-1:1),e]}}else{if(N(n)){return[t+(n==="L"?1:-1),e+(i==="T"?1:-1)]}else{return[t,e+(i==="T"?1:-1)]}}}),"shiftPositionByArchitectureDirectionPair");var C=(0,l.K2)((function(t){if(t==="LT"||t==="TL"){return[1,1]}else if(t==="BL"||t==="LB"){return[1,-1]}else if(t==="BR"||t==="RB"){return[-1,-1]}else{return[-1,1]}}),"getArchitectureDirectionXYFactors");var M=(0,l.K2)((function(t,e){if(A(t,e)){return"bend"}else if(N(t)){return"horizontal"}return"vertical"}),"getArchitectureDirectionAlignment");var x=(0,l.K2)((function(t){const e=t;return e.type==="service"}),"isArchitectureService");var O=(0,l.K2)((function(t){const e=t;return e.type==="junction"}),"isArchitectureJunction");var D=(0,l.K2)((t=>t.data()),"edgeData");var R=(0,l.K2)((t=>t.data()),"nodeData");var b=l.UI.architecture;var G=new a.m((()=>({nodes:{},groups:{},edges:[],registeredIds:{},config:b,dataStructures:void 0,elements:{}})));var F=(0,l.K2)((()=>{G.reset();(0,l.IU)()}),"clear");var S=(0,l.K2)((function({id:t,icon:e,in:r,title:i,iconText:n}){if(G.records.registeredIds[t]!==void 0){throw new Error(`The service id [${t}] is already in use by another ${G.records.registeredIds[t]}`)}if(r!==void 0){if(t===r){throw new Error(`The service [${t}] cannot be placed within itself`)}if(G.records.registeredIds[r]===void 0){throw new Error(`The service [${t}]'s parent does not exist. Please make sure the parent is created before this service`)}if(G.records.registeredIds[r]==="node"){throw new Error(`The service [${t}]'s parent is not a group`)}}G.records.registeredIds[t]="node";G.records.nodes[t]={id:t,type:"service",icon:e,iconText:n,title:i,edges:[],in:r}}),"addService");var P=(0,l.K2)((()=>Object.values(G.records.nodes).filter(x)),"getServices");var U=(0,l.K2)((function({id:t,in:e}){G.records.registeredIds[t]="node";G.records.nodes[t]={id:t,type:"junction",edges:[],in:e}}),"addJunction");var k=(0,l.K2)((()=>Object.values(G.records.nodes).filter(O)),"getJunctions");var Y=(0,l.K2)((()=>Object.values(G.records.nodes)),"getNodes");var H=(0,l.K2)((t=>G.records.nodes[t]),"getNode");var X=(0,l.K2)((function({id:t,icon:e,in:r,title:i}){if(G.records.registeredIds[t]!==void 0){throw new Error(`The group id [${t}] is already in use by another ${G.records.registeredIds[t]}`)}if(r!==void 0){if(t===r){throw new Error(`The group [${t}] cannot be placed within itself`)}if(G.records.registeredIds[r]===void 0){throw new Error(`The group [${t}]'s parent does not exist. Please make sure the parent is created before this group`)}if(G.records.registeredIds[r]==="node"){throw new Error(`The group [${t}]'s parent is not a group`)}}G.records.registeredIds[t]="group";G.records.groups[t]={id:t,icon:e,title:i,in:r}}),"addGroup");var z=(0,l.K2)((()=>Object.values(G.records.groups)),"getGroups");var V=(0,l.K2)((function({lhsId:t,rhsId:e,lhsDir:r,rhsDir:i,lhsInto:n,rhsInto:o,lhsGroup:a,rhsGroup:s,title:h}){if(!E(r)){throw new Error(`Invalid direction given for left hand side of edge ${t}--${e}. Expected (L,R,T,B) got ${r}`)}if(!E(i)){throw new Error(`Invalid direction given for right hand side of edge ${t}--${e}. Expected (L,R,T,B) got ${i}`)}if(G.records.nodes[t]===void 0&&G.records.groups[t]===void 0){throw new Error(`The left-hand id [${t}] does not yet exist. Please create the service/group before declaring an edge to it.`)}if(G.records.nodes[e]===void 0&&G.records.groups[t]===void 0){throw new Error(`The right-hand id [${e}] does not yet exist. Please create the service/group before declaring an edge to it.`)}const l=G.records.nodes[t].in;const c=G.records.nodes[e].in;if(a&&l&&c&&l==c){throw new Error(`The left-hand id [${t}] is modified to traverse the group boundary, but the edge does not pass through two groups.`)}if(s&&l&&c&&l==c){throw new Error(`The right-hand id [${e}] is modified to traverse the group boundary, but the edge does not pass through two groups.`)}const d={lhsId:t,lhsDir:r,lhsInto:n,lhsGroup:a,rhsId:e,rhsDir:i,rhsInto:o,rhsGroup:s,title:h};G.records.edges.push(d);if(G.records.nodes[t]&&G.records.nodes[e]){G.records.nodes[t].edges.push(G.records.edges[G.records.edges.length-1]);G.records.nodes[e].edges.push(G.records.edges[G.records.edges.length-1])}}),"addEdge");var B=(0,l.K2)((()=>G.records.edges),"getEdges");var W=(0,l.K2)((()=>{if(G.records.dataStructures===void 0){const t={};const e=Object.entries(G.records.nodes).reduce(((e,[r,i])=>{e[r]=i.edges.reduce(((e,i)=>{const n=H(i.lhsId)?.in;const o=H(i.rhsId)?.in;if(n&&o&&n!==o){const e=M(i.lhsDir,i.rhsDir);if(e!=="bend"){t[n]??={};t[n][o]=e;t[o]??={};t[o][n]=e}}if(i.lhsId===r){const t=I(i.lhsDir,i.rhsDir);if(t){e[t]=i.rhsId}}else{const t=I(i.rhsDir,i.lhsDir);if(t){e[t]=i.lhsId}}return e}),{});return e}),{});const r=Object.keys(e)[0];const i={[r]:1};const n=Object.keys(e).reduce(((t,e)=>e===r?t:{...t,[e]:1}),{});const o=(0,l.K2)((t=>{const r={[t]:[0,0]};const o=[t];while(o.length>0){const t=o.shift();if(t){i[t]=1;delete n[t];const a=e[t];const[s,h]=r[t];Object.entries(a).forEach((([t,e])=>{if(!i[e]){r[e]=_([s,h],t);o.push(e)}}))}}return r}),"BFS");const a=[o(r)];while(Object.keys(n).length>0){a.push(o(Object.keys(n)[0]))}G.records.dataStructures={adjList:e,spatialMaps:a,groupAlignments:t}}return G.records.dataStructures}),"getDataStructures");var j=(0,l.K2)(((t,e)=>{G.records.elements[t]=e}),"setElementForId");var q=(0,l.K2)((t=>G.records.elements[t]),"getElementById");var $={clear:F,setDiagramTitle:l.ke,getDiagramTitle:l.ab,setAccTitle:l.SV,getAccTitle:l.iN,setAccDescription:l.EI,getAccDescription:l.m7,addService:S,getServices:P,addJunction:U,getJunctions:k,getNodes:Y,getNode:H,addGroup:X,getGroups:z,addEdge:V,getEdges:B,setElementForId:j,getElementById:q,getDataStructures:W};function K(t){const e=(0,l.D7)().architecture;if(e?.[t]){return e[t]}return b[t]}(0,l.K2)(K,"getConfigField");var Z=(0,l.K2)(((t,e)=>{(0,o.S)(t,e);t.groups.map(e.addGroup);t.services.map((t=>e.addService({...t,type:"service"})));t.junctions.map((t=>e.addJunction({...t,type:"junction"})));t.edges.map(e.addEdge)}),"populateDb");var Q={parse:(0,l.K2)((async t=>{const e=await(0,c.qg)("architecture",t);l.Rm.debug(e);Z(e,$)}),"parse")};var J=(0,l.K2)((t=>`\n  .edge {\n    stroke-width: ${t.archEdgeWidth};\n    stroke: ${t.archEdgeColor};\n    fill: none;\n  }\n\n  .arrow {\n    fill: ${t.archEdgeArrowColor};\n  }\n\n  .node-bkg {\n    fill: none;\n    stroke: ${t.archGroupBorderColor};\n    stroke-width: ${t.archGroupBorderWidth};\n    stroke-dasharray: 8;\n  }\n  .node-icon-text {\n    display: flex; \n    align-items: center;\n  }\n  \n  .node-icon-text > div {\n    color: #fff;\n    margin: 1px;\n    height: fit-content;\n    text-align: center;\n    overflow: hidden;\n    display: -webkit-box;\n    -webkit-box-orient: vertical;\n  }\n`),"getStyles");var tt=J;var et=(0,l.K2)((t=>`<g><rect width="80" height="80" style="fill: #087ebf; stroke-width: 0px;"/>${t}</g>`),"wrapIcon");var rt={prefix:"mermaid-architecture",height:80,width:80,icons:{database:{body:et('<path id="b" data-name="4" d="m20,57.86c0,3.94,8.95,7.14,20,7.14s20-3.2,20-7.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><path id="c" data-name="3" d="m20,45.95c0,3.94,8.95,7.14,20,7.14s20-3.2,20-7.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><path id="d" data-name="2" d="m20,34.05c0,3.94,8.95,7.14,20,7.14s20-3.2,20-7.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse id="e" data-name="1" cx="40" cy="22.14" rx="20" ry="7.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="20" y1="57.86" x2="20" y2="22.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="60" y1="57.86" x2="60" y2="22.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/>')},server:{body:et('<rect x="17.5" y="17.5" width="45" height="45" rx="2" ry="2" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="17.5" y1="32.5" x2="62.5" y2="32.5" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="17.5" y1="47.5" x2="62.5" y2="47.5" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><g><path d="m56.25,25c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: #fff; stroke-width: 0px;"/><path d="m56.25,25c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: none; stroke: #fff; stroke-miterlimit: 10;"/></g><g><path d="m56.25,40c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: #fff; stroke-width: 0px;"/><path d="m56.25,40c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: none; stroke: #fff; stroke-miterlimit: 10;"/></g><g><path d="m56.25,55c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: #fff; stroke-width: 0px;"/><path d="m56.25,55c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: none; stroke: #fff; stroke-miterlimit: 10;"/></g><g><circle cx="32.5" cy="25" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="27.5" cy="25" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="22.5" cy="25" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/></g><g><circle cx="32.5" cy="40" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="27.5" cy="40" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="22.5" cy="40" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/></g><g><circle cx="32.5" cy="55" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="27.5" cy="55" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="22.5" cy="55" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/></g>')},disk:{body:et('<rect x="20" y="15" width="40" height="50" rx="1" ry="1" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="24" cy="19.17" rx=".8" ry=".83" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="56" cy="19.17" rx=".8" ry=".83" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="24" cy="60.83" rx=".8" ry=".83" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="56" cy="60.83" rx=".8" ry=".83" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="40" cy="33.75" rx="14" ry="14.58" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="40" cy="33.75" rx="4" ry="4.17" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><path d="m37.51,42.52l-4.83,13.22c-.26.71-1.1,1.02-1.76.64l-4.18-2.42c-.66-.38-.81-1.26-.33-1.84l9.01-10.8c.88-1.05,2.56-.08,2.09,1.2Z" style="fill: #fff; stroke-width: 0px;"/>')},internet:{body:et('<circle cx="40" cy="40" r="22.5" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="40" y1="17.5" x2="40" y2="62.5" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="17.5" y1="40" x2="62.5" y2="40" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><path d="m39.99,17.51c-15.28,11.1-15.28,33.88,0,44.98" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><path d="m40.01,17.51c15.28,11.1,15.28,33.88,0,44.98" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="19.75" y1="30.1" x2="60.25" y2="30.1" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="19.75" y1="49.9" x2="60.25" y2="49.9" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/>')},cloud:{body:et('<path d="m65,47.5c0,2.76-2.24,5-5,5H20c-2.76,0-5-2.24-5-5,0-1.87,1.03-3.51,2.56-4.36-.04-.21-.06-.42-.06-.64,0-2.6,2.48-4.74,5.65-4.97,1.65-4.51,6.34-7.76,11.85-7.76.86,0,1.69.08,2.5.23,2.09-1.57,4.69-2.5,7.5-2.5,6.1,0,11.19,4.38,12.28,10.17,2.14.56,3.72,2.51,3.72,4.83,0,.03,0,.07-.01.1,2.29.46,4.01,2.48,4.01,4.9Z" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/>')},unknown:i.Gc,blank:{body:et("")}}};var it=(0,l.K2)((async function(t,e){const r=K("padding");const i=K("iconSize");const o=i/2;const a=i/6;const s=a/2;await Promise.all(e.edges().map((async e=>{const{source:i,sourceDir:h,sourceArrow:c,sourceGroup:d,target:f,targetDir:g,targetArrow:u,targetGroup:p,label:m}=D(e);let{x:E,y:L}=e[0].sourceEndpoint();const{x:_,y:M}=e[0].midpoint();let{x,y:O}=e[0].targetEndpoint();const R=r+4;if(d){if(N(h)){E+=h==="L"?-R:R}else{L+=h==="T"?-R:R+18}}if(p){if(N(g)){x+=g==="L"?-R:R}else{O+=g==="T"?-R:R+18}}if(!d&&$.getNode(i)?.type==="junction"){if(N(h)){E+=h==="L"?o:-o}else{L+=h==="T"?o:-o}}if(!p&&$.getNode(f)?.type==="junction"){if(N(g)){x+=g==="L"?o:-o}else{O+=g==="T"?o:-o}}if(e[0]._private.rscratch){const e=t.insert("g");e.insert("path").attr("d",`M ${E},${L} L ${_},${M} L${x},${O} `).attr("class","edge");if(c){const t=N(h)?y[h](E,a):E-s;const r=T(h)?y[h](L,a):L-s;e.insert("polygon").attr("points",v[h](a)).attr("transform",`translate(${t},${r})`).attr("class","arrow")}if(u){const t=N(g)?y[g](x,a):x-s;const r=T(g)?y[g](O,a):O-s;e.insert("polygon").attr("points",v[g](a)).attr("transform",`translate(${t},${r})`).attr("class","arrow")}if(m){const t=!A(h,g)?N(h)?"X":"Y":"XY";let r=0;if(t==="X"){r=Math.abs(E-x)}else if(t==="Y"){r=Math.abs(L-O)/1.5}else{r=Math.abs(E-x)/2}const i=e.append("g");await(0,n.GZ)(i,m,{useHtmlLabels:false,width:r,classes:"architecture-service-label"},(0,l.D7)());i.attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","middle").attr("text-anchor","middle");if(t==="X"){i.attr("transform","translate("+_+", "+M+")")}else if(t==="Y"){i.attr("transform","translate("+_+", "+M+") rotate(-90)")}else if(t==="XY"){const t=I(h,g);if(t&&w(t)){const e=i.node().getBoundingClientRect();const[r,n]=C(t);i.attr("dominant-baseline","auto").attr("transform",`rotate(${-1*r*n*45})`);const o=i.node().getBoundingClientRect();i.attr("transform",`\n                translate(${_}, ${M-e.height/2})\n                translate(${r*o.width/2}, ${n*o.height/2})\n                rotate(${-1*r*n*45}, 0, ${e.height/2})\n              `)}}}}})))}),"drawEdges");var nt=(0,l.K2)((async function(t,e){const r=K("padding");const o=r*.75;const a=K("fontSize");const s=K("iconSize");const h=s/2;await Promise.all(e.nodes().map((async e=>{const r=R(e);if(r.type==="group"){const{h:s,w:c,x1:d,y1:f}=e.boundingBox();t.append("rect").attr("x",d+h).attr("y",f+h).attr("width",c).attr("height",s).attr("class","node-bkg");const g=t.append("g");let u=d;let p=f;if(r.icon){const t=g.append("g");t.html(`<g>${await(0,i.WY)(r.icon,{height:o,width:o,fallbackPrefix:rt.prefix})}</g>`);t.attr("transform","translate("+(u+h+1)+", "+(p+h+1)+")");u+=o;p+=a/2-1-2}if(r.label){const t=g.append("g");await(0,n.GZ)(t,r.label,{useHtmlLabels:false,width:c,classes:"architecture-service-label"},(0,l.D7)());t.attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","start").attr("text-anchor","start");t.attr("transform","translate("+(u+h+4)+", "+(p+h+2)+")")}}})))}),"drawGroups");var ot=(0,l.K2)((async function(t,e,r){for(const o of r){const r=e.append("g");const a=K("iconSize");if(o.title){const t=r.append("g");await(0,n.GZ)(t,o.title,{useHtmlLabels:false,width:a*1.5,classes:"architecture-service-label"},(0,l.D7)());t.attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","middle").attr("text-anchor","middle");t.attr("transform","translate("+a/2+", "+a+")")}const s=r.append("g");if(o.icon){s.html(`<g>${await(0,i.WY)(o.icon,{height:a,width:a,fallbackPrefix:rt.prefix})}</g>`)}else if(o.iconText){s.html(`<g>${await(0,i.WY)("blank",{height:a,width:a,fallbackPrefix:rt.prefix})}</g>`);const t=s.append("g");const e=t.append("foreignObject").attr("width",a).attr("height",a);const r=e.append("div").attr("class","node-icon-text").attr("style",`height: ${a}px;`).append("div").html(o.iconText);const n=parseInt(window.getComputedStyle(r.node(),null).getPropertyValue("font-size").replace(/\D/g,""))??16;r.attr("style",`-webkit-line-clamp: ${Math.floor((a-2)/n)};`)}else{s.append("path").attr("class","node-bkg").attr("id","node-"+o.id).attr("d",`M0 ${a} v${-a} q0,-5 5,-5 h${a} q5,0 5,5 v${a} H0 Z`)}r.attr("class","architecture-service");const{width:h,height:c}=r._groups[0][0].getBBox();o.width=h;o.height=c;t.setElementForId(o.id,r)}return 0}),"drawServices");var at=(0,l.K2)((function(t,e,r){r.forEach((r=>{const i=e.append("g");const n=K("iconSize");const o=i.append("g");o.append("rect").attr("id","node-"+r.id).attr("fill-opacity","0").attr("width",n).attr("height",n);i.attr("class","architecture-junction");const{width:a,height:s}=i._groups[0][0].getBBox();i.width=a;i.height=s;t.setElementForId(r.id,i)}))}),"drawJunctions");(0,i.pC)([{name:rt.prefix,icons:rt}]);d.A.use(g());function st(t,e){t.forEach((t=>{e.add({group:"nodes",data:{type:"service",id:t.id,icon:t.icon,label:t.title,parent:t.in,width:K("iconSize"),height:K("iconSize")},classes:"node-service"})}))}(0,l.K2)(st,"addServices");function ht(t,e){t.forEach((t=>{e.add({group:"nodes",data:{type:"junction",id:t.id,parent:t.in,width:K("iconSize"),height:K("iconSize")},classes:"node-junction"})}))}(0,l.K2)(ht,"addJunctions");function lt(t,e){e.nodes().map((e=>{const r=R(e);if(r.type==="group"){return}r.x=e.position().x;r.y=e.position().y;const i=t.getElementById(r.id);i.attr("transform","translate("+(r.x||0)+","+(r.y||0)+")")}))}(0,l.K2)(lt,"positionNodes");function ct(t,e){t.forEach((t=>{e.add({group:"nodes",data:{type:"group",id:t.id,icon:t.icon,label:t.title,parent:t.in},classes:"node-group"})}))}(0,l.K2)(ct,"addGroups");function dt(t,e){t.forEach((t=>{const{lhsId:r,rhsId:i,lhsInto:n,lhsGroup:o,rhsInto:a,lhsDir:s,rhsDir:h,rhsGroup:l,title:c}=t;const d=A(t.lhsDir,t.rhsDir)?"segments":"straight";const f={id:`${r}-${i}`,label:c,source:r,sourceDir:s,sourceArrow:n,sourceGroup:o,sourceEndpoint:s==="L"?"0 50%":s==="R"?"100% 50%":s==="T"?"50% 0":"50% 100%",target:i,targetDir:h,targetArrow:a,targetGroup:l,targetEndpoint:h==="L"?"0 50%":h==="R"?"100% 50%":h==="T"?"50% 0":"50% 100%"};e.add({group:"edges",data:f,classes:d})}))}(0,l.K2)(dt,"addEdges");function ft(t,e,r){const i=(0,l.K2)(((t,e)=>Object.entries(t).reduce(((t,[i,n])=>{let o=0;const a=Object.entries(n);if(a.length===1){t[i]=a[0][1];return t}for(let s=0;s<a.length-1;s++){for(let n=s+1;n<a.length;n++){const[h,l]=a[s];const[c,d]=a[n];const f=r[h]?.[c];if(f===e){t[i]??=[];t[i]=[...t[i],...l,...d]}else if(h==="default"||c==="default"){t[i]??=[];t[i]=[...t[i],...l,...d]}else{const e=`${i}-${o++}`;t[e]=l;const r=`${i}-${o++}`;t[r]=d}}}return t}),{})),"flattenAlignments");const n=e.map((e=>{const r={};const n={};Object.entries(e).forEach((([e,[i,o]])=>{const a=t.getNode(e)?.in??"default";r[o]??={};r[o][a]??=[];r[o][a].push(e);n[i]??={};n[i][a]??=[];n[i][a].push(e)}));return{horiz:Object.values(i(r,"horizontal")).filter((t=>t.length>1)),vert:Object.values(i(n,"vertical")).filter((t=>t.length>1))}}));const[o,a]=n.reduce((([t,e],{horiz:r,vert:i})=>[[...t,...r],[...e,...i]]),[[],[]]);return{horizontal:o,vertical:a}}(0,l.K2)(ft,"getAlignments");function gt(t){const e=[];const r=(0,l.K2)((t=>`${t[0]},${t[1]}`),"posToStr");const i=(0,l.K2)((t=>t.split(",").map((t=>parseInt(t)))),"strToPos");t.forEach((t=>{const n=Object.fromEntries(Object.entries(t).map((([t,e])=>[r(e),t])));const o=[r([0,0])];const a={};const s={L:[-1,0],R:[1,0],T:[0,1],B:[0,-1]};while(o.length>0){const t=o.shift();if(t){a[t]=1;const h=n[t];if(h){const l=i(t);Object.entries(s).forEach((([t,i])=>{const s=r([l[0]+i[0],l[1]+i[1]]);const c=n[s];if(c&&!a[s]){o.push(s);e.push({[p[t]]:c,[p[m(t)]]:h,gap:1.5*K("iconSize")})}}))}}}}));return e}(0,l.K2)(gt,"getRelativeConstraints");function ut(t,e,r,i,n,{spatialMaps:o,groupAlignments:a}){return new Promise((s=>{const h=(0,u.Ltv)("body").append("div").attr("id","cy").attr("style","display:none");const c=(0,d.A)({container:document.getElementById("cy"),style:[{selector:"edge",style:{"curve-style":"straight",label:"data(label)","source-endpoint":"data(sourceEndpoint)","target-endpoint":"data(targetEndpoint)"}},{selector:"edge.segments",style:{"curve-style":"segments","segment-weights":"0","segment-distances":[.5],"edge-distances":"endpoints","source-endpoint":"data(sourceEndpoint)","target-endpoint":"data(targetEndpoint)"}},{selector:"node",style:{"compound-sizing-wrt-labels":"include"}},{selector:"node[label]",style:{"text-valign":"bottom","text-halign":"center","font-size":`${K("fontSize")}px`}},{selector:".node-service",style:{label:"data(label)",width:"data(width)",height:"data(height)"}},{selector:".node-junction",style:{width:"data(width)",height:"data(height)"}},{selector:".node-group",style:{padding:`${K("padding")}px`}}]});h.remove();ct(r,c);st(t,c);ht(e,c);dt(i,c);const f=ft(n,o,a);const g=gt(o);const p=c.layout({name:"fcose",quality:"proof",styleEnabled:false,animate:false,nodeDimensionsIncludeLabels:false,idealEdgeLength(t){const[e,r]=t.connectedNodes();const{parent:i}=R(e);const{parent:n}=R(r);const o=i===n?1.5*K("iconSize"):.5*K("iconSize");return o},edgeElasticity(t){const[e,r]=t.connectedNodes();const{parent:i}=R(e);const{parent:n}=R(r);const o=i===n?.45:.001;return o},alignmentConstraint:f,relativePlacementConstraint:g});p.one("layoutstop",(()=>{function t(t,e,r,i){let n,o;const{x:a,y:s}=t;const{x:h,y:l}=e;o=(i-s+(a-r)*(s-l)/(a-h))/Math.sqrt(1+Math.pow((s-l)/(a-h),2));n=Math.sqrt(Math.pow(i-s,2)+Math.pow(r-a,2)-Math.pow(o,2));const c=Math.sqrt(Math.pow(h-a,2)+Math.pow(l-s,2));n=n/c;let d=(h-a)*(i-s)-(l-s)*(r-a);switch(true){case d>=0:d=1;break;case d<0:d=-1;break}let f=(h-a)*(r-a)+(l-s)*(i-s);switch(true){case f>=0:f=1;break;case f<0:f=-1;break}o=Math.abs(o)*d;n=n*f;return{distances:o,weights:n}}(0,l.K2)(t,"getSegmentWeights");c.startBatch();for(const e of Object.values(c.edges())){if(e.data?.()){const{x:r,y:i}=e.source().position();const{x:n,y:o}=e.target().position();if(r!==n&&i!==o){const r=e.sourceEndpoint();const i=e.targetEndpoint();const{sourceDir:n}=D(e);const[o,a]=T(n)?[r.x,i.y]:[i.x,r.y];const{weights:s,distances:h}=t(r,i,o,a);e.style("segment-distances",h);e.style("segment-weights",s)}}}c.endBatch();p.run()}));p.run();c.ready((t=>{l.Rm.info("Ready",t);s(c)}))}))}(0,l.K2)(ut,"layoutArchitecture");var pt=(0,l.K2)((async(t,e,r,i)=>{const n=i.db;const o=n.getServices();const a=n.getJunctions();const s=n.getGroups();const c=n.getEdges();const d=n.getDataStructures();const f=(0,h.D)(e);const g=f.append("g");g.attr("class","architecture-edges");const u=f.append("g");u.attr("class","architecture-services");const p=f.append("g");p.attr("class","architecture-groups");await ot(n,u,o);at(n,u,a);const v=await ut(o,a,s,c,n,d);await it(g,v);await nt(p,v);lt(n,v);(0,l.ot)(void 0,f,K("padding"),K("useMaxWidth"))}),"draw");var vt={draw:pt};var yt={parser:Q,db:$,renderer:vt,styles:tt}},19163:(t,e,r)=>{"use strict";r.d(e,{S:()=>n});var i=r(75905);function n(t,e){if(t.accDescr){e.setAccDescription?.(t.accDescr)}if(t.accTitle){e.setAccTitle?.(t.accTitle)}if(t.title){e.setDiagramTitle?.(t.title)}}(0,i.K2)(n,"populateCommonDb")},13249:(t,e,r)=>{"use strict";r.d(e,{m:()=>n});var i=r(75905);var n=class{constructor(t){this.init=t;this.records=this.init()}static{(0,i.K2)(this,"ImperativeState")}reset(){this.records=this.init()}}}}]);