# 🚀 Installation Guide - Advanced Coffee Shop Optimization System

## Prerequisites

### System Requirements
- **Python**: 3.8+ (3.10+ recommended for optimal performance)
- **Memory**: 4GB RAM minimum, 8GB+ recommended
- **Storage**: 2GB free space for dependencies
- **OS**: Windows 10+, macOS 10.14+, or Linux (Ubuntu 18.04+)

### Platform-Specific Dependencies

#### Windows
```bash
# Install Visual C++ Build Tools (required for some packages)
# Download from: https://visualstudio.microsoft.com/visual-cpp-build-tools/
```

#### macOS
```bash
# Install Xcode Command Line Tools
xcode-select --install

# Install Homebrew (if not already installed)
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
```

#### Linux (Ubuntu/Debian)
```bash
# Install development packages
sudo apt update
sudo apt install python3-dev python3-pip build-essential libgeos-dev libproj-dev
```

## Installation Steps

### 1. Clone or Download the Project
```bash
# If using git
git clone <repository-url>
cd coffee-shop-optimization

# Or download and extract the ZIP file
```

### 2. Create Virtual Environment
```bash
# Create virtual environment
python -m venv coffee-optimization-env

# Activate virtual environment
# Windows:
coffee-optimization-env\Scripts\activate

# macOS/Linux:
source coffee-optimization-env/bin/activate
```

### 3. Upgrade pip and Install Core Tools
```bash
# Upgrade pip to latest version
python -m pip install --upgrade pip

# Install wheel for better package compilation
pip install wheel setuptools
```

### 4. Install Requirements
```bash
# Install all required packages
pip install -r requirements.txt

# For faster installation with parallel downloads
pip install -r requirements.txt --use-pep517 --no-cache-dir
```

### 5. Install Jupyter Kernel
```bash
# Register the environment as a Jupyter kernel
python -m ipykernel install --user --name=coffee-optimization --display-name="Coffee Optimization"
```

### 6. Verify Installation
```bash
# Test core imports
python -c "
import numpy as np
import pandas as pd
import pulp
import folium
import plotly
import geopy
print('✅ All core packages installed successfully!')
"
```

## Optional Enhancements

### GPU Acceleration (CUDA)
```bash
# For NVIDIA GPU acceleration (optional)
# Check CUDA version first: nvidia-smi

# For CUDA 11.x
pip install cupy-cuda11x

# For CUDA 12.x
pip install cupy-cuda12x
```

### Commercial Solvers (Advanced Users)
```bash
# Gurobi (requires license)
pip install gurobipy
# Register license: grbgetkey YOUR_LICENSE_KEY

# IBM CPLEX (requires license)
pip install cplex
```

### Web Dashboard Extensions
```bash
# For interactive web dashboards
pip install streamlit dash bokeh
```

## Troubleshooting

### Common Issues and Solutions

#### 1. Package Compilation Errors
```bash
# If packages fail to compile, try pre-compiled wheels
pip install --only-binary=all -r requirements.txt
```

#### 2. Geospatial Package Issues
```bash
# Install GDAL dependencies first
# Windows: Download from https://www.lfd.uci.edu/~gohlke/pythonlibs/
# macOS: brew install gdal
# Linux: sudo apt install gdal-bin libgdal-dev
```

#### 3. Memory Issues During Installation
```bash
# Install packages one by one if memory is limited
pip install numpy pandas
pip install matplotlib seaborn
pip install pulp scipy
# Continue with remaining packages...
```

#### 4. Jupyter Kernel Not Appearing
```bash
# List available kernels
jupyter kernelspec list

# Remove and reinstall kernel
jupyter kernelspec remove coffee-optimization
python -m ipykernel install --user --name=coffee-optimization
```

### Platform-Specific Fixes

#### Windows Specific
```bash
# If Visual C++ errors occur
pip install --upgrade setuptools wheel
pip install --only-binary=all package_name
```

#### macOS Specific
```bash
# If SSL certificate errors occur
/Applications/Python\ 3.x/Install\ Certificates.command

# If compiler errors occur
export CPPFLAGS=-I/usr/local/include
export LDFLAGS=-L/usr/local/lib
```

#### Linux Specific
```bash
# If permission errors occur
pip install --user -r requirements.txt

# If shared library errors occur
sudo ldconfig
```

## Performance Optimization

### Memory Management
```bash
# For large datasets, install memory-efficient alternatives
pip install modin[ray]  # Faster pandas alternative
pip install dask[complete]  # Parallel computing
```

### Solver Performance
```bash
# Install high-performance solvers
pip install ortools  # Google OR-Tools (free)
pip install python-mip  # Mixed Integer Programming
```

## Verification Script

Create and run this verification script:

```python
# verification.py
import sys
import importlib

required_packages = [
    'numpy', 'pandas', 'scipy', 'sklearn', 'pulp', 
    'matplotlib', 'seaborn', 'plotly', 'folium', 
    'geopy', 'requests', 'jupyter'
]

print("🔍 Verifying installation...")
print("=" * 50)

failed_imports = []
for package in required_packages:
    try:
        importlib.import_module(package)
        print(f"✅ {package}")
    except ImportError:
        print(f"❌ {package}")
        failed_imports.append(package)

print("=" * 50)
if not failed_imports:
    print("🎉 All packages installed successfully!")
    print("🚀 Ready to run the optimization system!")
else:
    print(f"⚠️  Failed imports: {', '.join(failed_imports)}")
    print("Please reinstall the missing packages.")

print(f"Python version: {sys.version}")
```

Run with: `python verification.py`

## Next Steps

1. **Launch Jupyter**: `jupyter notebook`
2. **Open**: `Coffe - Stores.ipynb`
3. **Select Kernel**: "Coffee Optimization"
4. **Run**: Execute all cells to start optimization

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Verify Python and package versions
3. Ensure virtual environment is activated
4. Review error messages for specific package issues

**System Status**: ✅ Ready for institutional-grade optimization analysis
