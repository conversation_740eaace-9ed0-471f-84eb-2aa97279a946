#!/usr/bin/env python3
"""
Simple Demo - Advanced Coffee Shop Optimization System
"""

import numpy as np
import pandas as pd
import pulp
from geopy.distance import great_circle

print("🚀 Advanced Coffee Shop Optimization System - Simple Demo")
print("="*60)

# Simple Point class
class Point:
    def __init__(self, name, longitude, latitude, income=50000, traffic=100):
        self.name = name
        self.x = longitude
        self.y = latitude
        self.income = income
        self.traffic = traffic
    
    def __str__(self):
        return self.name
    
    def __hash__(self):
        return hash((self.name, self.x, self.y))

# Sample Chicago libraries with enhanced data
libraries = [
    Point("Harold Washington Library Center", -87.628, 41.876, 65000, 300),
    Point("Lincoln Park", -87.635, 41.928, 85000, 280),
    Point("Logan Square", -87.707, 41.929, 72000, 220),
    Point("Near North", -87.631, 41.903, 95000, 350),
    Point("Uptown", -87.653, 41.966, 52000, 200),
    Point("Chinatown", -87.632, 41.853, 46000, 200),
    Point("Bucktown-Wicker Park", -87.674, 41.913, 75000, 250),
    Point("Edgewater", -87.663, 41.993, 62000, 190),
    Point("Albany Park", -87.71409, 41.975456, 45000, 150),
    Point("Austin", -87.77593, 41.899041, 38000, 120)
]

print(f"📍 Loaded {len(libraries)} library locations")

# Simple financial calculation
def calculate_npv(location):
    """Simple NPV calculation"""
    monthly_customers = location.traffic * 30
    income_multiplier = location.income / 50000
    monthly_revenue = monthly_customers * income_multiplier * 6.50
    monthly_profit = monthly_revenue * 0.65 - 8000
    
    # Simple 5-year NPV
    npv = -150000  # Initial investment
    for year in range(1, 6):
        npv += (monthly_profit * 12) / (1.12 ** year)
    
    return npv

# Calculate distances
def get_distance(p1, p2):
    return great_circle((p1.y, p1.x), (p2.y, p2.x)).miles

print("\n🎯 Running Multi-Objective Optimization...")

# Create optimization model
prob = pulp.LpProblem("Coffee_Shop_Optimization", pulp.LpMaximize)

# Decision variables
shop_vars = {loc: pulp.LpVariable(f"shop_{i}", cat='Binary') 
            for i, loc in enumerate(libraries)}

assignment_vars = {(loc, lib): pulp.LpVariable(f"assign_{i}_{j}", cat='Binary')
                  for i, loc in enumerate(libraries) 
                  for j, lib in enumerate(libraries)}

# Constraints
num_shops = 3

# Each library assigned to exactly one shop
for lib in libraries:
    prob += pulp.lpSum([assignment_vars[(loc, lib)] for loc in libraries]) == 1

# Libraries can only be assigned to open shops
for loc in libraries:
    for lib in libraries:
        prob += assignment_vars[(loc, lib)] <= shop_vars[loc]

# Exactly num_shops shops must be opened
prob += pulp.lpSum([shop_vars[loc] for loc in libraries]) == num_shops

# Multi-objective function
# Minimize distance + Maximize profit
distance_obj = -pulp.lpSum([assignment_vars[(loc, lib)] * get_distance(loc, lib) 
                           for loc in libraries for lib in libraries])

profit_obj = pulp.lpSum([shop_vars[loc] * calculate_npv(loc) for loc in libraries])

# Combined objective (balanced weights)
prob += 0.4 * distance_obj + 0.6 * profit_obj / 100000

print("🔧 Solving optimization problem...")

# Solve
prob.solve(pulp.PULP_CBC_CMD(msg=0))

if prob.status == pulp.LpStatusOptimal:
    print("✅ Optimization successful!")
    
    # Extract solution
    selected_shops = [loc for loc in libraries if shop_vars[loc].varValue == 1]
    assignments = [(loc, lib) for loc in libraries for lib in libraries 
                  if assignment_vars[(loc, lib)].varValue == 1]
    
    total_distance = sum(get_distance(loc, lib) for loc, lib in assignments)
    total_npv = sum(calculate_npv(loc) for loc in selected_shops)
    
    print(f"\n📊 OPTIMIZATION RESULTS:")
    print(f"   • Number of coffee shops: {len(selected_shops)}")
    print(f"   • Total NPV: ${total_npv:,.0f}")
    print(f"   • Total distance: {total_distance:.1f} miles")
    print(f"   • Average distance per library: {total_distance/len(libraries):.2f} miles")
    
    print(f"\n🏪 OPTIMAL COFFEE SHOP LOCATIONS:")
    for i, shop in enumerate(selected_shops, 1):
        npv = calculate_npv(shop)
        roi = (npv / 150000) * 100
        print(f"   {i}. {shop.name}")
        print(f"      • NPV: ${npv:,.0f}")
        print(f"      • ROI: {roi:.1f}%")
        print(f"      • Demographics: ${shop.income:,} income, {shop.traffic} daily traffic")
    
    print(f"\n🎯 STRATEGIC INSIGHTS:")
    total_investment = len(selected_shops) * 150000
    portfolio_roi = (total_npv / total_investment) * 100
    print(f"   • Total investment required: ${total_investment:,.0f}")
    print(f"   • Portfolio ROI: {portfolio_roi:.1f}%")
    print(f"   • Market coverage: {len(libraries)} libraries served")
    
    # Show library assignments
    print(f"\n📚 LIBRARY ASSIGNMENTS:")
    assignment_dict = {}
    for shop, lib in assignments:
        if shop not in assignment_dict:
            assignment_dict[shop] = []
        assignment_dict[shop].append(lib)
    
    for shop in selected_shops:
        assigned_libs = assignment_dict.get(shop, [])
        print(f"\n☕ {shop.name} serves {len(assigned_libs)} libraries:")
        for lib in assigned_libs:
            distance = get_distance(shop, lib)
            print(f"   • {lib.name} ({distance:.2f} miles)")
    
    print(f"\n" + "="*60)
    print("✅ ADVANCED OPTIMIZATION COMPLETE!")
    print("🚀 System successfully optimized coffee shop locations")
    print("💡 Ready for full-scale institutional deployment")
    print("="*60)
    
else:
    print(f"❌ Optimization failed: {pulp.LpStatus[prob.status]}")
