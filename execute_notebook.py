#!/usr/bin/env python3
"""
Execute Advanced Coffee Shop Optimization Notebook
Simulates notebook execution and shows results
"""

import sys
import numpy as np
import pandas as pd
import pulp
from geopy.distance import great_circle
import warnings
warnings.filterwarnings('ignore')

def main():
    print("="*80)
    print("🚀 ADVANCED COFFEE SHOP LOCATION OPTIMIZATION SYSTEM")
    print("📊 Institutional-Grade Multi-Objective Analysis")
    print("="*80)
    
    # Advanced Point class with demographics
    class AdvancedPoint:
        def __init__(self, name, longitude, latitude, 
                     population_density=1000, income_level=50000, 
                     foot_traffic=100, competition_score=0.5):
            self.name = name
            self.x = longitude
            self.y = latitude
            self.population_density = population_density
            self.income_level = income_level
            self.foot_traffic = foot_traffic
            self.competition_score = competition_score
            
        def __str__(self):
            return self.name
        
        def __hash__(self):
            return hash((self.name, self.x, self.y))
    
    # Financial Model
    class FinancialModel:
        def __init__(self):
            self.setup_cost = 150000
            self.monthly_fixed_costs = 8000
            self.avg_transaction = 6.50
            self.gross_margin = 0.65
            self.discount_rate = 0.12
            self.analysis_period = 60
            
        def calculate_monthly_revenue(self, location):
            base_customers = location.foot_traffic * 30
            income_multiplier = min(location.income_level / 50000, 2.0)
            density_multiplier = min(location.population_density / 1000, 1.5)
            competition_penalty = 1 - (location.competition_score * 0.4)
            
            adjusted_customers = (base_customers * income_multiplier * 
                                density_multiplier * competition_penalty)
            
            return adjusted_customers * self.avg_transaction
        
        def calculate_npv(self, location):
            monthly_revenue = self.calculate_monthly_revenue(location)
            monthly_profit = (monthly_revenue * self.gross_margin) - self.monthly_fixed_costs
            
            monthly_rate = self.discount_rate / 12
            npv = -self.setup_cost
            
            for month in range(1, self.analysis_period + 1):
                npv += monthly_profit / ((1 + monthly_rate) ** month)
                
            return npv
        
        def calculate_roi(self, location):
            npv = self.calculate_npv(location)
            return (npv / self.setup_cost) * 100
        
        def calculate_payback_period(self, location):
            monthly_revenue = self.calculate_monthly_revenue(location)
            monthly_profit = (monthly_revenue * self.gross_margin) - self.monthly_fixed_costs
            
            if monthly_profit <= 0:
                return float('inf')
                
            return self.setup_cost / monthly_profit
    
    print("\n💼 Advanced financial modeling framework loaded")
    print("📈 Multi-objective optimization classes initialized")
    
    # Load enhanced library data
    print("\n📡 PHASE 1: Data Loading and Enhancement")
    print("-" * 50)
    
    enhanced_libraries = [
        AdvancedPoint("Albany Park", -87.71409, 41.975456, 2800, 45000, 150, 0.6),
        AdvancedPoint("Harold Washington Library Center", -87.628, 41.876, 4000, 65000, 300, 0.9),
        AdvancedPoint("Lincoln Park", -87.635, 41.928, 3200, 85000, 280, 0.8),
        AdvancedPoint("Logan Square", -87.707, 41.929, 2800, 72000, 220, 0.8),
        AdvancedPoint("Near North", -87.631, 41.903, 3800, 95000, 350, 0.9),
        AdvancedPoint("Uptown", -87.653, 41.966, 2700, 52000, 200, 0.8),
        AdvancedPoint("Chinatown", -87.632, 41.853, 2800, 46000, 200, 0.8),
        AdvancedPoint("Bucktown-Wicker Park", -87.674, 41.913, 3500, 75000, 250, 0.9),
        AdvancedPoint("Edgewater", -87.663, 41.993, 2600, 62000, 190, 0.7),
        AdvancedPoint("Austin", -87.77593, 41.899041, 1200, 38000, 120, 0.7),
        AdvancedPoint("Englewood", -87.644, 41.779, 1100, 28000, 80, 0.5),
        AdvancedPoint("Brighton Park", -87.696, 41.817, 1800, 36000, 110, 0.7),
        AdvancedPoint("Canaryville", -87.652, 41.807, 1400, 44000, 120, 0.6),
        AdvancedPoint("Clearing", -87.762, 41.790, 1200, 48000, 100, 0.4),
        AdvancedPoint("Dunning", -87.806, 41.950, 1500, 52000, 140, 0.5)
    ]
    
    print(f"✅ Loaded {len(enhanced_libraries)} enhanced library locations")
    
    # Initialize optimization components
    financial_model = FinancialModel()
    libraries = enhanced_libraries
    num_shops = 5
    
    print(f"\n🎯 PHASE 2: Multi-Objective Optimization ({num_shops} shops)")
    print("-" * 50)
    
    # Calculate distances
    def get_distance(p1, p2):
        return great_circle((p1.y, p1.x), (p2.y, p2.x)).miles
    
    # Run optimization scenarios
    scenarios = [
        {"name": "Distance-Focused", "weights": (0.6, 0.3, 0.1)},
        {"name": "Profit-Focused", "weights": (0.2, 0.7, 0.1)},
        {"name": "Balanced", "weights": (0.4, 0.4, 0.2)},
        {"name": "Coverage-Focused", "weights": (0.3, 0.2, 0.5)}
    ]
    
    scenario_results = {}
    
    for scenario in scenarios:
        print(f"\n🔍 Analyzing scenario: {scenario['name']}")
        weights = scenario['weights']
        
        # Create optimization model
        prob = pulp.LpProblem(f"Coffee_Optimization_{scenario['name']}", pulp.LpMaximize)
        
        # Decision variables
        shop_vars = {loc: pulp.LpVariable(f"shop_{hash(loc)}", cat='Binary') 
                    for loc in libraries}
        
        assignment_vars = {(loc, lib): pulp.LpVariable(f"assign_{hash(loc)}_{hash(lib)}", cat='Binary')
                          for loc in libraries for lib in libraries}
        
        # Constraints
        for lib in libraries:
            prob += pulp.lpSum([assignment_vars[(loc, lib)] for loc in libraries]) == 1
        
        for loc in libraries:
            for lib in libraries:
                prob += assignment_vars[(loc, lib)] <= shop_vars[loc]
        
        prob += pulp.lpSum([shop_vars[loc] for loc in libraries]) == num_shops
        
        # Multi-objective function
        distance_obj = -pulp.lpSum([assignment_vars[(loc, lib)] * get_distance(loc, lib) 
                                   for loc in libraries for lib in libraries])
        
        profit_obj = pulp.lpSum([shop_vars[loc] * financial_model.calculate_npv(loc) 
                                for loc in libraries])
        
        max_distance_var = pulp.LpVariable("max_distance", lowBound=0)
        for lib in libraries:
            prob += pulp.lpSum([assignment_vars[(loc, lib)] * get_distance(loc, lib) 
                               for loc in libraries]) <= max_distance_var
        coverage_obj = -max_distance_var
        
        # Combined objective
        prob += (weights[0] * distance_obj + 
                weights[1] * profit_obj / 100000 + 
                weights[2] * coverage_obj)
        
        # Solve
        prob.solve(pulp.PULP_CBC_CMD(msg=0))
        
        if prob.status == pulp.LpStatusOptimal:
            selected_shops = [loc for loc in libraries if shop_vars[loc].varValue == 1]
            assignments = [(loc, lib) for loc in libraries for lib in libraries 
                          if assignment_vars[(loc, lib)].varValue == 1]
            
            total_distance = sum(get_distance(loc, lib) for loc, lib in assignments)
            total_npv = sum(financial_model.calculate_npv(loc) for loc in selected_shops)
            max_distance = max(get_distance(loc, lib) for loc, lib in assignments)
            
            scenario_results[scenario['name']] = {
                'selected_shops': selected_shops,
                'assignments': assignments,
                'total_distance': total_distance,
                'total_npv': total_npv,
                'max_distance': max_distance
            }
            
            print(f"   ✅ Total NPV: ${total_npv:,.0f}")
            print(f"   📏 Total Distance: {total_distance:.1f} miles")
            print(f"   📐 Max Distance: {max_distance:.1f} miles")
        else:
            print(f"   ❌ Scenario failed")
    
    # Select best scenario (balanced approach)
    best_scenario = "Balanced"
    if best_scenario in scenario_results:
        best_result = scenario_results[best_scenario]
    else:
        best_scenario = list(scenario_results.keys())[0]
        best_result = scenario_results[best_scenario]
    
    print(f"\n🏆 Selected Scenario: {best_scenario}")
    print(f"📊 Optimal Solution Summary:")
    print(f"   • Total NPV: ${best_result['total_npv']:,.0f}")
    print(f"   • Average ROI: {np.mean([financial_model.calculate_roi(shop) for shop in best_result['selected_shops']]):.1f}%")
    print(f"   • Total Distance: {best_result['total_distance']:.1f} miles")
    print(f"   • Max Distance: {best_result['max_distance']:.1f} miles")
    
    # Generate detailed report
    print(f"\n📋 PHASE 3: Strategic Recommendations")
    print("-" * 50)
    
    print(f"\n🏪 OPTIMAL COFFEE SHOP LOCATIONS:")
    for i, shop in enumerate(best_result['selected_shops'], 1):
        npv = financial_model.calculate_npv(shop)
        roi = financial_model.calculate_roi(shop)
        payback = financial_model.calculate_payback_period(shop)
        
        print(f"\n{i}. {shop.name}")
        print(f"   📍 Location: ({shop.y:.4f}, {shop.x:.4f})")
        print(f"   💰 NPV: ${npv:,.0f}")
        print(f"   📈 ROI: {roi:.1f}%")
        print(f"   ⏱️ Payback: {payback:.1f} months")
        print(f"   🏘️ Demographics: {shop.population_density:,.0f}/sq mi, ${shop.income_level:,.0f} avg income")
        print(f"   🚶 Foot Traffic: {shop.foot_traffic}/day")
        print(f"   🏢 Competition: {shop.competition_score:.2f}/1.0")
    
    print(f"\n🎯 STRATEGIC INSIGHTS:")
    total_investment = len(best_result['selected_shops']) * financial_model.setup_cost
    portfolio_npv = best_result['total_npv']
    portfolio_roi = (portfolio_npv / total_investment) * 100
    
    print(f"   • Total Investment Required: ${total_investment:,.0f}")
    print(f"   • Portfolio NPV: ${portfolio_npv:,.0f}")
    print(f"   • Portfolio ROI: {portfolio_roi:.1f}%")
    print(f"   • Average Service Distance: {best_result['total_distance']/len(libraries):.2f} miles")
    print(f"   • Market Coverage: {len(libraries)} libraries served")
    
    # Show library assignments
    print(f"\n📚 LIBRARY ASSIGNMENTS:")
    assignment_dict = {}
    for shop, lib in best_result['assignments']:
        if shop not in assignment_dict:
            assignment_dict[shop] = []
        assignment_dict[shop].append(lib)
    
    for shop in best_result['selected_shops']:
        assigned_libs = assignment_dict.get(shop, [])
        print(f"\n☕ {shop.name} serves {len(assigned_libs)} libraries:")
        for lib in assigned_libs[:3]:  # Show first 3
            distance = get_distance(shop, lib)
            print(f"   • {lib.name} ({distance:.2f} miles)")
        if len(assigned_libs) > 3:
            print(f"   • ... and {len(assigned_libs) - 3} more")
    
    print(f"\n" + "="*80)
    print(f"✅ OPTIMIZATION COMPLETE - STRATEGIC RECOMMENDATIONS READY")
    print(f"🚀 Advanced system successfully optimized coffee shop locations")
    print(f"💡 Ready for institutional deployment with ${portfolio_npv:,.0f} projected NPV")
    print(f"="*80)
    
    return best_result, scenario_results

if __name__ == "__main__":
    try:
        best_result, all_results = main()
        print(f"\n🎉 EXECUTION SUCCESSFUL!")
        print(f"📊 Generated {len(all_results)} optimization scenarios")
        print(f"💰 Best portfolio NPV: ${best_result['total_npv']:,.0f}")
    except Exception as e:
        print(f"\n❌ Error during execution: {e}")
        sys.exit(1)
