# Advanced Coffee Shop Location Optimization System
# Requirements file for institutional-grade multi-objective optimization

# Core Data Science and Analytics
numpy>=1.24.0
pandas>=2.0.0
scipy>=1.10.0
scikit-learn>=1.3.0

# Optimization and Mathematical Programming
pulp>=2.7.0
cvxpy>=1.3.0

# Visualization and Plotting
matplotlib>=3.7.0
seaborn>=0.12.0
plotly>=5.15.0
folium>=0.14.0

# Geographic and Distance Calculations
geopy>=2.3.0
geopandas>=0.13.0

# Web Requests and Data Fetching
requests>=2.31.0
urllib3>=2.0.0

# Jupyter Notebook Support
jupyter>=1.0.0
ipython>=8.14.0
notebook>=6.5.0
ipywidgets>=8.0.0

# Statistical Analysis and Monte Carlo
statsmodels>=0.14.0
pymc>=5.6.0

# Performance and Parallel Computing
numba>=0.57.0
joblib>=1.3.0

# Data Validation and Quality
pydantic>=2.0.0
marshmallow>=3.20.0

# Financial Calculations
numpy-financial>=1.0.0
quantlib-python>=1.31.0

# Additional Utilities
tqdm>=4.65.0
colorama>=0.4.6
rich>=13.4.0

# Development and Testing (Optional)
pytest>=7.4.0
black>=23.7.0
flake8>=6.0.0
mypy>=1.5.0

# Documentation (Optional)
sphinx>=7.1.0
sphinx-rtd-theme>=1.3.0

# Export and Reporting
openpyxl>=3.1.0
xlsxwriter>=3.1.0
reportlab>=4.0.0

# Memory and Performance Monitoring
psutil>=5.9.0
memory-profiler>=0.61.0

# Configuration Management
pyyaml>=6.0.0
python-dotenv>=1.0.0

# Logging and Debugging
loguru>=0.7.0

# Version Compatibility Notes:
# - Python 3.8+ required for optimal performance
# - Some packages may require specific system dependencies
# - For Windows users: Visual C++ Build Tools may be needed for some packages
# - For macOS users: Xcode Command Line Tools recommended
# - For Linux users: Standard development packages (gcc, g++, python3-dev)

# Installation Instructions:
# 1. Create virtual environment: python -m venv venv
# 2. Activate environment: 
#    - Windows: venv\Scripts\activate
#    - macOS/Linux: source venv/bin/activate
# 3. Install requirements: pip install -r requirements.txt
# 4. For Jupyter support: python -m ipykernel install --user --name=coffee-optimization

# Optional GPU Acceleration (for large-scale optimization):
# cupy-cuda11x>=12.0.0  # Uncomment if CUDA 11.x available
# cupy-cuda12x>=12.0.0  # Uncomment if CUDA 12.x available

# Alternative Solvers (Optional - for advanced optimization):
# gurobipy>=10.0.0      # Commercial solver (license required)
# cplex>=22.1.0         # IBM CPLEX (license required)
# ortools>=9.7.0        # Google OR-Tools (free alternative)

# Web Dashboard (Optional - for interactive visualization):
# streamlit>=1.25.0
# dash>=2.12.0
# bokeh>=3.2.0

# Database Connectivity (Optional):
# sqlalchemy>=2.0.0
# psycopg2-binary>=2.9.0
# pymongo>=4.4.0

# Cloud Integration (Optional):
# boto3>=1.28.0         # AWS
# google-cloud-storage>=2.10.0  # Google Cloud
# azure-storage-blob>=12.17.0   # Azure

# Machine Learning Extensions (Optional):
# xgboost>=1.7.0
# lightgbm>=4.0.0
# catboost>=1.2.0
