#!/usr/bin/env python3
"""
Advanced Coffee Shop Optimization System - Installation Verification
Verifies all required packages are installed and working correctly
"""

import sys
import importlib
from datetime import datetime

def test_import(package_name, alias=None):
    """Test importing a package and return status"""
    try:
        if alias:
            module = importlib.import_module(package_name)
            globals()[alias] = module
        else:
            importlib.import_module(package_name)
        return True, None
    except ImportError as e:
        return False, str(e)

def main():
    print("=" * 80)
    print("🚀 ADVANCED COFFEE SHOP OPTIMIZATION SYSTEM")
    print("📋 Installation Verification Report")
    print("=" * 80)
    print(f"📅 Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🐍 Python Version: {sys.version}")
    print("=" * 80)

    # Core packages to test
    packages = [
        ("numpy", "np"),
        ("pandas", "pd"),
        ("scipy", None),
        ("sklearn", None),
        ("pulp", None),
        ("matplotlib.pyplot", "plt"),
        ("seaborn", "sns"),
        ("plotly.express", "px"),
        ("plotly.graph_objects", "go"),
        ("folium", None),
        ("geopy.distance", None),
        ("requests", None),
        ("jupyter", None),
        ("statsmodels", None),
        ("numba", None),
        ("joblib", None),
        ("tqdm", None),
        ("yaml", None)
    ]

    print("\n📦 PACKAGE VERIFICATION:")
    print("-" * 50)

    success_count = 0
    failed_packages = []

    for package, alias in packages:
        success, error = test_import(package, alias)
        if success:
            print(f"✅ {package:<25} - OK")
            success_count += 1
        else:
            print(f"❌ {package:<25} - FAILED: {error}")
            failed_packages.append(package)

    print("-" * 50)
    print(f"📊 Results: {success_count}/{len(packages)} packages successfully imported")

    if failed_packages:
        print(f"\n⚠️  Failed packages: {', '.join(failed_packages)}")
        print("💡 Run: pip install -r requirements_core.txt")

    # Test core functionality
    print("\n🔧 FUNCTIONALITY TESTS:")
    print("-" * 50)

    try:
        # Test NumPy
        import numpy as np
        arr = np.array([1, 2, 3])
        print(f"✅ NumPy array creation: {arr}")

        # Test Pandas
        import pandas as pd
        df = pd.DataFrame({'A': [1, 2], 'B': [3, 4]})
        print(f"✅ Pandas DataFrame: {df.shape}")

        # Test PuLP
        import pulp
        prob = pulp.LpProblem("test", pulp.LpMinimize)
        print(f"✅ PuLP optimization problem created")

        # Test Plotly
        import plotly.graph_objects as go
        fig = go.Figure()
        print(f"✅ Plotly figure created")

        # Test Folium
        import folium
        m = folium.Map()
        print(f"✅ Folium map created")

        print("✅ All functionality tests passed!")

    except Exception as e:
        print(f"❌ Functionality test failed: {e}")

    # Version information
    print("\n📋 VERSION INFORMATION:")
    print("-" * 50)

    version_packages = [
        ("numpy", "np"),
        ("pandas", "pd"),
        ("pulp", None),
        ("plotly", None),
        ("folium", None)
    ]

    for package, alias in version_packages:
        try:
            if alias:
                module = importlib.import_module(package)
                version = getattr(module, '__version__', 'Unknown')
            else:
                module = importlib.import_module(package)
                version = getattr(module, '__version__', 'Unknown')
            print(f"📦 {package:<15}: {version}")
        except:
            print(f"📦 {package:<15}: Not available")

    # System readiness
    print("\n🎯 SYSTEM READINESS:")
    print("-" * 50)

    if success_count == len(packages):
        print("🟢 READY: All packages installed and working")
        print("🚀 You can now run the Advanced Coffee Shop Optimization System!")
        print("💡 Next steps:")
        print("   1. Open Jupyter: jupyter notebook")
        print("   2. Open: Coffe - Stores.ipynb")
        print("   3. Run all cells to start optimization")
        return True
    else:
        print("🟡 PARTIAL: Some packages missing or not working")
        print("🔧 Please install missing packages before proceeding")
        return False

if __name__ == "__main__":
    success = main()
    print("\n" + "=" * 80)
    if success:
        print("✅ VERIFICATION COMPLETE - SYSTEM READY FOR OPTIMIZATION")
    else:
        print("⚠️  VERIFICATION INCOMPLETE - PLEASE RESOLVE ISSUES")
    print("=" * 80)
